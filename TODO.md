# TODO: Project wrap-up before retirement

## Monetization Setup

- [x] Acquire Google AdSense Publisher ID (`ca-pub-...`) and Ad Slot IDs
- [x] Configure AdSense env vars in `.env.local` (VITE*ADSENSE_CLIENT_ID, VITE_ADSENSE_SLOT*\*)
- [x] Verify AdSense script loads and `<ins class="adsbygoogle">` tags render
- [x] Insert `<GoogleAd>` slots in header, footer, content, and story-end pages
- [x] Remove all references to `lovable.dev` across code & docs
- [x] Remove all Firebase dependencies and migrate MFA to Supabase Auth

## Subscription & Payment Flow

- [x] Integrate Stripe or Paddle for ad-free subscriptions
- [x] Implement API/backend endpoint to toggle ad-free status
- [x] Test ad-free subscription purchase and entitlement

## Documentation

- [ ] Update README with monetization & subscription instructions
- [ ] Finalize this `TODO.md` and handoff docs
- [ ] Remove remaining placeholder images & text

## QA & Testing

- [ ] Write unit tests for `AdsContext` & `GoogleAd` component
- [ ] Perform cross-browser testing of ad slots
- [ ] Run performance audit for ad loading impact

## Deployment

- [ ] Set AdSense env vars in production (Vercel/Netlify)
- [ ] Deploy latest build and smoke-test live ads

## Handoff & Archive

- [ ] Document environment setup and access credentials
- [ ] Schedule handoff session with new maintainer
- [ ] Archive repository, config, and credentials securely
