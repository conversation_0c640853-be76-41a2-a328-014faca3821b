import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

console.log(`Function 'complete-story' up and running!`);

serve(async (req: Request) => {
  // This is needed if you're planning to invoke your function from a browser.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '', // Use service role key for admin-level operations like updating any story
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return new Response(JSON.stringify({ error: 'User not authenticated' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      });
    }

    const { story_id } = await req.json();
    if (!story_id) {
      return new Response(JSON.stringify({ error: 'Missing story_id in request body' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Fetch the story to check authorship and current status
    const { data: story, error: fetchError } = await supabaseClient
      .from('stories')
      .select('author_id, status')
      .eq('id', story_id)
      .single();

    if (fetchError) {
      console.error('Error fetching story:', fetchError);
      return new Response(JSON.stringify({ error: 'Failed to fetch story', details: fetchError.message }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    if (!story) {
      return new Response(JSON.stringify({ error: 'Story not found' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    if (story.author_id !== user.id) {
      return new Response(JSON.stringify({ error: 'Only the story author can mark it as complete' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403, // Forbidden
      });
    }

    if (story.status === 'completed') {
      // Story is already complete, return current story data or a specific message
      // Fetching full story data again to return it, as we only selected author_id and status initially
      const { data: alreadyCompletedStory, error: alreadyCompletedError } = await supabaseClient
        .from('stories')
        .select('*, author:author_id(id, username, avatar_url), contributions(*, author:user_id(id, username, avatar_url))')
        .eq('id', story_id)
        .single();
      
      if (alreadyCompletedError) {
         return new Response(JSON.stringify({ error: 'Failed to fetch already completed story', details: alreadyCompletedError.message }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        });
      }
      return new Response(JSON.stringify({ message: 'Story is already complete', story: alreadyCompletedStory }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200, // Or a different status like 208 Already Reported
      });
    }

    // Update the story status and completed_at timestamp
    const { data: updatedStory, error: updateError } = await supabaseClient
      .from('stories')
      .update({ status: 'completed', completed_at: new Date().toISOString() })
      .eq('id', story_id)
      .select('*, author:author_id(id, username, avatar_url), contributions(*, author:user_id(id, username, avatar_url))') // Select all fields of the updated story, including author and contributions
      .single();

    if (updateError) {
      console.error('Error updating story:', updateError);
      return new Response(JSON.stringify({ error: 'Failed to complete story', details: updateError.message }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    return new Response(JSON.stringify(updatedStory), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (e: any) {
    console.error('Unexpected error in complete-story function:', e);
    return new Response(JSON.stringify({ error: 'Internal server error', details: e.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 