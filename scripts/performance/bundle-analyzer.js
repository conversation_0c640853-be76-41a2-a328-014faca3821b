#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// Bundle size thresholds (in KB)
const THRESHOLDS = {
  mainBundle: 500,     // Main JavaScript bundle
  cssBundle: 100,      // CSS bundle
  vendorBundle: 1000,  // Vendor dependencies
  totalBundle: 1500,   // Total bundle size
  chunkSize: 200       // Individual chunk size
};

// File extensions to analyze
const EXTENSIONS = {
  js: ['.js', '.jsx', '.ts', '.tsx'],
  css: ['.css', '.scss', '.sass'],
  assets: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2']
};

async function getDirectorySize(dirPath) {
  try {
    const files = await fs.readdir(dirPath, { withFileTypes: true });
    let totalSize = 0;
    
    for (const file of files) {
      const filePath = path.join(dirPath, file.name);
      
      if (file.isDirectory()) {
        totalSize += await getDirectorySize(filePath);
      } else {
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
      }
    }
    
    return totalSize;
  } catch (error) {
    return 0;
  }
}

async function analyzeFile(filePath) {
  try {
    const stats = await fs.stat(filePath);
    const ext = path.extname(filePath);
    const name = path.basename(filePath);
    
    return {
      name,
      path: filePath,
      size: stats.size,
      sizeKB: Math.round(stats.size / 1024),
      extension: ext,
      type: getFileType(ext)
    };
  } catch (error) {
    return null;
  }
}

function getFileType(extension) {
  if (EXTENSIONS.js.includes(extension)) return 'javascript';
  if (EXTENSIONS.css.includes(extension)) return 'css';
  if (EXTENSIONS.assets.includes(extension)) return 'assets';
  return 'other';
}

async function analyzeBuildDirectory(buildPath) {
  try {
    const files = await fs.readdir(buildPath, { withFileTypes: true });
    const analysis = {
      javascript: [],
      css: [],
      assets: [],
      other: [],
      totalSize: 0,
      summary: {
        javascript: { count: 0, size: 0 },
        css: { count: 0, size: 0 },
        assets: { count: 0, size: 0 },
        other: { count: 0, size: 0 }
      }
    };

    for (const file of files) {
      const filePath = path.join(buildPath, file.name);
      
      if (file.isDirectory()) {
        // Recursively analyze subdirectories
        const subAnalysis = await analyzeBuildDirectory(filePath);
        
        // Merge results
        analysis.javascript.push(...subAnalysis.javascript);
        analysis.css.push(...subAnalysis.css);
        analysis.assets.push(...subAnalysis.assets);
        analysis.other.push(...subAnalysis.other);
        analysis.totalSize += subAnalysis.totalSize;
      } else {
        const fileAnalysis = await analyzeFile(filePath);
        if (fileAnalysis) {
          analysis[fileAnalysis.type].push(fileAnalysis);
          analysis.totalSize += fileAnalysis.size;
          analysis.summary[fileAnalysis.type].count++;
          analysis.summary[fileAnalysis.type].size += fileAnalysis.size;
        }
      }
    }

    return analysis;
  } catch (error) {
    console.error('Error analyzing build directory:', error);
    return null;
  }
}

function identifyMainBundles(jsFiles) {
  const bundles = {
    main: null,
    vendor: null,
    chunks: []
  };

  // Sort by size (largest first)
  const sortedFiles = [...jsFiles].sort((a, b) => b.size - a.size);

  // Identify main bundle (usually the largest or contains 'main')
  bundles.main = sortedFiles.find(file => 
    file.name.includes('main') || file.name.includes('index')
  ) || sortedFiles[0];

  // Identify vendor bundle (usually contains 'vendor' or 'chunk')
  bundles.vendor = sortedFiles.find(file => 
    file.name.includes('vendor') || file.name.includes('chunk')
  );

  // Remaining files are chunks
  bundles.chunks = sortedFiles.filter(file => 
    file !== bundles.main && file !== bundles.vendor
  );

  return bundles;
}

function checkThresholds(analysis) {
  const issues = [];
  const bundles = identifyMainBundles(analysis.javascript);
  
  // Check main bundle size
  if (bundles.main && bundles.main.sizeKB > THRESHOLDS.mainBundle) {
    issues.push({
      type: 'warning',
      category: 'Main Bundle',
      message: `Main bundle (${bundles.main.name}) is ${bundles.main.sizeKB}KB, exceeds threshold of ${THRESHOLDS.mainBundle}KB`,
      file: bundles.main.name,
      actual: bundles.main.sizeKB,
      threshold: THRESHOLDS.mainBundle
    });
  }

  // Check vendor bundle size
  if (bundles.vendor && bundles.vendor.sizeKB > THRESHOLDS.vendorBundle) {
    issues.push({
      type: 'warning',
      category: 'Vendor Bundle',
      message: `Vendor bundle (${bundles.vendor.name}) is ${bundles.vendor.sizeKB}KB, exceeds threshold of ${THRESHOLDS.vendorBundle}KB`,
      file: bundles.vendor.name,
      actual: bundles.vendor.sizeKB,
      threshold: THRESHOLDS.vendorBundle
    });
  }

  // Check individual chunk sizes
  bundles.chunks.forEach(chunk => {
    if (chunk.sizeKB > THRESHOLDS.chunkSize) {
      issues.push({
        type: 'info',
        category: 'Chunk Size',
        message: `Chunk (${chunk.name}) is ${chunk.sizeKB}KB, consider code splitting`,
        file: chunk.name,
        actual: chunk.sizeKB,
        threshold: THRESHOLDS.chunkSize
      });
    }
  });

  // Check CSS bundle size
  const totalCssSize = Math.round(analysis.summary.css.size / 1024);
  if (totalCssSize > THRESHOLDS.cssBundle) {
    issues.push({
      type: 'warning',
      category: 'CSS Bundle',
      message: `Total CSS size is ${totalCssSize}KB, exceeds threshold of ${THRESHOLDS.cssBundle}KB`,
      actual: totalCssSize,
      threshold: THRESHOLDS.cssBundle
    });
  }

  // Check total bundle size
  const totalSize = Math.round(analysis.totalSize / 1024);
  if (totalSize > THRESHOLDS.totalBundle) {
    issues.push({
      type: 'error',
      category: 'Total Bundle',
      message: `Total bundle size is ${totalSize}KB, exceeds threshold of ${THRESHOLDS.totalBundle}KB`,
      actual: totalSize,
      threshold: THRESHOLDS.totalBundle
    });
  }

  return issues;
}

function generateOptimizationSuggestions(analysis, issues) {
  const suggestions = [];

  // Analyze large JavaScript files
  const largeJsFiles = analysis.javascript
    .filter(file => file.sizeKB > 100)
    .sort((a, b) => b.size - a.size);

  if (largeJsFiles.length > 0) {
    suggestions.push({
      category: 'Code Splitting',
      priority: 'high',
      description: 'Consider implementing code splitting for large JavaScript files',
      files: largeJsFiles.map(f => f.name),
      impact: 'High - Can significantly reduce initial bundle size'
    });
  }

  // Check for potential duplicate dependencies
  const jsFileNames = analysis.javascript.map(f => f.name.toLowerCase());
  const possibleDuplicates = jsFileNames.filter((name, index) => 
    jsFileNames.indexOf(name) !== index
  );

  if (possibleDuplicates.length > 0) {
    suggestions.push({
      category: 'Bundle Optimization',
      priority: 'medium',
      description: 'Potential duplicate files detected',
      files: possibleDuplicates,
      impact: 'Medium - May indicate duplicate dependencies'
    });
  }

  // Analyze asset sizes
  const largeAssets = analysis.assets
    .filter(file => file.sizeKB > 500)
    .sort((a, b) => b.size - a.size);

  if (largeAssets.length > 0) {
    suggestions.push({
      category: 'Asset Optimization',
      priority: 'medium',
      description: 'Large assets detected that could be optimized',
      files: largeAssets.map(f => `${f.name} (${f.sizeKB}KB)`),
      impact: 'Medium - Can improve load times for asset-heavy pages'
    });
  }

  // CSS optimization
  if (analysis.css.length > 5) {
    suggestions.push({
      category: 'CSS Optimization',
      priority: 'low',
      description: 'Multiple CSS files detected, consider bundling',
      files: analysis.css.map(f => f.name),
      impact: 'Low - Can reduce number of requests'
    });
  }

  return suggestions;
}

function generateReport(analysis, issues, suggestions) {
  let report = '# Bundle Analysis Report\\n\\n';
  report += `Generated on: ${new Date().toISOString()}\\n\\n`;

  // Summary
  report += '## Bundle Summary\\n\\n';
  report += `| Type | Files | Total Size | Average Size |\\n`;
  report += `|------|-------|------------|--------------|\\n`;
  
  Object.entries(analysis.summary).forEach(([type, data]) => {
    const totalKB = Math.round(data.size / 1024);
    const avgKB = data.count > 0 ? Math.round(totalKB / data.count) : 0;
    report += `| ${type.charAt(0).toUpperCase() + type.slice(1)} | ${data.count} | ${totalKB}KB | ${avgKB}KB |\\n`;
  });

  const totalSizeKB = Math.round(analysis.totalSize / 1024);
  report += `| **Total** | ${Object.values(analysis.summary).reduce((sum, data) => sum + data.count, 0)} | **${totalSizeKB}KB** | - |\\n\\n`;

  // Issues
  if (issues.length > 0) {
    report += '## Issues Found\\n\\n';
    issues.forEach(issue => {
      const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
      report += `${icon} **${issue.category}**: ${issue.message}\\n\\n`;
    });
  } else {
    report += '## Issues Found\\n\\n✅ No issues found - all bundles are within recommended thresholds.\\n\\n';
  }

  // Optimization suggestions
  if (suggestions.length > 0) {
    report += '## Optimization Suggestions\\n\\n';
    suggestions.forEach(suggestion => {
      const priorityIcon = suggestion.priority === 'high' ? '🔴' : 
                          suggestion.priority === 'medium' ? '🟡' : '🟢';
      report += `${priorityIcon} **${suggestion.category}** (${suggestion.priority} priority)\\n`;
      report += `${suggestion.description}\\n`;
      report += `**Impact**: ${suggestion.impact}\\n`;
      if (suggestion.files.length > 0) {
        report += `**Files**: ${suggestion.files.join(', ')}\\n`;
      }
      report += '\\n';
    });
  }

  // Detailed breakdown
  report += '## Detailed File Analysis\\n\\n';
  
  ['javascript', 'css', 'assets'].forEach(type => {
    if (analysis[type].length > 0) {
      report += `### ${type.charAt(0).toUpperCase() + type.slice(1)} Files\\n\\n`;
      report += '| File | Size | Path |\\n';
      report += '|------|------|------|\\n';
      
      analysis[type]
        .sort((a, b) => b.size - a.size)
        .forEach(file => {
          report += `| ${file.name} | ${file.sizeKB}KB | ${file.path} |\\n`;
        });
      
      report += '\\n';
    }
  });

  return report;
}

async function saveReport(analysis, issues, suggestions) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputDir = path.join(__dirname, '../../reports/performance');
  
  try {
    await fs.mkdir(outputDir, { recursive: true });
  } catch (error) {
    // Directory already exists
  }

  // Save JSON data
  const data = { analysis, issues, suggestions, timestamp: new Date().toISOString() };
  const jsonFilename = `bundle-analysis-${timestamp}.json`;
  await fs.writeFile(path.join(outputDir, jsonFilename), JSON.stringify(data, null, 2));

  // Save markdown report
  const report = generateReport(analysis, issues, suggestions);
  const mdFilename = `bundle-report-${timestamp}.md`;
  await fs.writeFile(path.join(outputDir, mdFilename), report);

  console.log(`\\n📊 Bundle analysis saved to:`);
  console.log(`- reports/performance/${jsonFilename}`);
  console.log(`- reports/performance/${mdFilename}`);
}

async function main() {
  console.log('📦 Starting Bundle Analysis...\\n');
  
  const buildPath = path.join(__dirname, '../../dist');
  
  try {
    // Check if build directory exists
    await fs.access(buildPath);
  } catch (error) {
    console.error('❌ Build directory not found. Run `npm run build` first.');
    process.exit(1);
  }

  const analysis = await analyzeBuildDirectory(buildPath);
  if (!analysis) {
    console.error('❌ Failed to analyze build directory');
    process.exit(1);
  }

  const issues = checkThresholds(analysis);
  const suggestions = generateOptimizationSuggestions(analysis, issues);

  // Print summary
  console.log('📊 Bundle Analysis Summary:');
  console.log(`Total Size: ${Math.round(analysis.totalSize / 1024)}KB`);
  console.log(`JavaScript: ${analysis.javascript.length} files (${Math.round(analysis.summary.javascript.size / 1024)}KB)`);
  console.log(`CSS: ${analysis.css.length} files (${Math.round(analysis.summary.css.size / 1024)}KB)`);
  console.log(`Assets: ${analysis.assets.length} files (${Math.round(analysis.summary.assets.size / 1024)}KB)`);

  if (issues.length > 0) {
    console.log(`\\n⚠️ Found ${issues.length} issues:`);
    issues.forEach(issue => {
      const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${icon} ${issue.message}`);
    });
  } else {
    console.log('\\n✅ All bundles are within recommended thresholds');
  }

  if (suggestions.length > 0) {
    console.log(`\\n💡 ${suggestions.length} optimization suggestions available`);
  }

  await saveReport(analysis, issues, suggestions);

  // Exit with error code if there are critical issues
  const criticalIssues = issues.filter(issue => issue.type === 'error');
  if (criticalIssues.length > 0) {
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { analyzeBuildDirectory, checkThresholds, generateOptimizationSuggestions };