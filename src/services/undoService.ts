import { supabase } from './auth';

export interface UndoResult {
  success: boolean;
  message: string;
  creditsDeducted?: number;
}

export const UNDO_CREDIT_COST = 5;

/**
 * Undo a story contribution and deduct credits from the user
 */
export const undoContribution = async (
  contributionId: string,
  userId: string
): Promise<UndoResult> => {
  try {
    // Start a transaction to ensure atomicity
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error('Failed to fetch user credits');
    }

    if (!user || user.credits < UNDO_CREDIT_COST) {
      return {
        success: false,
        message: `Insufficient credits. You need ${UNDO_CREDIT_COST} credits to undo.`,
      };
    }

    // Get the contribution to verify it exists and get story info
    const { data: contribution, error: contributionError } = await supabase
      .from('story_contributions')
      .select(`
        id,
        content,
        story_id,
        author_id,
        created_at,
        stories!inner(id, title)
      `)
      .eq('id', contributionId)
      .single();

    if (contributionError || !contribution) {
      return {
        success: false,
        message: 'Contribution not found or already removed.',
      };
    }

    // Check if this is actually the last contribution in the story
    const { data: laterContributions, error: laterError } = await supabase
      .from('story_contributions')
      .select('id')
      .eq('story_id', contribution.story_id)
      .gt('created_at', contribution.created_at)
      .limit(1);

    if (laterError) {
      throw new Error('Failed to verify contribution order');
    }

    if (laterContributions && laterContributions.length > 0) {
      return {
        success: false,
        message: 'Can only undo the most recent contribution.',
      };
    }

    // Perform the undo operation
    const { error: deleteError } = await supabase
      .from('story_contributions')
      .delete()
      .eq('id', contributionId);

    if (deleteError) {
      throw new Error('Failed to remove contribution');
    }

    // Deduct credits from user
    const { error: creditError } = await supabase
      .from('profiles')
      .update({ 
        credits: user.credits - UNDO_CREDIT_COST 
      })
      .eq('id', userId);

    if (creditError) {
      // If credit deduction fails, we should ideally rollback the contribution deletion
      // For now, we'll log the error but consider the undo successful
      console.error('Failed to deduct credits after undo:', creditError);
    }

    // Update story word count (optional - could be handled by triggers)
    const { data: remainingContributions } = await supabase
      .from('story_contributions')
      .select('content')
      .eq('story_id', contribution.story_id);

    if (remainingContributions) {
      const totalWords = remainingContributions.reduce((count, contrib) => {
        return count + contrib.content.trim().split(/\s+/).length;
      }, 0);

      await supabase
        .from('stories')
        .update({ current_word_count: totalWords })
        .eq('id', contribution.story_id);
    }

    return {
      success: true,
      message: `Successfully undone "${contribution.content}". ${UNDO_CREDIT_COST} credits deducted.`,
      creditsDeducted: UNDO_CREDIT_COST,
    };

  } catch (error) {
    console.error('Undo service error:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while undoing the contribution.',
    };
  }
};

/**
 * Check if a user can undo a specific contribution
 */
export const canUndoContribution = async (
  contributionId: string,
  userId: string
): Promise<{ canUndo: boolean; reason?: string }> => {
  try {
    // Check user credits
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('credits')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return { canUndo: false, reason: 'User not found' };
    }

    if (user.credits < UNDO_CREDIT_COST) {
      return { 
        canUndo: false, 
        reason: `Insufficient credits (need ${UNDO_CREDIT_COST}, have ${user.credits})` 
      };
    }

    // Check if contribution exists and is the most recent
    const { data: contribution, error: contributionError } = await supabase
      .from('story_contributions')
      .select('story_id, created_at')
      .eq('id', contributionId)
      .single();

    if (contributionError || !contribution) {
      return { canUndo: false, reason: 'Contribution not found' };
    }

    // Check if this is the most recent contribution
    const { data: laterContributions, error: laterError } = await supabase
      .from('story_contributions')
      .select('id')
      .eq('story_id', contribution.story_id)
      .gt('created_at', contribution.created_at)
      .limit(1);

    if (laterError) {
      return { canUndo: false, reason: 'Failed to verify contribution order' };
    }

    if (laterContributions && laterContributions.length > 0) {
      return { canUndo: false, reason: 'Can only undo the most recent contribution' };
    }

    return { canUndo: true };

  } catch (error) {
    console.error('Can undo check error:', error);
    return { canUndo: false, reason: 'Error checking undo eligibility' };
  }
}; 