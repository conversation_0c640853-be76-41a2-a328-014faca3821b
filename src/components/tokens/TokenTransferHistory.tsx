import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { transferService, TokenTransferHistory as TransferHistoryType } from '@/services/transferService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowUpRight, 
  ArrowDownLeft, 
  RefreshCw, 
  Calendar,
  User,
  MessageSquare,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface TokenTransferHistoryProps {
  onTransferSelect?: (transferId: string) => void;
  limit?: number;
}

interface TransferStats {
  totalSent: number;
  totalReceived: number;
  transferCount: number;
  todayTransferred: number;
  remainingDailyLimit: number;
}

export const TokenTransferHistory: React.FC<TokenTransferHistoryProps> = ({
  onTransferSelect,
  limit = 50
}) => {
  const { user } = useAuth();
  const [transfers, setTransfers] = useState<TransferHistoryType[]>([]);
  const [stats, setStats] = useState<TransferStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Load transfer history
  const loadTransferHistory = useCallback(async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError('');

    try {
      const [historyData, statsData] = await Promise.all([
        transferService.getUserTransferHistory(user.id, limit, 0),
        transferService.getTransferStats(user.id)
      ]);

      setTransfers(historyData);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading transfer history:', err);
      setError('Failed to load transfer history');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, limit]);

  // Load data on mount
  useEffect(() => {
    loadTransferHistory();
  }, [loadTransferHistory]);

  // Get status badge for transfer
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="secondary" className="text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary" className="text-yellow-700 bg-yellow-100 dark:text-yellow-300 dark:bg-yellow-900">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Failed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline">
            <AlertCircle className="w-3 h-3 mr-1" />
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get transfer direction icon and color
  const getTransferIcon = (isSender: boolean) => {
    if (isSender) {
      return <ArrowUpRight className="w-4 h-4 text-red-500" />;
    } else {
      return <ArrowDownLeft className="w-4 h-4 text-green-500" />;
    }
  };

  // Format transfer amount with direction
  const formatTransferAmount = (transfer: TransferHistoryType) => {
    const prefix = transfer.isSender ? '-' : '+';
    const colorClass = transfer.isSender ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400';
    
    return (
      <span className={cn('font-medium', colorClass)}>
        {prefix}{transfer.amount}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Transfer Stats */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-600" />
                <div>
                  <div className="text-sm font-medium">{stats.totalReceived}</div>
                  <div className="text-xs text-muted-foreground">Received</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-red-600" />
                <div>
                  <div className="text-sm font-medium">{stats.totalSent}</div>
                  <div className="text-xs text-muted-foreground">Sent</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-blue-600" />
                <div>
                  <div className="text-sm font-medium">{stats.transferCount}</div>
                  <div className="text-xs text-muted-foreground">Total</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-amber-600" />
                <div>
                  <div className="text-sm font-medium">{stats.remainingDailyLimit}</div>
                  <div className="text-xs text-muted-foreground">Daily Left</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Transfer History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Transfer History
              </CardTitle>
              <CardDescription>
                Your token transfer activity
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadTransferHistory}
              disabled={isLoading}
            >
              <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
              Refresh
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="w-8 h-8 mx-auto mb-2" />
              <p>{error}</p>
              <Button variant="outline" onClick={loadTransferHistory} className="mt-2">
                Try Again
              </Button>
            </div>
          )}

          {isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              <RefreshCw className="w-8 h-8 mx-auto mb-2 animate-spin" />
              <p>Loading transfer history...</p>
            </div>
          )}

          {!isLoading && !error && transfers.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="w-8 h-8 mx-auto mb-2" />
              <p>No transfers yet</p>
              <p className="text-sm">Your transfer history will appear here</p>
            </div>
          )}

          {!isLoading && !error && transfers.length > 0 && (
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {transfers.map((transfer, index) => (
                  <div key={transfer.transferId}>
                    <div 
                      className={cn(
                        'flex items-center gap-4 p-4 rounded-lg transition-colors',
                        onTransferSelect && 'hover:bg-muted cursor-pointer'
                      )}
                      onClick={() => onTransferSelect?.(transfer.transferId)}
                    >
                      {/* Transfer Direction Icon */}
                      <div className="flex-shrink-0">
                        {getTransferIcon(transfer.isSender)}
                      </div>

                      {/* Other User Info */}
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={transfer.otherAvatarUrl} />
                          <AvatarFallback>
                            {transfer.otherDisplayName?.[0] || transfer.otherUsername[0]?.toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {transfer.isSender ? 'To' : 'From'} @{transfer.otherUsername}
                            </span>
                            {getStatusBadge(transfer.status)}
                          </div>
                          
                          {transfer.otherDisplayName && (
                            <div className="text-xs text-muted-foreground truncate">
                              {transfer.otherDisplayName}
                            </div>
                          )}
                          
                          {transfer.transferMessage && (
                            <div className="flex items-center gap-1 mt-1">
                              <MessageSquare className="w-3 h-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground truncate">
                                {transfer.transferMessage}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Amount and Time */}
                      <div className="text-right flex-shrink-0">
                        <div className="text-sm">
                          {formatTransferAmount(transfer)} tokens
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(transfer.createdAt), { addSuffix: true })}
                        </div>
                      </div>
                    </div>

                    {index < transfers.length - 1 && <Separator />}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
};