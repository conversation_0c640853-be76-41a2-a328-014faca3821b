[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toast](../README.md) / ToastAction

# Variable: ToastAction

> `const` **ToastAction**: `ForwardRefExoticComponent`\<`Omit`\<`ToastActionProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/toast.tsx:56](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toast.tsx#L56)
