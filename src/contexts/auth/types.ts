import { BadgeTier } from "@/components/ui/achievement-badge";
import { Session } from "@supabase/supabase-js";

// Define the types for system maintenance settings
export interface SystemMaintenanceState {
  enabled: boolean;
  message: string;
  allowAdmins: boolean;
  kickOutUsers: boolean; // Changed from kickExistingUsers to match existing code
}

// Define the structure of the user object with all required properties
export interface User {
  id: string;
  username: string;
  email: string;
  profilePicture?: string;
  tier: "free" | "wordsmith" | "storyteller" | "authors-guild";
  credits: number;
  createdAt: string;
  updatedAt: string;
  termsAccepted?: boolean;
  isAdmin?: boolean;
  achievements?: {
    storiesCreated: number;
    storiesParticipated: number;
    wordsContributed: number;
    votesReceived: number;
    badges: BadgeTier[];
  };
}

// Define the context type with all required methods
export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: Error | null;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, username: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  loadUserProfile: (userId: string) => Promise<void>;
  handleAuthStateChange: (user: any) => Promise<void>;
  handleError: (error: AuthError) => void;
  session: Session | null;
  systemMaintenance: SystemMaintenanceState;
  setSystemMaintenance: (settings: SystemMaintenanceState) => Promise<void>;
  isAdmin: () => boolean;
  login: (
    email: string,
    password: string,
    rememberMe?: boolean,
  ) => Promise<any>;
  loginWithGoogle: () => Promise<any>;
  logout: () => Promise<void>;
  register: (
    username: string,
    email: string,
    password: string,
  ) => Promise<boolean>;
  sendOTP: (target: string, type: "email" | "phone") => Promise<boolean>;
  verifyOTP: (target: string, code: string) => Promise<boolean>;
  resendOTP: (target: string, type: "email" | "phone") => Promise<boolean>;
  updateAchievements: (
    achievements: Partial<User["achievements"]>,
  ) => Promise<void>;
  addBadge: (badge: BadgeTier) => Promise<void>;
  mfaResolver: null;
  setMFAResolver: () => void;
  isMFAEnabled: () => boolean;
  completeMFASetup: () => Promise<boolean>;
}

export interface AuthError extends Error {
  code: string;
  message: string;
}
