import React, { useState } from 'react';
import { PricingPlan } from './SubscriptionPage';

interface CurrentSubscriptionCardProps {
  subscriptionStatus: string | null;
  isSubscribed: boolean;
  currentPlan?: PricingPlan; // Optional: if you want to show which plan they're on
  onManage: () => Promise<void>;
}

const CurrentSubscriptionCard: React.FC<CurrentSubscriptionCardProps> = ({
  subscriptionStatus,
  isSubscribed,
  currentPlan,
  onManage
}) => {
  const [isManaging, setIsManaging] = useState(false);

  const handleManageClick = async () => {
    setIsManaging(true);
    try {
      await onManage();
    } catch (error) {
      console.error('Error managing subscription:', error);
    } finally {
      setIsManaging(false);
    }
  };

  const getStatusDisplayInfo = () => {
    switch (subscriptionStatus) {
      case 'active':
        return {
          color: 'text-green-600 bg-green-50 border-green-200',
          text: 'Active',
          description: 'Your subscription is active and you\'re enjoying an ad-free experience!'
        };
      case 'trialing':
        return {
          color: 'text-blue-600 bg-blue-50 border-blue-200',
          text: 'Trial',
          description: 'You\'re currently in a free trial period.'
        };
      case 'past_due':
        return {
          color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
          text: 'Payment Past Due',
          description: 'Please update your payment method to continue your ad-free experience.'
        };
      case 'canceled':
        return {
          color: 'text-red-600 bg-red-50 border-red-200',
          text: 'Canceled',
          description: 'Your subscription has been canceled and will end at the end of the current billing period.'
        };
      case 'unpaid':
        return {
          color: 'text-red-600 bg-red-50 border-red-200',
          text: 'Unpaid',
          description: 'Payment failed. Please update your payment method.'
        };
      default:
        return {
          color: 'text-gray-600 bg-gray-50 border-gray-200',
          text: subscriptionStatus || 'Unknown',
          description: 'Subscription status information is available.'
        };
    }
  };

  const statusInfo = getStatusDisplayInfo();

  if (!isSubscribed) {
    return null; // Don't render if user is not subscribed
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-900">Your Subscription</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium border ${statusInfo.color}`}>
          {statusInfo.text}
        </div>
      </div>

      <div className="space-y-3">
        <p className="text-gray-700">{statusInfo.description}</p>
        
        {currentPlan && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">Current Plan</h4>
            <div className="flex justify-between items-start">
              <div>
                <p className="font-medium">{currentPlan.name}</p>
                <p className="text-sm text-gray-600">{currentPlan.description}</p>
              </div>
              <p className="text-lg font-semibold text-gray-900">{currentPlan.price}</p>
            </div>
            {currentPlan.features && currentPlan.features.length > 0 && (
              <ul className="mt-3 space-y-1">
                {currentPlan.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleManageClick}
            disabled={isManaging}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition duration-150 font-medium"
          >
            {isManaging ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Opening...
              </span>
            ) : (
              'Manage Subscription'
            )}
          </button>
          
          {(subscriptionStatus === 'past_due' || subscriptionStatus === 'unpaid') && (
            <button
              onClick={handleManageClick}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-150 font-medium"
            >
              Update Payment Method
            </button>
          )}
        </div>

        <div className="text-xs text-gray-500 border-t pt-3">
          <p>
            You can manage your subscription, update payment methods, view billing history, 
            and cancel your subscription through the secure Stripe customer portal.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CurrentSubscriptionCard;