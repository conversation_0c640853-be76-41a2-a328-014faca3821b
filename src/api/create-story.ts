import { NextFunction, Request, Response } from "express";
import { DatabaseAdapter } from "@/lib/db-adapter";

export const createStory = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const storyData = req.body;
    const result = await DatabaseAdapter.createStory(storyData);
    if (result.error) {
      return res.status(500).json({ error: result.error.message });
    }
    res.status(201).json(result.data);
  } catch (error) {
    next(error);
  }
};
