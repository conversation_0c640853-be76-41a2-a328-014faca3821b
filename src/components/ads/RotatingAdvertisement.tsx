import React, { useState, useEffect } from "react";
import { useAds, AdPlacement } from "@/contexts/AdsContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { XCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import GoogleAd from "@/components/ads/GoogleAd";

interface RotatingAdvertisementProps {
  placement: AdPlacement;
  className?: string;
  rotationInterval?: number; // in milliseconds, default 1 minute
}

// Multiple ad slots for each placement to enable rotation
const rotatingSlotMap = {
  header: [
    import.meta.env.VITE_ADSENSE_SLOT_HEADER,
    import.meta.env.VITE_ADSENSE_SLOT_HEADER_2,
    import.meta.env.VITE_ADSENSE_SLOT_HEADER_3,
  ].filter(Boolean), // Remove undefined slots
  footer: [
    import.meta.env.VITE_ADSENSE_SLOT_FOOTER,
    import.meta.env.VITE_ADSENSE_SLOT_FOOTER_2,
    import.meta.env.VITE_ADSENSE_SLOT_FOOTER_3,
  ].filter(Boolean),
  content: [
    import.meta.env.VITE_ADSENSE_SLOT_GALLERY_INFEED,
    import.meta.env.VITE_ADSENSE_SLOT_GALLERY_INFEED_2,
    import.meta.env.VITE_ADSENSE_SLOT_GALLERY_INFEED_3,
  ].filter(Boolean),
  sidebar: [
    import.meta.env.VITE_ADSENSE_SLOT_STORY_SIDEBAR,
    import.meta.env.VITE_ADSENSE_SLOT_STORY_SIDEBAR_2,
    import.meta.env.VITE_ADSENSE_SLOT_STORY_SIDEBAR_3,
  ].filter(Boolean),
  "story-end": [
    import.meta.env.VITE_ADSENSE_SLOT_STORY_END,
    import.meta.env.VITE_ADSENSE_SLOT_STORY_END_2,
    import.meta.env.VITE_ADSENSE_SLOT_STORY_END_3,
  ].filter(Boolean),
};

export const RotatingAdvertisement: React.FC<RotatingAdvertisementProps> = ({
  placement,
  className = "",
  rotationInterval = 30000, // 30 seconds default
}) => {
  const { shouldShowAd } = useAds();
  const navigate = useNavigate();
  const [currentAdIndex, setCurrentAdIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  
  const showAd = shouldShowAd(placement);
  const adSlots = rotatingSlotMap[placement] || [];
  const currentAdSlot = adSlots[currentAdIndex];

  // Rotation logic with pause on user interaction
  useEffect(() => {
    if (!showAd || adSlots.length <= 1 || isPaused) return;

    const intervalId = setInterval(() => {
      setCurrentAdIndex((prevIndex) => (prevIndex + 1) % adSlots.length);
    }, rotationInterval);

    return () => clearInterval(intervalId);
  }, [showAd, adSlots.length, rotationInterval, isPaused]);

  // Pause rotation when user is actively interacting with the page
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleUserActivity = () => {
      setIsPaused(true);
      clearTimeout(timeoutId);
      
      // Resume rotation after 5 seconds of inactivity
      timeoutId = setTimeout(() => {
        setIsPaused(false);
      }, 5000);
    };

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
      clearTimeout(timeoutId);
    };
  }, []);

  if (!showAd || !currentAdSlot) return null;

  // Different ad styles based on placement
  const getAdStyle = () => {
    switch (placement) {
      case "header":
        return "w-full h-16 bg-white border border-gray-200";
      case "sidebar":
        return "w-full h-64 bg-white border border-gray-200";
      case "footer":
        return "w-full h-24 bg-white border border-gray-200";
      case "content":
        return "w-full h-36 bg-white border border-gray-200";
      default:
        return "w-full h-24 bg-white border border-gray-200";
    }
  };

  const handleGoAdFree = () => {
    navigate("/subscription?plan=ad-free");
  };

  return (
    <Card className={`${getAdStyle()} overflow-hidden shadow-sm transition-opacity duration-500 ${className}`}>
      <CardContent className="p-2 h-full flex flex-col justify-between">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500 font-medium">
              Advertisement
            </span>
            {adSlots.length > 1 && (
              <div className="flex space-x-1">
                {adSlots.map((_, index) => (
                  <div
                    key={index}
                    className={`w-1.5 h-1.5 rounded-full transition-colors duration-300 ${
                      index === currentAdIndex 
                        ? 'bg-literary-gold' 
                        : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            )}
            {isPaused && (
              <span className="text-xs text-orange-500 font-medium">⏸️</span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
            onClick={handleGoAdFree}
            aria-label="Remove ads"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-grow flex items-center justify-center">
          <GoogleAd 
            placement={placement} 
            slot={currentAdSlot}
            key={`${placement}-${currentAdIndex}`} // Force re-render on rotation
          />
        </div>

        <div className="text-center mt-2">
          <Button
            variant="link"
            size="sm"
            className="text-xs text-literary-gold hover:text-literary-gold/80 p-0 font-medium"
            onClick={handleGoAdFree}
          >
            Remove ads with Premium
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}; 