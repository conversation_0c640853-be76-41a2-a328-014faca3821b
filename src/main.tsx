import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { PostHogProvider } from "@/components/providers";

// Test utilities are conditionally loaded to avoid network errors
if (process.env.NODE_ENV === "development") {
  // Dynamically import test utilities only in development
  import("./utils/test-credits").catch((err) => {
    console.warn("Failed to load test utilities:", err);
  });
}

createRoot(document.getElementById("root")!).render(
  <PostHogProvider>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </PostHogProvider>
);
