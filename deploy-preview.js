// Script to deploy to preview environment
import { execSync } from "child_process";

console.log("Deploying to preview environment...");

try {
  // Generate Prisma client
  console.log("Generating Prisma client...");
  execSync("npx prisma generate", { stdio: "inherit" });

  // Deploy to Vercel preview
  console.log("Deploying to Vercel preview...");
  execSync("vercel", { stdio: "inherit" });

  console.log("Deployment to preview completed successfully!");
  console.log(
    "Note: Database migrations will be run automatically during the build process.",
  );
} catch (error) {
  console.error("Error deploying to preview:", error);
  process.exit(1);
}
