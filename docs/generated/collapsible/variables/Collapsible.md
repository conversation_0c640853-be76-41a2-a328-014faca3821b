[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [collapsible](../README.md) / Collapsible

# Variable: Collapsible

> `const` **Collapsible**: `ForwardRefExoticComponent`\<`CollapsibleProps` & `RefAttributes`\<`HTMLDivElement`\>\> = `CollapsiblePrimitive.Root`

Defined in: [src/components/ui/collapsible.tsx:3](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/collapsible.tsx#L3)
