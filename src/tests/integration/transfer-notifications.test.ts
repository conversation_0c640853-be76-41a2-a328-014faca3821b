import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the services before importing them
vi.mock('@/services/transferNotificationService', () => ({
  transferNotificationService: {
    notifyTransferSuccess: vi.fn().mockResolvedValue(undefined),
    notifyTransferFailure: vi.fn().mockResolvedValue(undefined),
    notifyTransferLimit: vi.fn().mockResolvedValue(undefined),
    notifyTransferSummary: vi.fn().mockResolvedValue(undefined),
  }
}));

vi.mock('@/services/transferService', () => ({
  transferService: {
    executeTransfer: vi.fn().mockResolvedValue({ success: true, transferId: 'test-123' }),
    checkTransferLimits: vi.fn().mockResolvedValue({ allowed: true }),
  }
}));

// Now import the mocked services
import { transferNotificationService } from '@/services/transferNotificationService';
import { transferService } from '@/services/transferService';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({ error: null })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => ({ 
            data: { username: 'testuser' }, 
            error: null 
          })),
          in: vi.fn(() => ({
            order: vi.fn(() => ({
              range: vi.fn(() => ({ 
                data: [], 
                error: null 
              }))
            }))
          }))
        })),
        update: vi.fn(() => ({
          eq: vi.fn(() => ({
            in: vi.fn(() => ({ error: null }))
          }))
        }))
      }))
    }))
  }
}));

// Mock token service
vi.mock('@/services/tokenService', () => ({
  tokenService: {
    findUserByUsername: vi.fn(() => Promise.resolve({
      userId: 'recipient-id',
      username: 'recipient',
      isActive: true
    })),
    canAffordTransfer: vi.fn(() => Promise.resolve({
      canAfford: true,
      balance: 100
    })),
    transferTokens: vi.fn(() => Promise.resolve({
      success: true,
      transferId: 'transfer-123',
      senderNewBalance: 90,
      recipientNewBalance: 110
    }))
  }
}));

// Mock auth hook
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>'
    }
  })
}));

// Mock toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('Transfer Notification System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('TransferNotificationService', () => {
    it('should send success notifications for both sender and recipient', async () => {
      const transferData = {
        senderId: 'sender-123',
        senderUsername: 'sender',
        recipientId: 'recipient-456',
        recipientUsername: 'recipient',
        amount: 50,
        transferId: 'transfer-789',
        transferMessage: 'Thanks for your help!',
        senderNewBalance: 50,
        recipientNewBalance: 150,
      };

      await transferNotificationService.notifyTransferSuccess(transferData);

      // Should create notifications for both users
      expect(transferNotificationService.notifyTransferSuccess).toHaveBeenCalledWith(transferData);
    });

    it('should send failure notification for sender only', async () => {
      const failureData = {
        senderId: 'sender-123',
        senderUsername: 'sender',
        recipientUsername: 'recipient',
        amount: 50,
        error: 'Insufficient tokens',
        transferMessage: 'Payment for service',
      };

      await transferNotificationService.notifyTransferFailure(failureData);

      expect(transferNotificationService.notifyTransferFailure).toHaveBeenCalledWith(failureData);
    });

    it('should send transfer limit notifications', async () => {
      await transferNotificationService.notifyTransferLimit('user-123', 'daily', 100);

      expect(transferNotificationService.notifyTransferLimit).toHaveBeenCalledWith(
        'user-123',
        'daily',
        100
      );
    });

    it('should send transfer summary notifications', async () => {
      const summaryData = {
        totalSent: 500,
        totalReceived: 300,
        transferCount: 15,
        topRecipients: [{ username: 'user1', amount: 200 }],
        topSenders: [{ username: 'user2', amount: 150 }],
      };

      await transferNotificationService.notifyTransferSummary('user-123', 'weekly', summaryData);

      expect(transferNotificationService.notifyTransferSummary).toHaveBeenCalledWith(
        'user-123',
        'weekly',
        summaryData
      );
    });
  });

  describe('Transfer Service Integration', () => {
    it('should send notifications when transfer succeeds', async () => {
      // Mock successful transfer
      vi.mocked(transferService.executeTransfer).mockResolvedValue({
        success: true,
        transferId: 'transfer-123',
        senderNewBalance: 75,
        recipientNewBalance: 125,
      });

      const transferRequest = {
        senderId: 'sender-123',
        recipientUsername: 'recipient',
        amount: 25,
        transferMessage: 'Payment for story collaboration',
      };

      const result = await transferService.executeTransfer(transferRequest);

      expect(result.success).toBe(true);
      expect(result.transferId).toBe('transfer-123');
    });

    it('should send failure notifications when transfer fails', async () => {
      // Mock a failed transfer
      vi.mocked(transferService.executeTransfer).mockResolvedValue({
        success: false,
        error: 'Insufficient balance'
      });

      const transferRequest = {
        senderId: 'sender-123',
        recipientUsername: 'recipient',
        amount: 1000,
        transferMessage: 'Large payment',
      };

      const result = await transferService.executeTransfer(transferRequest);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Insufficient balance');
    });

    it('should validate transfer limits and send notifications', async () => {
      // Mock limit check failure
      vi.mocked(transferService.checkTransferLimits).mockResolvedValue({
        allowed: false,
        error: 'Single transfer cannot exceed 500 tokens'
      });

      const limitCheck = await transferService.checkTransferLimits('user-123', 600);

      expect(limitCheck.allowed).toBe(false);
      expect(limitCheck.error).toContain('Single transfer cannot exceed');
    });
  });

  describe('Notification Templates', () => {
    it('should generate proper success notification messages', () => {
      const senderMessage = `You successfully sent 50 tokens to @recipient with message: "Thanks for your help!".`;
      const recipientMessage = `You received 50 tokens from @sender with message: "Thanks for your help!".`;

      expect(senderMessage).toContain('successfully sent');
      expect(senderMessage).toContain('50 tokens');
      expect(senderMessage).toContain('@recipient');
      expect(senderMessage).toContain('Thanks for your help!');

      expect(recipientMessage).toContain('received');
      expect(recipientMessage).toContain('50 tokens');
      expect(recipientMessage).toContain('@sender');
      expect(recipientMessage).toContain('Thanks for your help!');
    });

    it('should generate proper failure notification messages', () => {
      const failureMessage = `Failed to send 50 tokens to @recipient. Reason: Insufficient tokens`;

      expect(failureMessage).toContain('Failed to send');
      expect(failureMessage).toContain('50 tokens');
      expect(failureMessage).toContain('@recipient');
      expect(failureMessage).toContain('Insufficient tokens');
    });

    it('should generate proper limit notification messages', () => {
      const dailyLimitMessage = 'You have reached your daily transfer limit of 1000 tokens. Limit resets at midnight.';
      const singleLimitMessage = 'Transfer failed: Single transfer cannot exceed 500 tokens.';

      expect(dailyLimitMessage).toContain('daily transfer limit');
      expect(dailyLimitMessage).toContain('1000 tokens');
      expect(dailyLimitMessage).toContain('midnight');

      expect(singleLimitMessage).toContain('Single transfer');
      expect(singleLimitMessage).toContain('500 tokens');
    });
  });

  describe('Notification Data Structure', () => {
    it('should include all required fields in success notifications', () => {
      const expectedSenderNotification = {
        type: 'transfer_sent',
        content: {
          message: expect.any(String),
          amount: 50,
          recipient_username: 'recipient',
          transfer_id: 'transfer-123',
          new_balance: 50,
          transfer_message: 'Thanks for your help!',
        },
      };

      const expectedRecipientNotification = {
        type: 'transfer_received',
        content: {
          message: expect.any(String),
          amount: 50,
          sender_username: 'sender',
          transfer_id: 'transfer-123',
          new_balance: 150,
          transfer_message: 'Thanks for your help!',
        },
      };

      expect(expectedSenderNotification.content).toHaveProperty('amount');
      expect(expectedSenderNotification.content).toHaveProperty('recipient_username');
      expect(expectedSenderNotification.content).toHaveProperty('transfer_id');
      expect(expectedSenderNotification.content).toHaveProperty('new_balance');

      expect(expectedRecipientNotification.content).toHaveProperty('amount');
      expect(expectedRecipientNotification.content).toHaveProperty('sender_username');
      expect(expectedRecipientNotification.content).toHaveProperty('transfer_id');
      expect(expectedRecipientNotification.content).toHaveProperty('new_balance');
    });

    it('should include error details in failure notifications', () => {
      const expectedFailureNotification = {
        type: 'transfer_failed',
        content: {
          message: expect.any(String),
          amount: 50,
          recipient_username: 'recipient',
          error: 'Insufficient tokens',
          transfer_message: 'Payment for service',
        },
      };

      expect(expectedFailureNotification.content).toHaveProperty('error');
      expect(expectedFailureNotification.content).toHaveProperty('amount');
      expect(expectedFailureNotification.content).toHaveProperty('recipient_username');
    });
  });

  describe('Error Handling', () => {
    it('should not throw errors when notification creation fails', async () => {
      // Mock database error
      const { supabase } = await import('@/lib/supabase');
      vi.mocked(supabase.from)
        .mockReturnValueOnce({
          insert: vi.fn().mockReturnValue({ error: new Error('Database error') })
        } as any);

      const transferData = {
        senderId: 'sender-123',
        senderUsername: 'sender',
        recipientId: 'recipient-456',
        recipientUsername: 'recipient',
        amount: 50,
        transferId: 'transfer-789',
      };

      // Should not throw even if notification fails
      await expect(async () => {
        await transferNotificationService.notifyTransferSuccess(transferData);
      }).not.toThrow();
    });

    it('should handle malformed notification data gracefully', async () => {
      const malformedData = {
        senderId: '',
        senderUsername: '',
        recipientId: '',
        recipientUsername: '',
        amount: -1,
        transferId: '',
      };

      // Should not throw even with invalid data
      await expect(async () => {
        await transferNotificationService.notifyTransferSuccess(malformedData);
      }).not.toThrow();
    });
  });
}); 