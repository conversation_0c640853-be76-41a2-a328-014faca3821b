import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@10.17.0?target=deno&deno-std=0.132.0';
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts'; // Assuming you have a shared Supabase admin client
import { corsHeaders } from '../_shared/cors.ts';

const stripe = new Stripe(Deno.env.get('STRIPE_TEST_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2024-04-10',
});

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { user_id, subscription_id } = await req.json(); // Or retrieve subscription_id from your DB based on user_id

    if (!user_id && !subscription_id) {
      throw new Error('User ID or Subscription ID is required to cancel.');
    }

    let subIdToCancel = subscription_id;

    if (!subIdToCancel && user_id) {
      // If only user_id is provided, fetch the active subscription ID from your database
      const { data: activeSubscription, error: dbError } = await supabaseAdmin
        .from('subscriptions')
        .select('stripe_subscription_id')
        .eq('user_id', user_id)
        .eq('status', 'active') // or other statuses that can be cancelled
        .maybeSingle();

      if (dbError) {
        throw new Error(`Database error fetching subscription: ${dbError.message}`);
      }
      if (!activeSubscription) {
        throw new Error('No active subscription found for this user to cancel.');
      }
      subIdToCancel = activeSubscription.stripe_subscription_id;
    }
    
    if (!subIdToCancel) {
        throw new Error('Could not determine subscription to cancel.');
    }

    // Option 1: Cancel immediately
    // const deletedSubscription = await stripe.subscriptions.del(subIdToCancel);

    // Option 2: Cancel at period end (recommended for better UX)
    const updatedSubscription = await stripe.subscriptions.update(subIdToCancel, {
      cancel_at_period_end: true,
    });

    // Update your database record accordingly (e.g., status to 'canceled', set cancel_at_period_end)
    // This might also be handled by the 'customer.subscription.updated' webhook event
    const { error: updateError } = await supabaseAdmin
        .from('subscriptions')
        .update({ 
            status: 'canceled', // Or reflect `cancel_at_period_end`
            cancel_at_period_end: true,
            // updated_at: new Date().toISOString()
        })
        .eq('stripe_subscription_id', subIdToCancel);

    if (updateError) {
        console.warn('Failed to update subscription status in DB after Stripe cancellation, webhook should handle it.', updateError);
        // Not throwing here as Stripe cancellation was successful
    }

    return new Response(JSON.stringify({ 
        message: 'Subscription scheduled for cancellation at period end.',
        subscription: updatedSubscription 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});
