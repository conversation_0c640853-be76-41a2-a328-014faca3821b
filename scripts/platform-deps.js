#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const WINDOWS_DEPS = [
  '@rollup/rollup-win32-x64-msvc',
  '@esbuild/win32-x64'
];

const isWindows = process.platform === 'win32';
const isVercel = process.env.VERCEL === '1' || process.env.CI === 'true';

/**
 * Add Windows-specific dependencies for local development
 */
function addWindowsDeps() {
  if (!isWindows || isVercel) {
    console.log('⏭️  Skipping Windows deps (not Windows or in CI/Vercel)');
    return;
  }

  console.log('🪟 Adding Windows-specific dependencies for local development...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Add to optionalDependencies
    if (!packageJson.optionalDependencies) {
      packageJson.optionalDependencies = {};
    }
    
    WINDOWS_DEPS.forEach(dep => {
      if (!packageJson.optionalDependencies[dep]) {
        packageJson.optionalDependencies[dep] = '^4.13.0'; // Latest compatible version
        console.log(`  ✅ Added ${dep}`);
      }
    });
    
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    console.log('📦 Installing Windows dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    
  } catch (error) {
    console.error('❌ Error adding Windows deps:', error.message);
  }
}

/**
 * Remove Windows-specific dependencies for deployment
 */
function removeWindowsDeps() {
  console.log('🐧 Removing Windows-specific dependencies for deployment...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    if (packageJson.optionalDependencies) {
      WINDOWS_DEPS.forEach(dep => {
        if (packageJson.optionalDependencies[dep]) {
          delete packageJson.optionalDependencies[dep];
          console.log(`  ✅ Removed ${dep}`);
        }
      });
      
      // Clean up empty optionalDependencies
      if (Object.keys(packageJson.optionalDependencies).length === 0) {
        packageJson.optionalDependencies = {};
      }
    }
    
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    console.log('✅ Windows dependencies removed for deployment');
    
  } catch (error) {
    console.error('❌ Error removing Windows deps:', error.message);
  }
}

/**
 * Main function
 */
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'add':
      addWindowsDeps();
      break;
    case 'remove':
      removeWindowsDeps();
      break;
    case 'auto':
      if (isVercel) {
        removeWindowsDeps();
      } else {
        addWindowsDeps();
      }
      break;
    default:
      console.log(`
🔧 Platform Dependency Manager

Usage:
  node scripts/platform-deps.js add     - Add Windows deps for local dev
  node scripts/platform-deps.js remove  - Remove Windows deps for deployment  
  node scripts/platform-deps.js auto    - Auto-detect and manage deps

Current environment:
  Platform: ${process.platform}
  Vercel: ${isVercel}
  Windows: ${isWindows}
      `);
  }
}

main(); 