import { PERFORMANCE_THRESHOLDS } from '../../support/browsers/browser-config'

describe('Performance Testing Across Browsers', () => {
  beforeEach(() => {
    cy.mockSupabaseAuth()
  })

  context('Page Load Performance', () => {
    it('should load the homepage quickly', () => {
      cy.visit('/', {
        onBeforeLoad: (win) => {
          win.performance.mark('start-homepage')
        }
      })
      
      cy.window().then((win) => {
        win.performance.mark('end-homepage')
        win.performance.measure('homepage-load', 'start-homepage', 'end-homepage')
        
        const measure = win.performance.getEntriesByName('homepage-load')[0]
        expect(measure.duration).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime)
        
        cy.log(`Homepage loaded in ${measure.duration.toFixed(2)}ms`)
      })
    })

    it('should load the gallery page efficiently', () => {
      cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
      
      cy.visit('/gallery', {
        onBeforeLoad: (win) => {
          win.performance.mark('start-gallery')
        }
      })
      
      cy.wait('@getStories')
      
      cy.window().then((win) => {
        win.performance.mark('end-gallery')
        win.performance.measure('gallery-load', 'start-gallery', 'end-gallery')
        
        const measure = win.performance.getEntriesByName('gallery-load')[0]
        expect(measure.duration).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime)
        
        cy.log(`Gallery loaded in ${measure.duration.toFixed(2)}ms`)
      })
    })

    it('should handle large story lists without performance degradation', () => {
      // Mock a large dataset
      const largeStoryList = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        title: `Story ${i + 1}`,
        description: `This is story number ${i + 1}`,
        status: 'ACTIVE'
      }))
      
      cy.intercept('GET', '**/stories**', { body: largeStoryList }).as('getLargeStories')
      
      const startTime = Date.now()
      
      cy.visitAndWait('/gallery')
      cy.wait('@getLargeStories')
      
      // Check that rendering doesn't take too long
      cy.get('[data-testid=\"story-card\"]').should('have.length.greaterThan', 10)
      
      cy.then(() => {
        const loadTime = Date.now() - startTime
        expect(loadTime).to.be.lessThan(5000) // 5 seconds max for large lists
        cy.log(`Large story list rendered in ${loadTime}ms`)
      })
    })
  })

  context('JavaScript Performance', () => {
    it('should execute JavaScript efficiently', () => {
      cy.visitAndWait('/')
      
      cy.window().then((win) => {
        // Test DOM manipulation performance
        const startTime = win.performance.now()
        
        const testElement = win.document.createElement('div')
        testElement.innerHTML = 'Performance test'
        win.document.body.appendChild(testElement)
        
        const endTime = win.performance.now()
        const duration = endTime - startTime
        
        expect(duration).to.be.lessThan(10) // Should be very fast
        
        // Cleanup
        win.document.body.removeChild(testElement)
        
        cy.log(`DOM manipulation took ${duration.toFixed(2)}ms`)
      })
    })

    it('should handle form interactions efficiently', () => {
      cy.visitAndWait('/create-story')
      
      cy.window().then((win) => {
        win.performance.mark('start-form-interaction')
      })
      
      // Simulate rapid typing
      cy.get('[data-testid=\"story-title-input\"]').type('Performance Test Story Title That Is Quite Long')
      cy.get('[data-testid=\"story-description-input\"]').type('This is a performance test description that contains a lot of text to test how well the form handles rapid input and validation.')
      
      cy.window().then((win) => {
        win.performance.mark('end-form-interaction')
        win.performance.measure('form-interaction', 'start-form-interaction', 'end-form-interaction')
        
        const measure = win.performance.getEntriesByName('form-interaction')[0]
        expect(measure.duration).to.be.lessThan(1000) // Form should remain responsive
        
        cy.log(`Form interaction took ${measure.duration.toFixed(2)}ms`)
      })
    })

    it('should handle scroll performance efficiently', () => {
      cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
      
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.window().then((win) => {
        win.performance.mark('start-scroll')
      })
      
      // Test scrolling performance
      cy.scrollTo('bottom', { duration: 500 })
      cy.scrollTo('top', { duration: 500 })
      cy.scrollTo('center', { duration: 500 })
      
      cy.window().then((win) => {
        win.performance.mark('end-scroll')
        win.performance.measure('scroll-performance', 'start-scroll', 'end-scroll')
        
        const measure = win.performance.getEntriesByName('scroll-performance')[0]
        expect(measure.duration).to.be.lessThan(2000) // Scrolling should be smooth
        
        cy.log(`Scroll operations took ${measure.duration.toFixed(2)}ms`)
      })
    })
  })

  context('Memory Management', () => {
    it('should not leak memory during navigation', () => {
      cy.visitAndWait('/')
      
      cy.window().then((win) => {
        const initialMemory = (win.performance as any).memory?.usedJSHeapSize || 0
        cy.log(`Initial memory usage: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`)
        
        // Navigate through multiple pages
        const pages = ['/gallery', '/create-story', '/pricing', '/login', '/']
        
        pages.forEach(page => {
          cy.visitAndWait(page)
          cy.wait(500) // Allow time for page to settle
        })
        
        // Check memory usage after navigation
        cy.window().then((finalWin) => {
          const finalMemory = (finalWin.performance as any).memory?.usedJSHeapSize || 0
          const memoryIncrease = finalMemory - initialMemory
          
          cy.log(`Final memory usage: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`)
          cy.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)
          
          // Memory increase should be reasonable (less than 50MB)
          expect(memoryIncrease).to.be.lessThan(50 * 1024 * 1024)
        })
      })
    })

    it('should handle component mounting/unmounting efficiently', () => {
      cy.visitAndWait('/gallery')
      
      cy.window().then((win) => {
        const initialMemory = (win.performance as any).memory?.usedJSHeapSize || 0
        
        // Simulate component state changes
        for (let i = 0; i < 10; i++) {
          cy.get('[data-testid=\"search-input\"]').type('test')
          cy.get('[data-testid=\"search-input\"]').clear()
          cy.wait(100)
        }
        
        cy.window().then((finalWin) => {
          const finalMemory = (finalWin.performance as any).memory?.usedJSHeapSize || 0
          const memoryIncrease = finalMemory - initialMemory
          
          // Memory increase should be minimal for repetitive operations
          expect(memoryIncrease).to.be.lessThan(5 * 1024 * 1024) // Less than 5MB
          
          cy.log(`Component operations memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)
        })
      })
    })
  })

  context('Network Performance', () => {
    it('should handle multiple API calls efficiently', () => {
      // Simulate multiple concurrent API calls
      const apiCalls = [
        { url: '**/stories**', fixture: 'stories.json' },
        { url: '**/users/profile', body: { id: 1, username: 'test' } },
        { url: '**/subscriptions/status', body: { isSubscribed: false } }
      ]
      
      apiCalls.forEach((call, index) => {
        cy.intercept('GET', call.url, call.fixture ? { fixture: call.fixture } : { body: call.body })
          .as(`apiCall${index}`)
      })
      
      const startTime = Date.now()
      
      cy.visitAndWait('/dashboard')
      
      // Wait for all API calls to complete
      apiCalls.forEach((_, index) => {
        cy.wait(`@apiCall${index}`)
      })
      
      cy.then(() => {
        const totalTime = Date.now() - startTime
        expect(totalTime).to.be.lessThan(3000) // All API calls should complete within 3 seconds
        
        cy.log(`Multiple API calls completed in ${totalTime}ms`)
      })
    })

    it('should handle failed network requests gracefully', () => {
      // Mock network failures
      cy.intercept('GET', '**/stories**', { forceNetworkError: true }).as('networkError')
      
      const startTime = Date.now()
      
      cy.visitAndWait('/gallery')
      cy.wait('@networkError')
      
      // Page should still load and show error state
      cy.contains('Error loading').should('be.visible')
      
      cy.then(() => {
        const errorHandlingTime = Date.now() - startTime
        expect(errorHandlingTime).to.be.lessThan(5000) // Error should be handled quickly
        
        cy.log(`Error handling took ${errorHandlingTime}ms`)
      })
    })
  })

  context('Rendering Performance', () => {
    it('should render large lists efficiently', () => {
      const largeList = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        title: `Item ${i}`,
        description: `Description for item ${i}`
      }))
      
      cy.intercept('GET', '**/stories**', { body: largeList }).as('getLargeList')
      
      cy.visitAndWait('/gallery')
      cy.wait('@getLargeList')
      
      cy.window().then((win) => {
        win.performance.mark('start-render')
      })
      
      // Wait for rendering to complete
      cy.get('[data-testid=\"story-card\"]').should('be.visible')
      
      cy.window().then((win) => {
        win.performance.mark('end-render')
        win.performance.measure('render-time', 'start-render', 'end-render')
        
        const measure = win.performance.getEntriesByName('render-time')[0]
        
        // Large list should render within reasonable time
        expect(measure.duration).to.be.lessThan(2000)
        
        cy.log(`Large list rendered in ${measure.duration.toFixed(2)}ms`)
      })
    })

    it('should handle dynamic content updates efficiently', () => {
      cy.visitAndWait('/gallery')
      
      cy.window().then((win) => {
        win.performance.mark('start-updates')
      })
      
      // Simulate dynamic content updates
      cy.get('[data-testid=\"search-input\"]').type('test')
      cy.get('[data-testid=\"sort-select\"]').select('newest')
      cy.get('[data-testid=\"status-filter\"]').select('ACTIVE')
      
      cy.window().then((win) => {
        win.performance.mark('end-updates')
        win.performance.measure('update-time', 'start-updates', 'end-updates')
        
        const measure = win.performance.getEntriesByName('update-time')[0]
        
        // Dynamic updates should be fast
        expect(measure.duration).to.be.lessThan(500)
        
        cy.log(`Dynamic updates took ${measure.duration.toFixed(2)}ms`)
      })
    })
  })

  context('Browser-Specific Performance', () => {
    it('should perform well in Chrome/Chromium', () => {
      if (Cypress.browser.family === 'chromium') {
        cy.visitAndWait('/')
        
        cy.window().then((win) => {
          // Chrome-specific performance checks
          const navigation = win.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          
          if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.fetchStart
            expect(loadTime).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime)
            
            cy.log(`Chrome navigation timing: ${loadTime.toFixed(2)}ms`)
          }
        })
      }
    })

    it('should perform well in Firefox', () => {
      if (Cypress.browser.name === 'firefox') {
        cy.visitAndWait('/')
        
        cy.window().then((win) => {
          // Firefox-specific performance checks
          const navigation = win.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          
          if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.fetchStart
            expect(loadTime).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime + 500) // Allow slightly more time for Firefox
            
            cy.log(`Firefox navigation timing: ${loadTime.toFixed(2)}ms`)
          }
        })
      }
    })

    it('should perform well in Safari/WebKit', () => {
      if (Cypress.browser.name === 'webkit') {
        cy.visitAndWait('/')
        
        cy.window().then((win) => {
          // Safari-specific performance checks
          const navigation = win.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          
          if (navigation) {
            const loadTime = navigation.loadEventEnd - navigation.fetchStart
            expect(loadTime).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime + 1000) // Safari might be slower
            
            cy.log(`Safari navigation timing: ${loadTime.toFixed(2)}ms`)
          }
        })
      }
    })
  })
})