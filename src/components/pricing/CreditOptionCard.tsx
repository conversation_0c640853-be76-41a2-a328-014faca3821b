import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface Option {
  label: string;
  creditsLabel: string;
  price: string;
  credits?: number;
  cents?: number;
  buttonLabel?: React.ReactNode;
}

export interface CreditOptionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  options: Option[];
  onBuy: (type: string, amount: number, cents: number) => void;
  isLoading: boolean;
  type: string;
}

export const CreditOptionCard: React.FC<CreditOptionCardProps> = ({
  icon,
  title,
  description,
  options,
  onBuy,
  isLoading,
  type,
}) => (
  <Card>
    <CardHeader className="pb-2">
      <CardTitle className="text-base font-medium flex items-center gap-2">
        {icon}
        {title}
      </CardTitle>
    </CardHeader>
    <CardContent className="pb-2">
      <p className="text-sm text-gray-600">{description}</p>
      <div className="mt-2 space-y-1">
        {options.map((opt, idx) => (
          <div className="flex justify-between items-center" key={idx}>
            <span className="text-sm">{opt.creditsLabel}</span>
            <span
              className={
                opt.price ? "text-lg font-bold" : "text-sm text-gray-500"
              }
            >
              {opt.price}
            </span>
          </div>
        ))}
      </div>
    </CardContent>
    <CardFooter>
      <div className="grid grid-cols-3 gap-2 w-full">
        {options.map((opt, idx) =>
          opt.credits && opt.cents !== undefined ? (
            <Button
              key={idx}
              variant="outline"
              size="sm"
              onClick={() => onBuy(type, opt.credits!, opt.cents!)}
              disabled={isLoading}
              className="text-xs"
            >
              {isLoading ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                opt.buttonLabel || opt.label
              )}
            </Button>
          ) : null,
        )}
      </div>
    </CardFooter>
  </Card>
);
