{"mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "H:\\WORK\\AG\\word-by-word-story", "h:\\WORK\\AG\\word-by-word-story"]}, "taskmaster-ai": {"command": "task-master-ai", "args": [], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "GOOGLE_API_KEY": "AIzaSyCakgjYWmwtHNqPK69oq8yhIwwdCiEqgD0", "OPENROUTER_API_KEY": "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"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZ3NodGZ0emxzcHFwZnFydHV2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDg1NTUxNSwiZXhwIjoyMDYwNDMxNTE1fQ.4lnBxgSEV1m9OrZN9Jxtr1HA4j87GtfnNtFwLY9ZlbQ"]}}}