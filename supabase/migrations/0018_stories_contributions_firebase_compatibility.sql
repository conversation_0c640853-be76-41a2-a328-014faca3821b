-- Migration: Make stories and contributions tables compatible with Firebase Auth IDs

-- Update stories table
ALTER TABLE public.stories ALTER COLUMN author_id TYPE TEXT;

-- Update contributions table  
ALTER TABLE public.contributions ALTER COLUMN user_id TYPE TEXT;

-- Update any other tables that reference user IDs
-- (Add more ALTER statements here if there are other tables)

-- Drop and recreate RLS policies for stories
DROP POLICY IF EXISTS "Stories are viewable by everyone" ON public.stories;
DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;

CREATE POLICY "Service role can manage all stories"
ON public.stories FOR ALL 
USING (true)
WITH CHECK (true);

-- Drop and recreate RLS policies for contributions
DROP POLICY IF EXISTS "Contributions are viewable by everyone" ON public.contributions;
DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;

CREATE POLICY "Service role can manage all contributions"
ON public.contributions FOR ALL 
USING (true)
WITH CHECK (true); 