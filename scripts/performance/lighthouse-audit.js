#!/usr/bin/env node

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs').promises;
const path = require('path');

// Configuration for Lighthouse audits
const CONFIG = {
  extends: 'lighthouse:default',
  settings: {
    formFactor: 'desktop',
    throttling: {
      rttMs: 40,
      throughputKbps: 10240,
      cpuSlowdownMultiplier: 1,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0
    },
    screenEmulation: {
      mobile: false,
      width: 1350,
      height: 940,
      deviceScaleFactor: 1,
      disabled: false,
    },
    emulatedUserAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  }
};

const MOBILE_CONFIG = {
  extends: 'lighthouse:default',
  settings: {
    formFactor: 'mobile',
    throttling: {
      rttMs: 150,
      throughputKbps: 1638.4,
      cpuSlowdownMultiplier: 4,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0
    },
    screenEmulation: {
      mobile: true,
      width: 375,
      height: 667,
      deviceScaleFactor: 2,
      disabled: false,
    }
  }
};

// Pages to audit
const PAGES_TO_AUDIT = [
  { name: 'Homepage', url: 'http://localhost:5173/' },
  { name: 'Gallery', url: 'http://localhost:5173/gallery' },
  { name: 'Create Story', url: 'http://localhost:5173/create-story' },
  { name: 'Login', url: 'http://localhost:5173/login' },
  { name: 'Pricing', url: 'http://localhost:5173/pricing' },
  { name: 'Subscription', url: 'http://localhost:5173/subscription' }
];

// Performance thresholds
const THRESHOLDS = {
  performance: 90,
  accessibility: 95,
  bestPractices: 90,
  seo: 95,
  pwa: 80
};

// Core Web Vitals thresholds (in milliseconds)
const CORE_WEB_VITALS = {
  LCP: 2500,  // Largest Contentful Paint
  FID: 100,   // First Input Delay  
  CLS: 0.1    // Cumulative Layout Shift
};

async function launchChrome() {
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--no-sandbox', '--disable-gpu']
  });
  return chrome;
}

async function runLighthouseAudit(url, config, chrome) {
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
    port: chrome.port,
  };

  const runnerResult = await lighthouse(url, options, config);
  return runnerResult;
}

function extractMetrics(lighthouseResult) {
  const { lhr } = lighthouseResult;
  const { categories, audits } = lhr;

  return {
    scores: {
      performance: Math.round(categories.performance.score * 100),
      accessibility: Math.round(categories.accessibility.score * 100),
      bestPractices: Math.round(categories['best-practices'].score * 100),
      seo: Math.round(categories.seo.score * 100)
    },
    metrics: {
      firstContentfulPaint: audits['first-contentful-paint'].numericValue,
      largestContentfulPaint: audits['largest-contentful-paint'].numericValue,
      firstInputDelay: audits['max-potential-fid']?.numericValue || 0,
      cumulativeLayoutShift: audits['cumulative-layout-shift'].numericValue,
      speedIndex: audits['speed-index'].numericValue,
      timeToInteractive: audits['interactive'].numericValue,
      totalBlockingTime: audits['total-blocking-time'].numericValue
    },
    opportunities: audits['diagnostics'] ? Object.keys(audits)
      .filter(key => audits[key].details && audits[key].details.type === 'opportunity')
      .map(key => ({
        id: key,
        title: audits[key].title,
        description: audits[key].description,
        score: audits[key].score,
        numericValue: audits[key].numericValue
      })) : []
  };
}

function generateReport(results) {
  let report = '# Lighthouse Performance Audit Report\\n\\n';
  report += `Generated on: ${new Date().toISOString()}\\n\\n`;

  // Summary table
  report += '## Performance Summary\\n\\n';
  report += '| Page | Performance | Accessibility | Best Practices | SEO | Status |\\n';
  report += '|------|-------------|---------------|----------------|-----|--------|\\n';

  results.forEach(result => {
    const { scores } = result.metrics;
    const status = Object.values(scores).every(score => score >= 80) ? '✅' : '⚠️';
    report += `| ${result.pageName} | ${scores.performance} | ${scores.accessibility} | ${scores.bestPractices} | ${scores.seo} | ${status} |\\n`;
  });

  // Core Web Vitals
  report += '\\n## Core Web Vitals\\n\\n';
  report += '| Page | LCP (ms) | FID (ms) | CLS | Status |\\n';
  report += '|------|----------|----------|-----|--------|\\n';

  results.forEach(result => {
    const { metrics } = result.metrics;
    const lcpStatus = metrics.largestContentfulPaint <= CORE_WEB_VITALS.LCP ? '✅' : '❌';
    const fidStatus = metrics.firstInputDelay <= CORE_WEB_VITALS.FID ? '✅' : '❌';
    const clsStatus = metrics.cumulativeLayoutShift <= CORE_WEB_VITALS.CLS ? '✅' : '❌';
    const overallStatus = lcpStatus === '✅' && fidStatus === '✅' && clsStatus === '✅' ? '✅' : '❌';
    
    report += `| ${result.pageName} | ${Math.round(metrics.largestContentfulPaint)} ${lcpStatus} | ${Math.round(metrics.firstInputDelay)} ${fidStatus} | ${metrics.cumulativeLayoutShift.toFixed(3)} ${clsStatus} | ${overallStatus} |\\n`;
  });

  // Detailed metrics
  report += '\\n## Detailed Metrics\\n\\n';
  results.forEach(result => {
    const { metrics } = result.metrics;
    report += `### ${result.pageName}\\n\\n`;
    report += `- **First Contentful Paint**: ${Math.round(metrics.firstContentfulPaint)}ms\\n`;
    report += `- **Largest Contentful Paint**: ${Math.round(metrics.largestContentfulPaint)}ms\\n`;
    report += `- **Speed Index**: ${Math.round(metrics.speedIndex)}ms\\n`;
    report += `- **Time to Interactive**: ${Math.round(metrics.timeToInteractive)}ms\\n`;
    report += `- **Total Blocking Time**: ${Math.round(metrics.totalBlockingTime)}ms\\n`;
    report += `- **Cumulative Layout Shift**: ${metrics.cumulativeLayoutShift.toFixed(3)}\\n\\n`;
  });

  // Recommendations
  report += '\\n## Optimization Opportunities\\n\\n';
  results.forEach(result => {
    if (result.metrics.opportunities.length > 0) {
      report += `### ${result.pageName}\\n\\n`;
      result.metrics.opportunities.forEach(opp => {
        if (opp.score < 0.9) {
          report += `- **${opp.title}**: ${opp.description}\\n`;
        }
      });
      report += '\\n';
    }
  });

  return report;
}

async function saveResults(results, format = 'json') {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const outputDir = path.join(__dirname, '../../reports/performance');
  
  try {
    await fs.mkdir(outputDir, { recursive: true });
  } catch (error) {
    // Directory already exists
  }

  if (format === 'json') {
    const filename = `lighthouse-audit-${timestamp}.json`;
    await fs.writeFile(path.join(outputDir, filename), JSON.stringify(results, null, 2));
    console.log(`Results saved to: reports/performance/${filename}`);
  }

  if (format === 'markdown' || format === 'both') {
    const report = generateReport(results);
    const filename = `lighthouse-report-${timestamp}.md`;
    await fs.writeFile(path.join(outputDir, filename), report);
    console.log(`Report saved to: reports/performance/${filename}`);
  }
}

async function main() {
  console.log('🚀 Starting Lighthouse Performance Audit...\\n');
  
  const chrome = await launchChrome();
  const results = [];

  try {
    for (const page of PAGES_TO_AUDIT) {
      console.log(`Auditing ${page.name}...`);
      
      // Desktop audit
      const desktopResult = await runLighthouseAudit(page.url, CONFIG, chrome);
      const desktopMetrics = extractMetrics(desktopResult);
      
      // Mobile audit  
      const mobileResult = await runLighthouseAudit(page.url, MOBILE_CONFIG, chrome);
      const mobileMetrics = extractMetrics(mobileResult);

      results.push({
        pageName: page.name,
        url: page.url,
        desktop: desktopMetrics,
        mobile: mobileMetrics,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ ${page.name} complete`);
    }

    // Save results
    await saveResults(results, 'both');
    
    // Check if any page fails thresholds
    const failures = results.filter(result => {
      const desktopScores = result.desktop.scores;
      const mobileScores = result.mobile.scores;
      return Object.keys(THRESHOLDS).some(category => 
        desktopScores[category] < THRESHOLDS[category] || 
        mobileScores[category] < THRESHOLDS[category]
      );
    });

    if (failures.length > 0) {
      console.log('\\n❌ Some pages failed performance thresholds:');
      failures.forEach(failure => console.log(`- ${failure.pageName}`));
      process.exit(1);
    } else {
      console.log('\\n✅ All pages passed performance thresholds!');
    }

  } catch (error) {
    console.error('Error during audit:', error);
    process.exit(1);
  } finally {
    await chrome.kill();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runLighthouseAudit, extractMetrics, generateReport };