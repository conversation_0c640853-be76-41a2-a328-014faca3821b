import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { AiWordSuggestionsProps, Word } from "@/types";
import {
  generateAiWordSuggestions,
  AI_CREDIT_COSTS,
} from "@/services/aiServices";
import { useCreditOperation } from "@/hooks/use-credit-operation";

interface ApiError extends Error {
  message: string;
}

const MAX_SUGGESTIONS = 3;

export const AiWordSuggestions: React.FC<AiWordSuggestionsProps> = ({
  storyId,
  words,
  currentWord,
  onSelectWord,
  onSuggestionClick,
  observer,
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const { toast } = useToast();
  const { user } = useAuth();
  const { handleCreditOperation, isUsingCredits } = useCreditOperation();

  const { mutate, isPending, isError, error, reset } = useMutation({
    mutationFn: async (context: string) => {
      // First check if the user has enough credits and deduct them
      const success = await handleCreditOperation("wordSuggestion");
      if (!success) {
        throw new Error("Not enough credits for word suggestions");
      }

      // Then call the AI service
      return generateAiWordSuggestions(storyId || "", context);
    },
    onSuccess: (data) => {
      if (data && data.suggestions && data.suggestions.length > 0) {
        const limitedSuggestions = data.suggestions.slice(0, MAX_SUGGESTIONS);
        setSuggestions(limitedSuggestions);

        toast({
          title: "AI Suggestions Ready",
          description: `Used ${data.creditsUsed} credits for word suggestions.`,
        });
      } else {
        toast({
          title: "No suggestions found",
          description:
            "The AI could not come up with any suggestions for this word.",
        });
        setSuggestions([]);
      }
    },
    onError: (err: ApiError) => {
      console.error("Error fetching word suggestions:", err);
      toast({
        title: "Error fetching suggestions",
        description:
          err.message || "Failed to get AI word suggestions. Please try again.",
        variant: "destructive",
      });
      setSuggestions([]);
    },
  });

  useEffect(() => {
    if (words && words.length > 0) {
      const context = words.map((w) => w.word).join(" ");
      setSuggestions([]);
      mutate(context);
    } else {
      reset();
      setSuggestions([]);
    }
  }, [words, mutate, reset]);

  const handleSuggestionClick = (word: string) => {
    if (onSelectWord) {
      onSelectWord(word);
    }
    if (onSuggestionClick) {
      onSuggestionClick(word);
    }
    if (observer) {
      observer.recordClick("ai_suggestion", word);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex flex-col space-y-2 mt-2">
      <div className="text-sm text-gray-500 flex justify-between">
        <span>AI Word Suggestions</span>
        <span className="text-xs">
          Cost: {AI_CREDIT_COSTS.wordSuggestion} credit
          {AI_CREDIT_COSTS.wordSuggestion > 1 ? "s" : ""}
        </span>
      </div>
      <div className="flex space-x-2">
        {isPending || isUsingCredits ? (
          <Button variant="outline" disabled>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </Button>
        ) : isError ? (
          <Button
            variant="destructive"
            onClick={() => mutate(words?.map((w) => w.word).join(" ") || "")}
          >
            <Sparkles className="mr-2 h-4 w-4" />
            Retry Suggestion
          </Button>
        ) : suggestions.length > 0 ? (
          <>
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                className="bg-literary-cream/50 hover:bg-literary-cream"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <Sparkles className="mr-1 h-3 w-3 text-literary-gold" />
                {suggestion}
              </Button>
            ))}
          </>
        ) : (
          <Button
            variant="outline"
            onClick={() => mutate(words?.map((w) => w.word).join(" ") || "")}
            className="bg-literary-cream/50 hover:bg-literary-cream"
          >
            <Sparkles className="mr-2 h-4 w-4 text-literary-gold" />
            Magic Words
          </Button>
        )}
      </div>
    </div>
  );
};

export default AiWordSuggestions;
