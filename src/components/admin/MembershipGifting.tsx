import React, { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Gift, Hourglass, Calendar } from "lucide-react";

type GiftType = "lifetime" | "subscription" | "trial";
type SubscriptionTier = "wordsmith" | "storyteller" | "authors-guild";

export const MembershipGifting: React.FC = () => {
  const { isAdmin } = useAuth();
  const { toast } = useToast();
  const [email, setEmail] = useState("");
  const [giftType, setGiftType] = useState<GiftType>("trial");
  const [tier, setTier] = useState<SubscriptionTier>("wordsmith");
  const [duration, setDuration] = useState("1");

  const handleGiftMembership = async () => {
    try {
      // Here you would integrate with your subscription system
      // For now, we'll just show a success message
      toast({
        title: "Gift Membership Created",
        description: `A ${giftType} membership has been gifted to ${email}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to gift membership",
        variant: "destructive",
      });
    }
  };

  if (!isAdmin()) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gift Memberships</CardTitle>
        <CardDescription>
          Create promotional memberships and gift subscriptions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Recipient Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label>Gift Type</Label>
          <Select
            value={giftType}
            onValueChange={(value: GiftType) => setGiftType(value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lifetime">
                <div className="flex items-center gap-2">
                  <Hourglass className="h-4 w-4" />
                  <span>Lifetime Membership</span>
                </div>
              </SelectItem>
              <SelectItem value="subscription">
                <div className="flex items-center gap-2">
                  <Gift className="h-4 w-4" />
                  <span>Gift Subscription</span>
                </div>
              </SelectItem>
              <SelectItem value="trial">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Free Trial</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Subscription Tier</Label>
          <Select
            value={tier}
            onValueChange={(value: SubscriptionTier) => setTier(value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="wordsmith">Wordsmith</SelectItem>
              <SelectItem value="storyteller">Storyteller</SelectItem>
              <SelectItem value="authors-guild">Authors Guild</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {giftType !== "lifetime" && (
          <div className="space-y-2">
            <Label>Duration (months)</Label>
            <Input
              type="number"
              min="1"
              max="12"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
            />
          </div>
        )}

        <Button
          className="w-full"
          onClick={handleGiftMembership}
          disabled={!email || !giftType || !tier}
        >
          Gift Membership
        </Button>
      </CardContent>
    </Card>
  );
};

export default MembershipGifting;
