[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toast](../README.md) / ToastActionElement

# Type Alias: ToastActionElement

> **ToastActionElement** = `React.ReactElement`\<*typeof* [`ToastAction`](../variables/ToastAction.md)\>

Defined in: [src/components/ui/toast.tsx:115](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toast.tsx#L115)
