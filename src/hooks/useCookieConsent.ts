import { useState, useEffect, useCallback } from 'react';
import { 
  getCookieConsent, 
  needsConsentBanner, 
  hasConsentFor,
  type CookieConsentSettings 
} from '@/utils/cookie-consent';

interface UseCookieConsentReturn {
  consentSettings: CookieConsentSettings | null;
  needsBanner: boolean;
  hasAnalyticsConsent: boolean;
  hasMarketingConsent: boolean;
  hasFunctionalConsent: boolean;
  showBanner: () => void;
  hideBanner: () => void;
  refreshConsent: () => void;
}

/**
 * Hook to manage cookie consent state and banner visibility
 */
export const useCookieConsent = (): UseCookieConsentReturn => {
  const [consentSettings, setConsentSettings] = useState<CookieConsentSettings | null>(null);
  const [needsBanner, setNeedsBanner] = useState(false);

  // Load initial consent state
  const loadConsentSettings = useCallback(() => {
    const settings = getCookieConsent();
    const needsBannerCheck = needsConsentBanner();
    
    setConsentSettings(settings);
    setNeedsBanner(needsBannerCheck);
    
    console.log('🍪 Cookie Consent State:', {
      hasConsent: !!settings,
      needsBanner: needsBannerCheck,
      settings: settings ? {
        analytics: settings.analytics,
        marketing: settings.marketing,
        functional: settings.functional,
        version: settings.version,
      } : null
    });
  }, []);

  // Handle consent changes
  const handleConsentChange = useCallback((event: CustomEvent) => {
    const newSettings = event.detail as CookieConsentSettings;
    setConsentSettings(newSettings);
    setNeedsBanner(false);
    
    console.log('🍪 Cookie Consent Updated:', newSettings);
  }, []);

  // Banner control functions
  const showBanner = useCallback(() => {
    setNeedsBanner(true);
  }, []);

  const hideBanner = useCallback(() => {
    setNeedsBanner(false);
  }, []);

  const refreshConsent = useCallback(() => {
    loadConsentSettings();
  }, [loadConsentSettings]);

  // Set up event listeners and initial load
  useEffect(() => {
    loadConsentSettings();

    // Listen for consent changes
    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener);

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener);
    };
  }, [loadConsentSettings, handleConsentChange]);

  // Derived state for easy access
  const hasAnalyticsConsent = hasConsentFor('analytics');
  const hasMarketingConsent = hasConsentFor('marketing');
  const hasFunctionalConsent = hasConsentFor('functional');

  return {
    consentSettings,
    needsBanner,
    hasAnalyticsConsent,
    hasMarketingConsent,
    hasFunctionalConsent,
    showBanner,
    hideBanner,
    refreshConsent,
  };
};

export default useCookieConsent; 