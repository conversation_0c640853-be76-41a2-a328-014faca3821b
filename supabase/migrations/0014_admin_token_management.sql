-- Migration: Admin Token Management System
-- File: supabase/migrations/0014_admin_token_management.sql

-- Create admin_token_grants table for detailed logging of admin token operations
CREATE TABLE IF NOT EXISTS public.admin_token_grants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL CHECK (amount > 0),
    reason TEXT NOT NULL CHECK (LENGTH(reason) >= 10), -- Mandatory reason with minimum length
    grant_type TEXT NOT NULL DEFAULT 'manual' CHECK (grant_type IN ('manual', 'bulk', 'automated', 'correction')),
    batch_id UUID NULL, -- For tracking bulk operations
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now(),
    
    -- Foreign key constraints
    CONSTRAINT fk_admin_token_grants_admin FOREIGN KEY (admin_user_id) REFERENCES auth.users(id),
    CONSTRAINT fk_admin_token_grants_target FOREIGN KEY (target_user_id) REFERENCES auth.users(id)
);

-- Create admin_sessions table to track admin login/activity
CREATE TABLE IF NOT EXISTS public.admin_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_start TIMESTAMPTZ DEFAULT now(),
    session_end TIMESTAMPTZ NULL,
    ip_address INET,
    user_agent TEXT,
    actions_performed INTEGER DEFAULT 0,
    last_activity TIMESTAMPTZ DEFAULT now(),
    is_active BOOLEAN DEFAULT true,
    
    CONSTRAINT fk_admin_sessions_admin FOREIGN KEY (admin_user_id) REFERENCES auth.users(id)
);

-- Create admin_actions table for granular activity tracking
CREATE TABLE IF NOT EXISTS public.admin_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES public.admin_sessions(id) ON DELETE SET NULL,
    action_type TEXT NOT NULL CHECK (action_type IN ('token_grant', 'user_search', 'view_logs', 'bulk_operation', 'export_data')),
    target_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action_details JSONB DEFAULT '{}',
    ip_address INET,
    created_at TIMESTAMPTZ DEFAULT now(),
    
    CONSTRAINT fk_admin_actions_admin FOREIGN KEY (admin_user_id) REFERENCES auth.users(id),
    CONSTRAINT fk_admin_actions_session FOREIGN KEY (session_id) REFERENCES public.admin_sessions(id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_token_grants_admin_user ON public.admin_token_grants(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_token_grants_target_user ON public.admin_token_grants(target_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_token_grants_created_at ON public.admin_token_grants(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_token_grants_batch_id ON public.admin_token_grants(batch_id) WHERE batch_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_user ON public.admin_sessions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_active ON public.admin_sessions(is_active, last_activity DESC);

CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_user ON public.admin_actions(admin_user_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_session ON public.admin_actions(session_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_created_at ON public.admin_actions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_actions_type ON public.admin_actions(action_type);

-- Enable RLS
ALTER TABLE public.admin_token_grants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_actions ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Only admins can access admin tables
CREATE POLICY "Only admins can view token grants"
ON public.admin_token_grants FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = auth.uid() AND is_admin = true
    )
);

CREATE POLICY "Only admins can view admin sessions"
ON public.admin_sessions FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = auth.uid() AND is_admin = true
    )
);

CREATE POLICY "Only admins can view admin actions"
ON public.admin_actions FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = auth.uid() AND is_admin = true
    )
);

-- Function to grant tokens as admin with comprehensive logging
CREATE OR REPLACE FUNCTION public.admin_grant_tokens(
    p_admin_user_id UUID,
    p_target_user_id UUID,
    p_amount INTEGER,
    p_reason TEXT,
    p_grant_type TEXT DEFAULT 'manual',
    p_batch_id UUID DEFAULT NULL,
    p_session_id UUID DEFAULT NULL
)
RETURNS TABLE (
    success BOOLEAN,
    new_balance INTEGER,
    grant_id UUID,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_admin BOOLEAN := FALSE;
    v_target_user_exists BOOLEAN := FALSE;
    v_new_balance INTEGER;
    v_grant_id UUID;
    v_current_balance INTEGER := 0;
BEGIN
    -- Verify admin privileges
    SELECT is_admin INTO v_is_admin
    FROM public.user_profiles
    WHERE id = p_admin_user_id;
    
    IF NOT v_is_admin THEN
        RETURN QUERY SELECT FALSE, 0, NULL::UUID, 'Unauthorized: Admin privileges required';
        RETURN;
    END IF;
    
    -- Validate input parameters
    IF p_amount <= 0 THEN
        RETURN QUERY SELECT FALSE, 0, NULL::UUID, 'Invalid amount: Must be positive';
        RETURN;
    END IF;
    
    IF LENGTH(TRIM(p_reason)) < 10 THEN
        RETURN QUERY SELECT FALSE, 0, NULL::UUID, 'Invalid reason: Must be at least 10 characters';
        RETURN;
    END IF;
    
    -- Check if target user exists
    SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = p_target_user_id) INTO v_target_user_exists;
    
    IF NOT v_target_user_exists THEN
        RETURN QUERY SELECT FALSE, 0, NULL::UUID, 'Target user not found';
        RETURN;
    END IF;
    
    -- Get current balance (initialize if needed)
    SELECT token_balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_target_user_id;
    
    -- Use existing add_user_tokens function to grant tokens
    SELECT new_balance INTO v_new_balance
    FROM public.add_user_tokens(
        p_target_user_id,
        p_amount,
        'Admin grant: ' || p_reason
    )
    WHERE success = TRUE;
    
    IF v_new_balance IS NULL THEN
        RETURN QUERY SELECT FALSE, COALESCE(v_current_balance, 0), NULL::UUID, 'Failed to grant tokens';
        RETURN;
    END IF;
    
    -- Log the admin token grant
    INSERT INTO public.admin_token_grants (
        admin_user_id,
        target_user_id,
        amount,
        reason,
        grant_type,
        batch_id
    ) VALUES (
        p_admin_user_id,
        p_target_user_id,
        p_amount,
        TRIM(p_reason),
        p_grant_type,
        p_batch_id
    ) RETURNING id INTO v_grant_id;
    
    -- Log admin action
    INSERT INTO public.admin_actions (
        admin_user_id,
        session_id,
        action_type,
        target_user_id,
        action_details
    ) VALUES (
        p_admin_user_id,
        p_session_id,
        'token_grant',
        p_target_user_id,
        jsonb_build_object(
            'amount', p_amount,
            'reason', p_reason,
            'grant_type', p_grant_type,
            'grant_id', v_grant_id,
            'new_balance', v_new_balance
        )
    );
    
    RETURN QUERY SELECT TRUE, v_new_balance, v_grant_id, NULL::TEXT;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, COALESCE(v_current_balance, 0), NULL::UUID, SQLERRM;
END;
$$;

-- Function to start admin session
CREATE OR REPLACE FUNCTION public.start_admin_session(
    p_admin_user_id UUID,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS TABLE (
    success BOOLEAN,
    session_id UUID,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_admin BOOLEAN := FALSE;
    v_session_id UUID;
BEGIN
    -- Verify admin privileges
    SELECT is_admin INTO v_is_admin
    FROM public.user_profiles
    WHERE id = p_admin_user_id;
    
    IF NOT v_is_admin THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Unauthorized: Admin privileges required';
        RETURN;
    END IF;
    
    -- End any existing active sessions for this admin
    UPDATE public.admin_sessions
    SET 
        session_end = now(),
        is_active = false
    WHERE admin_user_id = p_admin_user_id AND is_active = true;
    
    -- Create new session
    INSERT INTO public.admin_sessions (
        admin_user_id,
        ip_address,
        user_agent
    ) VALUES (
        p_admin_user_id,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO v_session_id;
    
    RETURN QUERY SELECT TRUE, v_session_id, NULL::TEXT;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, SQLERRM;
END;
$$;

-- Function to end admin session
CREATE OR REPLACE FUNCTION public.end_admin_session(
    p_session_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.admin_sessions
    SET 
        session_end = now(),
        is_active = false
    WHERE id = p_session_id AND is_active = true;
    
    RETURN FOUND;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

-- Function to get admin token grant history with filtering
CREATE OR REPLACE FUNCTION public.get_admin_token_grants(
    p_admin_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_target_user_id UUID DEFAULT NULL,
    p_grant_type TEXT DEFAULT NULL,
    p_from_date TIMESTAMPTZ DEFAULT NULL,
    p_to_date TIMESTAMPTZ DEFAULT NULL
)
RETURNS TABLE (
    grant_id UUID,
    admin_username TEXT,
    target_username TEXT,
    amount INTEGER,
    reason TEXT,
    grant_type TEXT,
    batch_id UUID,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_admin BOOLEAN := FALSE;
BEGIN
    -- Verify admin privileges
    SELECT is_admin INTO v_is_admin
    FROM public.user_profiles
    WHERE id = p_admin_user_id;
    
    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Unauthorized: Admin privileges required';
    END IF;
    
    RETURN QUERY
    SELECT 
        atg.id,
        admin_profile.username,
        target_profile.username,
        atg.amount,
        atg.reason,
        atg.grant_type,
        atg.batch_id,
        atg.created_at
    FROM public.admin_token_grants atg
    LEFT JOIN public.user_profiles admin_profile ON admin_profile.id = atg.admin_user_id
    LEFT JOIN public.user_profiles target_profile ON target_profile.id = atg.target_user_id
    WHERE 
        (p_target_user_id IS NULL OR atg.target_user_id = p_target_user_id)
        AND (p_grant_type IS NULL OR atg.grant_type = p_grant_type)
        AND (p_from_date IS NULL OR atg.created_at >= p_from_date)
        AND (p_to_date IS NULL OR atg.created_at <= p_to_date)
    ORDER BY atg.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Function for bulk token granting
CREATE OR REPLACE FUNCTION public.admin_bulk_grant_tokens(
    p_admin_user_id UUID,
    p_target_user_ids UUID[],
    p_amount INTEGER,
    p_reason TEXT,
    p_session_id UUID DEFAULT NULL
)
RETURNS TABLE (
    batch_id UUID,
    total_granted INTEGER,
    successful_grants INTEGER,
    failed_grants INTEGER,
    error_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_admin BOOLEAN := FALSE;
    v_batch_id UUID := uuid_generate_v4();
    v_target_user_id UUID;
    v_successful_count INTEGER := 0;
    v_failed_count INTEGER := 0;
    v_errors JSONB := '[]'::JSONB;
    v_grant_result RECORD;
BEGIN
    -- Verify admin privileges
    SELECT is_admin INTO v_is_admin
    FROM public.user_profiles
    WHERE id = p_admin_user_id;
    
    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Unauthorized: Admin privileges required';
    END IF;
    
    -- Validate inputs
    IF array_length(p_target_user_ids, 1) IS NULL OR array_length(p_target_user_ids, 1) = 0 THEN
        RAISE EXCEPTION 'No target users specified';
    END IF;
    
    IF array_length(p_target_user_ids, 1) > 100 THEN
        RAISE EXCEPTION 'Maximum 100 users allowed per bulk operation';
    END IF;
    
    -- Process each user
    FOREACH v_target_user_id IN ARRAY p_target_user_ids
    LOOP
        SELECT * INTO v_grant_result
        FROM public.admin_grant_tokens(
            p_admin_user_id,
            v_target_user_id,
            p_amount,
            p_reason,
            'bulk',
            v_batch_id,
            p_session_id
        );
        
        IF v_grant_result.success THEN
            v_successful_count := v_successful_count + 1;
        ELSE
            v_failed_count := v_failed_count + 1;
            v_errors := v_errors || jsonb_build_object(
                'user_id', v_target_user_id,
                'error', v_grant_result.error_message
            );
        END IF;
    END LOOP;
    
    RETURN QUERY SELECT 
        v_batch_id,
        array_length(p_target_user_ids, 1),
        v_successful_count,
        v_failed_count,
        v_errors;
END;
$$;

-- Grant execute permissions to authenticated users (admin check is within functions)
GRANT EXECUTE ON FUNCTION public.admin_grant_tokens(UUID, UUID, INTEGER, TEXT, TEXT, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.start_admin_session(UUID, INET, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.end_admin_session(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_admin_token_grants(UUID, INTEGER, INTEGER, UUID, TEXT, TIMESTAMPTZ, TIMESTAMPTZ) TO authenticated;
GRANT EXECUTE ON FUNCTION public.admin_bulk_grant_tokens(UUID, UUID[], INTEGER, TEXT, UUID) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.admin_token_grants IS 'Detailed logging of admin token grant operations for audit trail';
COMMENT ON TABLE public.admin_sessions IS 'Tracks admin login sessions and activity';
COMMENT ON TABLE public.admin_actions IS 'Granular logging of admin actions for security and audit purposes';

COMMENT ON COLUMN public.admin_token_grants.reason IS 'Mandatory reason for token grant (minimum 10 characters)';
COMMENT ON COLUMN public.admin_token_grants.grant_type IS 'Type of grant: manual, bulk, automated, or correction';
COMMENT ON COLUMN public.admin_token_grants.batch_id IS 'Groups related bulk operations together';

COMMENT ON FUNCTION public.admin_grant_tokens IS 'Grants tokens to users with admin authorization and comprehensive logging';
COMMENT ON FUNCTION public.admin_bulk_grant_tokens IS 'Performs bulk token grants with batch tracking and error handling'; 