[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [achievement-badge](../README.md) / getBadgeDetails

# Function: getBadgeDetails()

> **getBadgeDetails**(`tier`): `object`

Defined in: [src/components/ui/achievement-badge.tsx:33](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/achievement-badge.tsx#L33)

## Parameters

### tier

[`BadgeTier`](../type-aliases/BadgeTier.md)

## Returns

`object`

### color

> **color**: `string` = `"bg-blue-100 text-blue-700 border-blue-200"`

### description

> **description**: `string` = `"Created at least 5 stories or contributed 100+ words"`

### icon

> **icon**: `Element`

### label

> **label**: `string` = `"Wordsmith"`
