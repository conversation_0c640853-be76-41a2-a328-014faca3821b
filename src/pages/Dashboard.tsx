import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { useStory } from "@/contexts/StoryContext";
import { Story } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  CalendarDays,
  Clock,
  Users,
  Plus,
  Star,
  Book,
  User,
  Crown,
  Edit,
  Trash2,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import SocialShare from "@/components/story/SocialShare";
import { useDocumentTitle, createPageTitle } from "@/hooks/useDocumentTitle";

const StoryCard: React.FC<{
  story: Story;
  onDelete: (storyId: string) => Promise<void>;
}> = ({ story, onDelete }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const isCreator = user?.id === story.createdBy.id;
  const isUserTurn = user?.id === story.currentTurn;
  const wordCount = story.wordCount;

  const handleDelete = async () => {
    try {
      await onDelete(story.id);
      toast({
        title: "Story deleted",
        description: "The story has been successfully deleted.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete the story. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="book-card hover:shadow-lg transition-all duration-300">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-serif">{story.title}</CardTitle>
            <CardDescription className="flex items-center gap-1 mt-1">
              <User size={14} className="text-gray-500" />
              <span>Started by {story.createdBy.username}</span>
            </CardDescription>
          </div>
          <div>
            {story.status === "active" ? (
              <Badge className="bg-green-600 hover:bg-green-700">Active</Badge>
            ) : (
              <Badge
                variant="outline"
                className="border-gray-400 text-gray-600"
              >
                Completed
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex justify-between text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Users size={14} />
              <span>{story.participants.length} participants</span>
            </div>
            <div className="flex items-center gap-1">
              <Book size={14} />
              <span>{wordCount} words</span>
            </div>
          </div>

          <div className="h-12 overflow-hidden relative bg-literary-paper rounded p-2 text-sm">
            {story.words.slice(-10).map((wordObj, idx) => (
              <span key={idx} className="story-word">
                {wordObj.word}
                {idx !== story.words.length - 1 ? " " : ""}
              </span>
            ))}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-literary-paper/90"></div>
          </div>

          {story.status === "active" && (
            <div className="flex items-center mt-2 gap-2 text-sm">
              <Clock size={14} className="text-gray-500" />
              <span className="text-gray-600">
                {isUserTurn ? (
                  <span className="text-literary-burgundy font-medium">
                    It's your turn!
                  </span>
                ) : (
                  <span>{`${story.participants.find((p) => p.id === story.currentTurn)?.username || "Someone"}'s turn`}</span>
                )}
              </span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="text-sm text-gray-500 flex items-center">
          <CalendarDays size={14} className="mr-1" />
          Updated{" "}
          {formatDistanceToNow(new Date(story.updatedAt), { addSuffix: true })}
        </div>
        <div className="flex space-x-2">
          {isCreator && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Story</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{story.title}"? This action
                    cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          <SocialShare storyId={story.id} storyTitle={story.title} />

          <Link to={`/story/${story.id}`}>
            <Button
              variant="outline"
              className="text-literary-burgundy border-literary-burgundy hover:bg-literary-burgundy hover:text-white"
            >
              {story.status === "active" ? (
                isUserTurn ? (
                  <>
                    <Edit size={16} className="mr-2" />
                    Add Word
                  </>
                ) : (
                  "View Story"
                )
              ) : (
                "Read Story"
              )}
            </Button>
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
};

const EmptyState: React.FC<{ type: "active" | "completed" | "all" }> = ({
  type,
}) => {
  const navigate = useNavigate();

  return (
    <div className="text-center py-12 px-4">
      <Book size={48} className="mx-auto text-gray-300 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {type === "active"
          ? "No active stories yet"
          : "No completed stories yet"}
      </h3>
      <p className="text-gray-500 max-w-md mx-auto mb-6">
        {type === "active"
          ? "Create your first story or join an existing one to start your collaborative writing journey."
          : "Complete your active stories to see them here. Keep writing one word at a time!"}
      </p>
      <Button
        className="bg-literary-burgundy hover:bg-opacity-90"
        onClick={() => navigate("/create-story")}
      >
        <Plus size={16} className="mr-2" />
        Create New Story
      </Button>
    </div>
  );
};

const DashboardPage: React.FC = () => {
  const { userStories = [], loading, deleteStory } = useStory();
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeCount, setActiveCount] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  const [yourTurnCount, setYourTurnCount] = useState(0);

  // Set page title
  useDocumentTitle(createPageTitle("Dashboard"));

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }

    if (!loading && userStories) {
      setActiveCount(userStories.filter((s) => s.status === "active").length);
      setCompletedCount(
        userStories.filter((s) => s.status === "completed").length,
      );
      setYourTurnCount(
        userStories.filter((s) => s.currentTurn === user?.id).length,
      );
    }
  }, [loading, isAuthenticated, navigate, userStories, user]);

  const handleDeleteStory = async (storyId: string) => {
    try {
      const success = await deleteStory(storyId);
      if (!success) {
        toast({
          title: "Error",
          description: "Failed to delete the story. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting story:", error);
      toast({
        title: "Error",
        description: "An error occurred while deleting the story.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-literary-burgundy/20 rounded-full"></div>
          <div className="h-4 w-48 mt-4 bg-literary-burgundy/20 rounded"></div>
          <div className="h-3 w-36 mt-2 bg-literary-burgundy/10 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-serif">Your Stories</h1>
          <p className="text-gray-600 mt-1">
            Manage your collaborative writing projects
          </p>
        </div>
        <Button
          className="mt-4 md:mt-0 bg-literary-burgundy hover:bg-opacity-90"
          onClick={() => navigate("/create-story")}
        >
          <Plus size={16} className="mr-2" />
          Create New Story
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Active Stories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{activeCount}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Your Turn</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-literary-burgundy">
              {yourTurnCount}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{completedCount}</div>
          </CardContent>
        </Card>
      </div>

      {user?.tier !== "free" && (
        <div className="bg-gradient-to-r from-literary-gold/20 to-literary-cream border border-literary-gold/20 rounded-lg p-4 mb-8 flex items-center justify-between">
          <div className="flex items-center">
            <Crown className="h-5 w-5 text-literary-gold mr-2" />
            <div>
              <h3 className="font-medium">
                {user?.tier === "authors-guild"
                  ? "Author's Guild"
                  : user?.tier === "storyteller"
                    ? "Storyteller"
                    : user?.tier === "wordsmith"
                      ? "Wordsmith"
                      : user?.tier === "premium"
                        ? "Premium"
                        : user?.tier === "pro"
                          ? "Professional"
                          : "Member"}{" "}
                Subscription
              </h3>
              <p className="text-sm text-gray-600">
                Enjoy your premium features and benefits
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate("/subscription")}
            className="border-literary-gold/50 hover:bg-literary-gold/10"
          >
            Manage
          </Button>
        </div>
      )}

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="all">All Stories</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          {userStories.filter((s) => s.status === "active").length > 0 ? (
            <div className="grid grid-cols-1 gap-6">
              {userStories
                .filter((story) => story.status === "active")
                .sort((a, b) => {
                  if (a.currentTurn === user?.id && b.currentTurn !== user?.id)
                    return -1;
                  if (a.currentTurn !== user?.id && b.currentTurn === user?.id)
                    return 1;
                  return (
                    new Date(b.updatedAt).getTime() -
                    new Date(a.updatedAt).getTime()
                  );
                })
                .map((story) => (
                  <StoryCard
                    key={story.id}
                    story={story}
                    onDelete={handleDeleteStory}
                  />
                ))}
            </div>
          ) : (
            <EmptyState type="active" />
          )}
        </TabsContent>

        <TabsContent value="completed">
          {userStories.filter((s) => s.status === "completed").length > 0 ? (
            <div className="grid grid-cols-1 gap-6">
              {userStories
                .filter((story) => story.status === "completed")
                .sort(
                  (a, b) =>
                    new Date(b.updatedAt).getTime() -
                    new Date(a.updatedAt).getTime(),
                )
                .map((story) => (
                  <StoryCard
                    key={story.id}
                    story={story}
                    onDelete={handleDeleteStory}
                  />
                ))}
            </div>
          ) : (
            <EmptyState type="completed" />
          )}
        </TabsContent>

        <TabsContent value="all">
          {userStories.length > 0 ? (
            <div className="grid grid-cols-1 gap-6">
              {userStories
                .sort(
                  (a, b) =>
                    new Date(b.updatedAt).getTime() -
                    new Date(a.updatedAt).getTime(),
                )
                .map((story) => (
                  <StoryCard
                    key={story.id}
                    story={story}
                    onDelete={handleDeleteStory}
                  />
                ))}
            </div>
          ) : (
            <EmptyState type="all" />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DashboardPage;
