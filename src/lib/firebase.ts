import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAAdP6EUFJ6VKPbj3J77iEXpVNZFVnZAbM",
  authDomain: "word-by-word-story.firebaseapp.com",
  projectId: "word-by-word-story",
  storageBucket: "word-by-word-story.firebasestorage.app",
  messagingSenderId: "258658124369",
  appId: "1:258658124369:web:dc5a554a2945fdad99ae70",
  measurementId: "G-N21FTCQ07B"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Google Auth Provider
export const googleProvider = new GoogleAuthProvider();

export default app; 