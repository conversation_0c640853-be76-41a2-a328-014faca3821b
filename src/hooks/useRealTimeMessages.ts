import { useState, useEffect, useCallback, useRef } from 'react';
import { realTimeService, RealTimeEvent, ContributionEvent, UserSessionEvent } from '@/services/realTimeService';
import { useAuth } from '@/contexts/auth/hooks';
import { useToast } from '@/hooks/use-toast';

export interface RealTimeMessage {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  timestamp: Date;
  position: number;
  specialType?: 'gotcha' | 'reverse' | 'golden' | null;
  tokenCost?: number;
  isOptimistic?: boolean; // For optimistic updates
}

export interface UseRealTimeMessagesOptions {
  storyId: string;
  enabled?: boolean;
  onMessageReceived?: (message: RealTimeMessage) => void;
  onUserJoined?: (user: UserSessionEvent) => void;
  onUserLeft?: (user: UserSessionEvent) => void;
  onTypingUpdate?: (users: UserSessionEvent[]) => void;
}

export interface UseRealTimeMessagesReturn {
  messages: RealTimeMessage[];
  typingUsers: UserSessionEvent[];
  isConnected: boolean;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'error';
  sendMessage: (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => Promise<void>;
  startTyping: () => void;
  stopTyping: () => void;
  addOptimisticMessage: (message: Omit<RealTimeMessage, 'id' | 'timestamp' | 'isOptimistic'>) => string;
  removeOptimisticMessage: (optimisticId: string) => void;
  reconnect: () => void;
}

/**
 * Custom hook for managing real-time message bubbles in collaborative stories
 * Provides real-time messaging, typing indicators, and optimistic updates
 */
export const useRealTimeMessages = (options: UseRealTimeMessagesOptions): UseRealTimeMessagesReturn => {
  const {
    storyId,
    enabled = true,
    onMessageReceived,
    onUserJoined,
    onUserLeft,
    onTypingUpdate,
  } = options;

  const { user } = useAuth();
  const { toast } = useToast();

  // State management
  const [messages, setMessages] = useState<RealTimeMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<UserSessionEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [isTyping, setIsTyping] = useState(false);

  // Refs for cleanup and state management
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const optimisticMessagesRef = useRef<Map<string, RealTimeMessage>>(new Map());
  const isTypingRef = useRef<boolean>(false);

  // Convert ContributionEvent to RealTimeMessage
  const convertContributionToMessage = useCallback((contribution: ContributionEvent): RealTimeMessage => {
    return {
      id: contribution.id,
      content: contribution.content,
      author: contribution.author,
      timestamp: new Date(contribution.created_at),
      position: contribution.position,
      specialType: contribution.special_type,
      tokenCost: contribution.token_cost,
    };
  }, []);

  // Handle real-time events
  const handleRealTimeEvent = useCallback((event: RealTimeEvent) => {
    console.log('Real-time event received:', event);

    switch (event.type) {
      case 'contribution_added': {
        const newMessage = convertContributionToMessage(event.data);
        
        setMessages(prev => {
          // Remove any optimistic message that matches this real message
          const filteredOptimistic = prev.filter(msg => 
            !(msg.isOptimistic && msg.content === newMessage.content && msg.author.id === newMessage.author.id)
          );
          
          // Check if message already exists to avoid duplicates
          if (filteredOptimistic.some(msg => msg.id === newMessage.id)) {
            return filteredOptimistic;
          }
          
          // Add new message and sort by position
          const updated = [...filteredOptimistic, newMessage].sort((a, b) => a.position - b.position);
          return updated;
        });

        onMessageReceived?.(newMessage);
        break;
      }

      case 'user_joined': {
        console.log('User joined:', event.data);
        onUserJoined?.(event.data);
        break;
      }

      case 'user_left': {
        console.log('User left:', event.data);
        setTypingUsers(prev => prev.filter(u => u.userId !== event.data.userId));
        onUserLeft?.(event.data);
        break;
      }

      case 'user_typing': {
        if (event.data.userId !== user?.id) { // Don't show own typing indicator
          setTypingUsers(prev => {
            const existing = prev.find(u => u.userId === event.data.userId);
            if (existing) {
              return prev.map(u => u.userId === event.data.userId ? event.data : u);
            }
            return [...prev, event.data];
          });
        }
        break;
      }

      case 'user_stopped_typing': {
        setTypingUsers(prev => prev.filter(u => u.userId !== event.data.userId));
        break;
      }

      default:
        console.log('Unhandled real-time event:', event);
    }
  }, [convertContributionToMessage, onMessageReceived, onUserJoined, onUserLeft, user?.id]);

  // Optimistic updates
  const addOptimisticMessage = useCallback((message: Omit<RealTimeMessage, 'id' | 'timestamp' | 'isOptimistic'>) => {
    const optimisticId = `optimistic-${Date.now()}-${Math.random()}`;
    const optimisticMessage: RealTimeMessage = {
      ...message,
      id: optimisticId,
      timestamp: new Date(),
      isOptimistic: true,
    };

    optimisticMessagesRef.current.set(optimisticId, optimisticMessage);
    setMessages(prev => [...prev, optimisticMessage].sort((a, b) => a.position - b.position));
    
    return optimisticId;
  }, []);

  const removeOptimisticMessage = useCallback((optimisticId: string) => {
    optimisticMessagesRef.current.delete(optimisticId);
    setMessages(prev => prev.filter(msg => msg.id !== optimisticId));
  }, []);

  // Send message function
  const sendMessage = useCallback(async (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => {
    if (!user || !content.trim()) {
      return;
    }

    try {
      // Stop typing when sending
      await realTimeService.updateUserStatus(storyId, user.id, false);

      // Create optimistic message
      const optimisticId = addOptimisticMessage({
        content: content.trim(),
        author: {
          id: user.id,
          username: user.username || user.email?.split('@')[0] || 'Anonymous',
          avatar_url: user.profilePicture,
        },
        position: messages.length + 1, // Rough position estimate
        specialType,
        tokenCost: specialType ? (specialType === 'gotcha' ? 2 : specialType === 'reverse' ? 5 : 3) : undefined,
      });

      // Broadcast via real-time service
      await realTimeService.broadcastContribution(storyId, {
        id: `temp-${Date.now()}`, // Temporary ID
        content: content.trim(),
        author: {
          id: user.id,
          username: user.username || user.email?.split('@')[0] || 'Anonymous',
          avatar_url: user.profilePicture,
        },
        created_at: new Date().toISOString(),
        position: messages.length + 1,
        special_type: specialType,
        token_cost: specialType ? (specialType === 'gotcha' ? 2 : specialType === 'reverse' ? 5 : 3) : undefined,
        storyId,
      });

      // Remove optimistic message after a delay (real message should arrive)
      setTimeout(() => {
        removeOptimisticMessage(optimisticId);
      }, 2000);

    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: 'Failed to send message',
        description: 'Your message could not be sent. Please try again.',
        variant: 'destructive',
      });
    }
  }, [user, storyId, messages.length, toast, addOptimisticMessage, removeOptimisticMessage]);

  // Enhanced typing management with automatic timeout and presence updates
  const startTyping = useCallback(async () => {
    if (!user?.id || !enabled) return;

    try {
      // Clear any existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Only send if not already typing
      if (!isTypingRef.current) {
        await realTimeService.updateUserStatus(storyId, user.id, true, true);
        setIsTyping(true);
        isTypingRef.current = true;
      }

      // Set new timeout to stop typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(async () => {
        await stopTyping();
      }, 3000);
    } catch (error) {
      console.error('Error starting typing:', error);
    }
  }, [user?.id, enabled, storyId]);

  const stopTyping = useCallback(async () => {
    if (!user?.id || !enabled) return;

    try {
      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Only send if currently typing
      if (isTypingRef.current) {
        await realTimeService.updateUserStatus(storyId, user.id, false, true);
        setIsTyping(false);
        isTypingRef.current = false;
      }
    } catch (error) {
      console.error('Error stopping typing:', error);
    }
  }, [user?.id, enabled, storyId]);

  // Enhanced user presence tracking
  const updateUserPresence = useCallback(async (isActive: boolean = true) => {
    if (!user?.id || !enabled) return;

    try {
      await realTimeService.updateUserStatus(storyId, user.id, undefined, isActive);
    } catch (error) {
      console.error('Error updating presence:', error);
    }
  }, [user?.id, enabled, storyId]);

  // Track user activity and update presence accordingly
  useEffect(() => {
    if (!enabled || !user?.id) return;

    let activityTimeout: NodeJS.Timeout;
    let isActiveState = true;

    const handleActivity = () => {
      // Clear existing timeout
      if (activityTimeout) {
        clearTimeout(activityTimeout);
      }

      // If user was inactive, mark as active again
      if (!isActiveState) {
        isActiveState = true;
        updateUserPresence(true);
      }

      // Set timeout to mark as inactive after 5 minutes
      activityTimeout = setTimeout(() => {
        isActiveState = false;
        updateUserPresence(false);
      }, 5 * 60 * 1000); // 5 minutes
    };

    // Listen for user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Initial activity
    handleActivity();

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
      
      if (activityTimeout) {
        clearTimeout(activityTimeout);
      }
    };
  }, [enabled, user?.id, updateUserPresence]);

  // Connection management
  const connect = useCallback(async () => {
    if (!enabled || !storyId || !user) {
      return;
    }

    try {
      setConnectionState('connecting');

      // Register user session
      await realTimeService.registerUserSession(storyId, user.id, user.username || user.email?.split('@')[0]);

      // Subscribe to real-time updates
      const unsubscribe = realTimeService.subscribeToStory(storyId, handleRealTimeEvent);
      unsubscribeRef.current = unsubscribe;

      setIsConnected(true);
      setConnectionState('connected');

      console.log(`Connected to real-time messages for story: ${storyId}`);
    } catch (error) {
      console.error('Failed to connect to real-time messages:', error);
      setConnectionState('error');
      toast({
        title: 'Connection Error',
        description: 'Failed to connect to real-time updates. Some features may not work properly.',
        variant: 'destructive',
      });
    }
  }, [enabled, storyId, user, handleRealTimeEvent, toast]);

  const disconnect = useCallback(async () => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    if (user && storyId) {
      try {
        await realTimeService.updateUserStatus(storyId, user.id, false, false);
      } catch (error) {
        console.error('Error updating user status on disconnect:', error);
      }
    }

    setIsConnected(false);
    setConnectionState('disconnected');
    setTypingUsers([]);
  }, [user, storyId]);

  const reconnect = useCallback(async () => {
    await disconnect();
    await connect();
  }, [disconnect, connect]);

  // Effect for connection management
  useEffect(() => {
    if (enabled && storyId && user) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, storyId, user?.id]); // Reduced dependencies to avoid reconnection loops

  // Update typing users callback
  useEffect(() => {
    onTypingUpdate?.(typingUsers);
  }, [typingUsers, onTypingUpdate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    messages,
    typingUsers,
    isConnected,
    connectionState,
    sendMessage,
    startTyping,
    stopTyping,
    addOptimisticMessage,
    removeOptimisticMessage,
    reconnect,
  };
}; 