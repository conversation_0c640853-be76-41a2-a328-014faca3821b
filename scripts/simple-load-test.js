/**
 * Simplified Load Testing Scenario
 * Tests real-time messaging and collaborative features under load
 */

console.log('🧪 Starting Load Testing Suite');
console.log('=' .repeat(60));

// Test configuration
const TEST_SCENARIOS = [
  { name: "Light Load", users: 10, duration: 5, messagesPerUser: 3 },
  { name: "Medium Load", users: 25, duration: 10, messagesPerUser: 5 },
  { name: "Heavy Load", users: 50, duration: 15, messagesPerUser: 8 },
];

// Performance thresholds
const THRESHOLDS = {
  messageLatency: 500,    // ms
  connectionTime: 2000,   // ms
  memoryGrowth: 50,       // MB
};

// Virtual user simulator
function createVirtualUser(id, scenario) {
  return {
    id: id,
    messagesSent: 0,
    connectionTime: 0,
    actions: [],
    
    async startSession() {
      const startTime = performance.now();
      
      // Simulate connection setup
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
      this.connectionTime = performance.now() - startTime;
      
      // Simulate user activity
      await this.simulateActivity(scenario);
      
      return { 
        success: true, 
        connectionTime: this.connectionTime,
        messagesSent: this.messagesSent 
      };
    },
    
    async simulateActivity(scenario) {
      const activityDuration = scenario.duration * 1000; // Convert to ms
      const messageInterval = activityDuration / scenario.messagesPerUser;
      
      for (let i = 0; i < scenario.messagesPerUser; i++) {
        const actionStart = performance.now();
        
        // Simulate message sending
        await this.sendMessage();
        
        const actionTime = performance.now() - actionStart;
        this.actions.push({ type: 'message', time: actionTime });
        
        // Wait between messages
        if (i < scenario.messagesPerUser - 1) {
          await new Promise(resolve => setTimeout(resolve, messageInterval + Math.random() * 500));
        }
      }
    },
    
    async sendMessage() {
      // Simulate message composition and network delay
      const networkLatency = Math.random() * 200 + 50; // 50-250ms
      await new Promise(resolve => setTimeout(resolve, networkLatency));
      this.messagesSent++;
    },
    
    getStats() {
      const avgActionTime = this.actions.length > 0 
        ? this.actions.reduce((sum, action) => sum + action.time, 0) / this.actions.length 
        : 0;
        
      return {
        id: this.id,
        connectionTime: this.connectionTime,
        messagesSent: this.messagesSent,
        totalActions: this.actions.length,
        averageActionTime: avgActionTime
      };
    }
  };
}

// Load test runner
async function runLoadTest(scenario) {
  console.log(`\n🚀 Starting ${scenario.name}`);
  console.log(`├─ Users: ${scenario.users}`);
  console.log(`├─ Duration: ${scenario.duration}s`);
  console.log(`├─ Messages per user: ${scenario.messagesPerUser}`);
  
  const startTime = performance.now();
  const startMemory = process.memoryUsage();
  
  // Create virtual users
  const users = [];
  for (let i = 0; i < scenario.users; i++) {
    users.push(createVirtualUser(`user-${i}`, scenario));
  }
  
  // Start all users concurrently
  console.log('├─ Starting user sessions...');
  const sessionPromises = users.map(user => user.startSession());
  const sessionResults = await Promise.allSettled(sessionPromises);
  
  // Calculate results
  const endTime = performance.now();
  const endMemory = process.memoryUsage();
  const testDuration = endTime - startTime;
  
  const successfulSessions = sessionResults.filter(r => 
    r.status === 'fulfilled' && r.value.success
  ).length;
  
  const userStats = users.map(user => user.getStats());
  const totalMessages = userStats.reduce((sum, stats) => sum + stats.messagesSent, 0);
  const avgConnectionTime = userStats.reduce((sum, stats) => sum + stats.connectionTime, 0) / userStats.length;
  const avgActionTime = userStats.reduce((sum, stats) => sum + stats.averageActionTime, 0) / userStats.length;
  
  const memoryGrowth = (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024; // MB
  
  // Check thresholds
  const violations = [];
  if (avgActionTime > THRESHOLDS.messageLatency) {
    violations.push(`Message latency: ${avgActionTime.toFixed(2)}ms > ${THRESHOLDS.messageLatency}ms`);
  }
  if (avgConnectionTime > THRESHOLDS.connectionTime) {
    violations.push(`Connection time: ${avgConnectionTime.toFixed(2)}ms > ${THRESHOLDS.connectionTime}ms`);
  }
  if (memoryGrowth > THRESHOLDS.memoryGrowth) {
    violations.push(`Memory growth: ${memoryGrowth.toFixed(2)}MB > ${THRESHOLDS.memoryGrowth}MB`);
  }
  
  // Print results
  const status = violations.length === 0 ? '✅ PASS' : '⚠️ WARNINGS';
  console.log(`\n${status} ${scenario.name} Results:`);
  console.log(`├─ Duration: ${(testDuration / 1000).toFixed(1)}s`);
  console.log(`├─ Successful sessions: ${successfulSessions}/${scenario.users}`);
  console.log(`├─ Total messages: ${totalMessages}`);
  console.log(`├─ Avg connection time: ${avgConnectionTime.toFixed(2)}ms`);
  console.log(`├─ Avg message time: ${avgActionTime.toFixed(2)}ms`);
  console.log(`├─ Memory growth: ${memoryGrowth.toFixed(2)}MB`);
  
  if (violations.length > 0) {
    console.log(`└─ ⚠️ Threshold violations:`);
    violations.forEach(violation => {
      console.log(`   └─ ${violation}`);
    });
  } else {
    console.log(`└─ ✅ All thresholds met`);
  }
  
  return {
    scenario: scenario.name,
    passed: violations.length === 0,
    users: scenario.users,
    successfulSessions,
    totalMessages,
    avgConnectionTime,
    avgActionTime,
    memoryGrowth,
    violations
  };
}

// Run all test scenarios
async function runAllTests() {
  const results = [];
  
  for (const scenario of TEST_SCENARIOS) {
    try {
      const result = await runLoadTest(scenario);
      results.push(result);
      
      // Cool down between tests
      console.log('\n⏸️ Cool down period...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`❌ Test ${scenario.name} failed:`, error.message);
      results.push({ scenario: scenario.name, passed: false, error: error.message });
    }
  }
  
  // Print overall summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 LOAD TESTING SUMMARY');
  console.log('='.repeat(60));
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const maxUsers = Math.max(...results.filter(r => r.users).map(r => r.users));
  const totalMessages = results.reduce((sum, r) => sum + (r.totalMessages || 0), 0);
  
  console.log(`Tests Run: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Success Rate: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  console.log(`Max Concurrent Users: ${maxUsers}`);
  console.log(`Total Messages Processed: ${totalMessages}`);
  
  // Performance rating
  const successRate = passedTests / totalTests;
  let performanceRating;
  if (successRate >= 1.0) performanceRating = 'EXCELLENT 🏅';
  else if (successRate >= 0.75) performanceRating = 'GOOD 👍';
  else if (successRate >= 0.5) performanceRating = 'ACCEPTABLE ⚠️';
  else performanceRating = 'NEEDS IMPROVEMENT 🔧';
  
  console.log(`\n🏆 Performance Rating: ${performanceRating}`);
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  const hasMemoryIssues = results.some(r => r.memoryGrowth > THRESHOLDS.memoryGrowth);
  const hasLatencyIssues = results.some(r => r.avgActionTime > THRESHOLDS.messageLatency);
  
  if (hasMemoryIssues) {
    console.log('🔧 Monitor memory usage - consider message cleanup strategies');
  }
  if (hasLatencyIssues) {
    console.log('⚡ Optimize message processing - check network and database performance');
  }
  if (!hasMemoryIssues && !hasLatencyIssues) {
    console.log('✅ Performance is excellent - ready for production scale');
  }
  
  console.log('📈 Consider implementing auto-scaling for peak loads');
  console.log('📊 Set up real-time performance monitoring in production');
  
  return results;
}

// Execute load tests
runAllTests()
  .then(() => {
    console.log('\n🎉 Load testing completed successfully!');
  })
  .catch((error) => {
    console.error('\n❌ Load testing failed:', error);
  }); 