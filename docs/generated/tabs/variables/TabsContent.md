[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [tabs](../README.md) / TabsContent

# Variable: TabsContent

> `const` **TabsContent**: `ForwardRefExoticComponent`\<`Omit`\<`TabsContentProps` & `RefAttributes`\<`HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/tabs.tsx:38](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/tabs.tsx#L38)
