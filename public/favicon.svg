<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <style>
      .book-cover { fill: #7D1D3F; }
      .book-spine { fill: #5A142E; }
      .book-pages { fill: #F8F0E3; }
      .page-lines { fill: #E8D8CB; }
    </style>
  </defs>
  
  <!-- Book cover -->
  <rect x="6" y="6" width="20" height="20" rx="2" class="book-cover"/>
  
  <!-- Book spine shadow -->
  <rect x="6" y="6" width="3" height="20" rx="2" class="book-spine"/>
  
  <!-- Pages -->
  <rect x="9" y="8" width="15" height="16" class="book-pages"/>
  
  <!-- Page lines for detail -->
  <line x1="11" y1="11" x2="22" y2="11" stroke="#E8D8CB" stroke-width="0.5"/>
  <line x1="11" y1="13" x2="20" y2="13" stroke="#E8D8CB" stroke-width="0.5"/>
  <line x1="11" y1="15" x2="21" y2="15" stroke="#E8D8CB" stroke-width="0.5"/>
  <line x1="11" y1="17" x2="19" y2="17" stroke="#E8D8CB" stroke-width="0.5"/>
  <line x1="11" y1="19" x2="22" y2="19" stroke="#E8D8CB" stroke-width="0.5"/>
  <line x1="11" y1="21" x2="20" y2="21" stroke="#E8D8CB" stroke-width="0.5"/>
</svg> 