/// <reference types="vitest" />
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
    // Better test timeout for integration tests
    testTimeout: 10000,
    // Environment variables for testing
    env: {
      NODE_ENV: 'test',
      VITE_SUPABASE_URL: 'https://test.supabase.co',
      VITE_SUPABASE_ANON_KEY: 'test-key',
      VITE_POSTHOG_KEY: 'phc_6EV53f87cfKaGCfz6ypuOUeuVHyqoc5P3XrLb1G8q3J',
      VITE_POSTHOG_HOST: 'https://us.i.posthog.com',
      VITE_POSTHOG_DISABLE_IN_DEV: 'false',
    },
    css: {
      modules: {
        classNameStrategy: 'non-scoped'
      }
    },
    // Better error reporting
    reporters: ['verbose'],
    // Allow tests to run in parallel but limit concurrency for stability
    pool: 'threads',
    poolOptions: {
      threads: {
        maxThreads: 4,
        minThreads: 1,
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
});