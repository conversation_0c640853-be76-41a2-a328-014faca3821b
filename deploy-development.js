// Script to deploy to development environment
import { execSync } from "child_process";

console.log("Deploying to development environment...");

try {
  // Generate Prisma client
  console.log("Generating Prisma client...");
  execSync("npx prisma generate", { stdio: "inherit" });

  // Deploy to Vercel development
  console.log("Deploying to Vercel development...");
  execSync("vercel --env development", { stdio: "inherit" });

  console.log("Deployment to development completed successfully!");
  console.log(
    "Note: Database migrations will be run automatically during the build process.",
  );
} catch (error) {
  console.error("Error deploying to development:", error);
  process.exit(1);
}
