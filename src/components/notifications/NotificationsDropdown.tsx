import React from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import NotificationItem from './NotificationItem';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { X } from 'lucide-react';

interface NotificationsDropdownProps {
  onClose: () => void;
}

const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({ onClose }) => {
  const { notifications, unreadCount, markAllAsRead, loading } = useNotifications();

  return (
    <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-white border border-gray-200 rounded-lg shadow-xl z-50 dark:bg-gray-800 dark:border-gray-700">
      <div className="flex justify-between items-center p-3 border-b dark:border-gray-600">
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200">Notifications</h3>
        <Button variant="ghost" size="sm" onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
          <X className="h-4 w-4" />
        </Button>
      </div>

      {unreadCount > 0 && (
        <div className="p-2 border-b dark:border-gray-600">
          <Button variant="link" size="sm" onClick={markAllAsRead} className="w-full text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            Mark all as read
          </Button>
        </div>
      )}

      <ScrollArea className="h-[300px] sm:h-[400px]">
        {loading && notifications.length === 0 && <p className="p-4 text-center text-gray-500 dark:text-gray-400">Loading...</p>}
        {!loading && notifications.length === 0 && (
          <p className="p-4 text-center text-gray-500 dark:text-gray-400">You have no notifications.</p>
        )}
        {notifications.map((notification, index) => (
          <React.Fragment key={notification.id}>
            <NotificationItem notification={notification} onCloseDropdown={onClose} />
            {index < notifications.length - 1 && <Separator className="dark:bg-gray-700" />}
          </React.Fragment>
        ))}
      </ScrollArea>
      {/* Optional: Link to a full notifications page */}
      {/* <div className="p-2 border-t dark:border-gray-600 text-center">
        <Button variant="link" size="sm" asChild>
          <a href="/notifications" onClick={onClose}>View all notifications</a>
        </Button>
      </div> */}
    </div>
  );
};

export default NotificationsDropdown; 