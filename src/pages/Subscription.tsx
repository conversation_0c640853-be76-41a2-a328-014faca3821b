import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  Sparkles,
  Star,
  Shield,
  Crown,
  Zap,
  Wand2,
  Award,
  Rocket,
} from "lucide-react";
import { useSubscription } from "@/hooks/use-subscription";
import { toast } from "@/components/ui/use-toast";
import { RotatingAdvertisement } from "@/components/ads/RotatingAdvertisement";
import { useAds } from "@/contexts/AdsContext";

const CreditsPage: React.FC = () => {
  const { user } = useAuth();
  const { isAdFreeUser } = useAds();
  const navigate = useNavigate();
  const {
    handleBuyCredits,
    checkCredits,
    isLoading,
    credits,
    userTier,
    SUBSCRIPTION_TIERS,
  } = useSubscription();

  useEffect(() => {
    if (user) {
      checkCredits();
    }
  }, [user]);

  const getTierCardClass = (tier: string) => {
    if (userTier === tier) {
      return "border-literary-gold bg-amber-50";
    }
    return "border-gray-200";
  };

  const handlePurchaseAttempt = () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase credits",
        variant: "destructive",
      });
      navigate("/login");
      return;
    }
  };

  return (
    <div className="max-w-6xl mx-auto pb-16">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-serif mb-3">Subscription & Credits</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Enhance your collaborative storytelling experience with premium
          features and AI-powered tools
        </p>
      </div>

      {(!user || !isAdFreeUser) && (
        <RotatingAdvertisement placement="header" className="mb-6 max-w-4xl mx-auto" />
      )}

      {user && (
        <div className="mb-8 flex justify-center">
          <Card className="w-full max-w-md border-literary-gold/30 shadow-md">
            <CardHeader className="pb-3 border-b border-gray-100">
              <CardTitle className="flex items-center justify-between">
                <span className="text-literary-navy text-xl">Your Credits</span>
                <span className="text-literary-gold font-bold text-3xl">
                  {credits}
                </span>
              </CardTitle>
              <CardDescription className="text-gray-600">
                Use credits for AI features and special story elements
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <p className="text-sm text-gray-700">
                Credits can be used for AI-powered suggestions, paragraph
                generation, and special story features.
              </p>
              <div className="mt-3 flex justify-end">
                <Button
                  onClick={() => navigate("/subscription#credit-options")}
                  size="sm"
                  className="bg-literary-gold hover:bg-literary-gold/90 text-white"
                >
                  <Crown className="h-4 w-4 mr-1" />
                  Buy More Credits
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="max-w-5xl mx-auto mb-8">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-serif">Choose Your Membership</h2>
          <p className="text-gray-600">
            Unlock more stories and collaborate with more writers
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card className={getTierCardClass("free")}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-gray-400" />
                Free
              </CardTitle>
              <div className="text-2xl font-bold">$0</div>
              <CardDescription>Get started for free</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">3</span> active stories
              </p>
              <p className="text-sm">
                <span className="font-medium">3</span> collaborators per story
              </p>
              <p className="text-sm">Basic story templates</p>
              <p className="text-sm">10 starter credits</p>
            </CardContent>
            <CardFooter>
              {user ? (
                userTier === "free" ? (
                  <Button disabled className="w-full bg-gray-300">
                    Current Plan
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full">
                    Downgrade
                  </Button>
                )
              ) : (
                <Button onClick={handlePurchaseAttempt} className="w-full">
                  Sign In To Choose
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card className={getTierCardClass("microtier")}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-500" />
                Writer
              </CardTitle>
              <div className="text-2xl font-bold">
                $4.99<span className="text-sm font-normal">/mo</span>
              </div>
              <CardDescription>For casual storytellers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">5</span> active stories
              </p>
              <p className="text-sm">
                <span className="font-medium">5</span> collaborators per story
              </p>
              <p className="text-sm">Basic story templates</p>
              <p className="text-sm">25 monthly credits</p>
              <p className="text-sm">Ad-free experience</p>
            </CardContent>
            <CardFooter>
              {user ? (
                userTier === "microtier" ? (
                  <Button disabled className="w-full bg-gray-300">
                    Current Plan
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full">
                    {userTier === "free" ? "Upgrade" : "Downgrade"}
                  </Button>
                )
              ) : (
                <Button onClick={handlePurchaseAttempt} className="w-full">
                  Sign In To Choose
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card className={getTierCardClass("wordsmith")}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5 text-indigo-500" />
                Wordsmith
              </CardTitle>
              <div className="text-2xl font-bold">
                $9.99<span className="text-sm font-normal">/mo</span>
              </div>
              <CardDescription>For dedicated writers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">10</span> active stories
              </p>
              <p className="text-sm">
                <span className="font-medium">5</span> collaborators per story
              </p>
              <p className="text-sm">All story templates</p>
              <p className="text-sm">50 monthly credits</p>
              <p className="text-sm">Ad-free experience</p>
              <p className="text-sm">Basic AI word suggestions</p>
            </CardContent>
            <CardFooter>
              {user ? (
                userTier === "wordsmith" ? (
                  <Button disabled className="w-full bg-gray-300">
                    Current Plan
                  </Button>
                ) : (
                  <Button variant="outline" className="w-full">
                    {["free", "microtier"].includes(userTier)
                      ? "Upgrade"
                      : "Downgrade"}
                  </Button>
                )
              ) : (
                <Button onClick={handlePurchaseAttempt} className="w-full">
                  Sign In To Choose
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card className={getTierCardClass("storyteller")}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-purple-500" />
                Storyteller
              </CardTitle>
              <div className="text-2xl font-bold">
                $19.99<span className="text-sm font-normal">/mo</span>
              </div>
              <CardDescription>For serious authors</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">20</span> active stories
              </p>
              <p className="text-sm">
                <span className="font-medium">10</span> collaborators per story
              </p>
              <p className="text-sm">All story templates</p>
              <p className="text-sm">100 monthly credits</p>
              <p className="text-sm">Ad-free experience</p>
              <p className="text-sm">Advanced AI word suggestions</p>
              <p className="text-sm font-medium text-literary-burgundy">
                ✨ Premium AI cover art
              </p>
              <p className="text-sm font-medium text-literary-burgundy">
                ✨ AI synopsis generation
              </p>
            </CardContent>
            <CardFooter>
              {user ? (
                userTier === "storyteller" ? (
                  <Button disabled className="w-full bg-gray-300">
                    Current Plan
                  </Button>
                ) : (
                  <Button className="w-full bg-literary-burgundy hover:bg-literary-burgundy/90 text-white">
                    {["free", "microtier", "wordsmith"].includes(userTier)
                      ? "Upgrade"
                      : "Downgrade"}
                  </Button>
                )
              ) : (
                <Button
                  onClick={handlePurchaseAttempt}
                  className="w-full bg-literary-burgundy hover:bg-literary-burgundy/90 text-white"
                >
                  Sign In To Choose
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card className={getTierCardClass("authors-guild")}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Rocket className="h-5 w-5 text-literary-gold" />
                Authors Guild
              </CardTitle>
              <div className="text-2xl font-bold">
                $29.99<span className="text-sm font-normal">/mo</span>
              </div>
              <CardDescription>For professional writers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">50</span> active stories
              </p>
              <p className="text-sm">
                <span className="font-medium">Unlimited</span> collaborators
              </p>
              <p className="text-sm">All story templates</p>
              <p className="text-sm">200 monthly credits</p>
              <p className="text-sm">Ad-free experience</p>
              <p className="text-sm">Advanced AI word suggestions</p>
              <p className="text-sm font-medium text-literary-burgundy">
                ✨ Premium AI cover art
              </p>
              <p className="text-sm font-medium text-literary-burgundy">
                ✨ AI synopsis generation
              </p>
              <p className="text-sm font-medium text-literary-gold">
                🏆 Story publishing tools
              </p>
              <p className="text-sm font-medium text-literary-gold">
                🏆 Export to e-book formats
              </p>
            </CardContent>
            <CardFooter>
              {user ? (
                userTier === "authors-guild" ? (
                  <Button disabled className="w-full bg-gray-300">
                    Current Plan
                  </Button>
                ) : (
                  <Button className="w-full bg-literary-gold hover:bg-literary-gold/90 text-white">
                    Upgrade
                  </Button>
                )
              ) : (
                <Button
                  onClick={handlePurchaseAttempt}
                  className="w-full bg-literary-gold hover:bg-literary-gold/90 text-white"
                >
                  Sign In To Choose
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>

      <div className="max-w-4xl mx-auto" id="credit-options">
        {user ? (
          <>
            {!isAdFreeUser && (
              <Card className="mb-6 border-green-200">
                <CardHeader className="bg-green-50">
                  <CardTitle className="flex items-center">
                    <Shield className="mr-2 text-green-600" />
                    Remove Ads
                  </CardTitle>
                  <CardDescription>
                    Enjoy a cleaner, distraction-free storytelling experience
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <p className="text-sm">
                    Purchase our ad-free option for a one-time payment and
                    permanently remove all advertisements from your account.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={() => navigate("/ad-free")}
                    variant="outline"
                    className="border-green-200 hover:bg-green-50"
                  >
                    Learn More
                  </Button>
                </CardFooter>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="text-literary-gold mr-2" />
                  AI Enhancement Credits
                </CardTitle>
                <CardDescription>
                  Purchase credits to access AI-powered features on demand
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-literary-gold" />
                      Word Suggestion Credits
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm text-gray-600">
                      Get AI suggestions for your next word based on story
                      context
                    </p>
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">20 credits</span>
                        <span className="text-lg font-bold">$0.99</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">100 credits</span>
                        <span className="text-lg font-bold">$3.99</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">500 credits</span>
                        <span className="text-lg font-bold">$14.99</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="grid grid-cols-3 gap-2 w-full">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleBuyCredits("Word Suggestion", 20, 99)
                        }
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "20"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleBuyCredits("Word Suggestion", 100, 399)
                        }
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "100"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleBuyCredits("Word Suggestion", 500, 1499)
                        }
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "500"
                        )}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium flex items-center gap-2">
                      <Star className="h-4 w-4 text-literary-gold" />
                      Cover Art Credits
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm text-gray-600">
                      Generate stunning custom cover art for your stories with
                      our premium AI generation
                    </p>
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Basic Cover (5 credits)</span>
                        <span className="text-lg font-bold">$1.49</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">
                          Premium Cover (10 credits)
                        </span>
                        <span className="text-lg font-bold">$2.99</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Cover Pack (50 credits)</span>
                        <span className="text-lg font-bold">$12.99</span>
                      </div>
                    </div>
                    <div className="mt-2 bg-amber-50 p-2 rounded-md">
                      <p className="text-xs text-amber-700">
                        <span className="font-medium">
                          ✨ Premium Feature:{" "}
                        </span>
                        Our AI cover art uses advanced algorithms to create
                        unique, publishable-quality artwork for your stories
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="grid grid-cols-3 gap-2 w-full">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("Cover Art", 5, 149)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "Basic"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("Cover Art", 10, 299)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "Premium"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("Cover Art", 50, 1299)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "Pack"
                        )}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base font-medium flex items-center gap-2">
                      <Sparkles className="h-4 w-4 text-literary-gold" />
                      Special Features
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm text-gray-600">
                      Use credits for special story features like "Gotcha" words
                      and AI-assisted writing
                    </p>
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">
                          Gotcha Word (1 credit each)
                        </span>
                        <span className="text-sm text-gray-500">
                          Charged per use
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">General Credits</span>
                        <span className="text-lg font-bold">$0.99</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">100 General Credits</span>
                        <span className="text-lg font-bold">$3.99</span>
                      </div>
                    </div>
                    <div className="mt-2 bg-amber-50 p-2 rounded-md">
                      <p className="text-xs text-amber-700">
                        <span className="font-medium">
                          ✨ Try our AI Synopsis:{" "}
                        </span>
                        Create professional book descriptions with our magical
                        AI synopsis generator (Available in Storyteller plan)
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <div className="grid grid-cols-3 gap-2 w-full">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("General", 20, 99)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "20"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("General", 100, 399)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "100"
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleBuyCredits("General", 500, 1499)}
                        disabled={isLoading}
                        className="text-xs"
                      >
                        {isLoading ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          "500"
                        )}
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card className="border-literary-gold/30">
            <CardHeader className="bg-literary-gold/5">
              <CardTitle className="flex items-center">
                <Star className="text-literary-gold mr-2" />
                AI Enhancement Credits
              </CardTitle>
              <CardDescription>
                Sign in to purchase credits and access AI-powered features
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="text-center space-y-4">
                <p className="text-sm text-gray-600">
                  Our credits system lets you pay only for the AI features you
                  use, when you need them.
                </p>
                <p className="text-sm text-gray-600">
                  Create an account to get 10 free starter credits and start
                  enhancing your stories!
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button
                onClick={() => navigate("/login")}
                className="bg-literary-gold hover:bg-literary-gold/90"
              >
                Sign In to Purchase
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>

      <div className="mt-6 bg-literary-paper p-4 rounded-lg border border-literary-gold/30 text-sm text-literary-navy">
        <h3 className="font-medium mb-2">About Credits</h3>
        <p className="mb-2">
          Credits can be used across different features in the app. Credits are
          non-refundable and do not expire.
        </p>
        <p className="mb-2">
          <strong>Gotcha Words:</strong> 1 credit per use - Make the next
          player's turn more challenging!
        </p>
        <p className="mb-2">
          <strong>AI Word Suggestions:</strong> 1 credit per use - Get
          intelligent suggestions for your next word.
        </p>
        <p className="mb-2">
          <strong>AI Paragraph Generation:</strong> 10 credits per use -
          Generate an entire paragraph with AI.
        </p>
        <p className="mb-2">
          <strong>AI Cover Art:</strong> 5-10 credits per use - Our magical AI
          creates stunning cover art that matches your story's theme and mood.
          Premium covers include enhanced details and commercial usage rights.
        </p>
        <p>
          <strong>AI Synopsis Generation:</strong> 3 credits per use - Available
          in Storyteller and Authors Guild plans, this feature creates
          professional-quality descriptions that capture the essence of your
          story.
        </p>
      </div>

      {(!user || !isAdFreeUser) && (
        <RotatingAdvertisement placement="footer" className="mt-8 max-w-4xl mx-auto" />
      )}
    </div>
  );
};

export default CreditsPage;
