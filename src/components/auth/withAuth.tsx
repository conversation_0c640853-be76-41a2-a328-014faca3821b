import React from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';

const withAuth = <P extends object>(WrappedComponent: React.ComponentType<P>): React.FC<P> => {
  const AuthComponent: React.FC<P> = (props) => {
    const { user, loading } = useAuth();
    const router = useRouter();

    if (loading) {
      return <p>Loading...</p>; // Or a spinner component
    }

    if (!user) {
      // It's better to handle redirection client-side after initial render to avoid SSR issues
      // or flashes of protected content if loading state isn't perfectly synced.
      if (typeof window !== 'undefined') {
        router.push('/signin'); // Assuming '/signin' is your login page route
      }
      return null; // Or a loading/redirecting message, or a redirect component
    }

    return <WrappedComponent {...props} />;
  };
  return AuthComponent;
};

export default withAuth; 