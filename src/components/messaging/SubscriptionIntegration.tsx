import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import {
  Crown,
  Star,
  Users,
  Zap,
  Shield,
  Download,
  Eye,
  Coins,
  Gift,
  Clock,
  CheckCircle,
  AlertCircle,
  Sparkles,
} from 'lucide-react';
import { useAuth } from '@/contexts/auth/hooks';
import { useToast } from '@/hooks/use-toast';

export interface SubscriptionTier {
  id: 'free' | 'premium' | 'pro';
  name: string;
  description: string;
  price: number;
  billingCycle: 'monthly' | 'annual';
  features: string[];
  tokensIncluded: number;
  maxStoriesPerMonth: number;
  prioritySupport: boolean;
  noAds: boolean;
  exportFeatures: boolean;
  customThemes: boolean;
  advancedAnalytics: boolean;
  icon: typeof Crown;
  color: string;
  gradient: string;
}

export interface UserSubscription {
  isActive: boolean;
  tier: SubscriptionTier['id'];
  startDate: string;
  nextBillingDate?: string;
  cancelAtPeriodEnd?: boolean;
  trialEndsAt?: string;
  tokensUsedThisMonth: number;
  storiesCreatedThisMonth: number;
}

export interface SubscriptionIntegrationProps {
  onUpgrade?: (tier: SubscriptionTier['id']) => void;
  onManageSubscription?: () => void;
  onCancelSubscription?: () => void;
  showUpgradePrompts?: boolean;
  className?: string;
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Perfect for getting started',
    price: 0,
    billingCycle: 'monthly',
    features: [
      '10 tokens per month',
      'Create 3 stories per month',
      'Basic collaboration',
      'Community support',
    ],
    tokensIncluded: 10,
    maxStoriesPerMonth: 3,
    prioritySupport: false,
    noAds: false,
    exportFeatures: false,
    customThemes: false,
    advancedAnalytics: false,
    icon: Users,
    color: 'text-gray-600',
    gradient: 'from-gray-400 to-gray-600',
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'For active storytellers',
    price: 9.99,
    billingCycle: 'monthly',
    features: [
      '100 tokens per month',
      'Unlimited stories',
      'Advanced collaboration',
      'No ads',
      'Export features',
      'Priority support',
    ],
    tokensIncluded: 100,
    maxStoriesPerMonth: -1, // Unlimited
    prioritySupport: true,
    noAds: true,
    exportFeatures: true,
    customThemes: false,
    advancedAnalytics: false,
    icon: Crown,
    color: 'text-blue-600',
    gradient: 'from-blue-400 to-blue-600',
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For professional writers',
    price: 19.99,
    billingCycle: 'monthly',
    features: [
      '250 tokens per month',
      'Unlimited stories',
      'Advanced collaboration',
      'No ads',
      'All export features',
      'Custom themes',
      'Advanced analytics',
      'Priority support',
      'Early access to features',
    ],
    tokensIncluded: 250,
    maxStoriesPerMonth: -1, // Unlimited
    prioritySupport: true,
    noAds: true,
    exportFeatures: true,
    customThemes: true,
    advancedAnalytics: true,
    icon: Star,
    color: 'text-purple-600',
    gradient: 'from-purple-400 to-purple-600',
  },
];

export const SubscriptionIntegration: React.FC<SubscriptionIntegrationProps> = ({
  onUpgrade,
  onManageSubscription,
  onCancelSubscription,
  showUpgradePrompts = true,
  className,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Mock subscription data (replace with actual API call)
  const [userSubscription, setUserSubscription] = useState<UserSubscription>({
    isActive: true,
    tier: 'premium',
    startDate: '2024-01-01',
    nextBillingDate: '2024-02-01',
    cancelAtPeriodEnd: false,
    tokensUsedThisMonth: 45,
    storiesCreatedThisMonth: 7,
  });

  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  // Get current tier information
  const currentTier = subscriptionTiers.find(tier => tier.id === userSubscription.tier) || subscriptionTiers[0];
  const isOnFreeTier = userSubscription.tier === 'free';

  // Calculate usage percentages
  const tokenUsagePercentage = (userSubscription.tokensUsedThisMonth / currentTier.tokensIncluded) * 100;
  const storyUsagePercentage = currentTier.maxStoriesPerMonth > 0 
    ? (userSubscription.storiesCreatedThisMonth / currentTier.maxStoriesPerMonth) * 100 
    : 0;

  // Check if user is approaching limits
  const isApproachingTokenLimit = tokenUsagePercentage > 80;
  const isApproachingStoryLimit = storyUsagePercentage > 80;

  // Handle upgrade
  const handleUpgrade = useCallback((tierId: SubscriptionTier['id']) => {
    if (onUpgrade) {
      onUpgrade(tierId);
    } else {
      toast({
        title: 'Upgrade initiated',
        description: `Upgrading to ${subscriptionTiers.find(t => t.id === tierId)?.name} plan...`,
      });
    }
    setShowUpgradeDialog(false);
  }, [onUpgrade, toast]);

  // Handle subscription management
  const handleManageSubscription = useCallback(() => {
    if (onManageSubscription) {
      onManageSubscription();
    } else {
      toast({
        title: 'Subscription Management',
        description: 'Opening subscription management portal...',
      });
    }
  }, [onManageSubscription, toast]);

  // Upgrade prompt component
  const UpgradePrompt: React.FC<{ feature: string; requiredTier: SubscriptionTier['id'] }> = ({ feature, requiredTier }) => {
    const requiredTierInfo = subscriptionTiers.find(t => t.id === requiredTier);
    if (!requiredTierInfo || userSubscription.tier !== 'free') return null;

    return (
      <Alert className="mb-4">
        <Sparkles className="h-4 w-4" />
        <AlertTitle>Upgrade to unlock {feature}</AlertTitle>
        <AlertDescription className="mt-2">
          <p className="mb-3">
            {feature} is available with {requiredTierInfo.name} plan and above.
          </p>
          <Button
            size="sm"
            onClick={() => setShowUpgradeDialog(true)}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
          >
            <Crown className="w-4 h-4 mr-2" />
            Upgrade Now
          </Button>
        </AlertDescription>
      </Alert>
    );
  };

  // Subscription status display
  const SubscriptionStatus: React.FC = () => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={cn(
              'p-2 rounded-lg bg-gradient-to-r',
              currentTier.gradient
            )}>
              <currentTier.icon className="w-5 h-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg">{currentTier.name} Plan</CardTitle>
              <CardDescription>{currentTier.description}</CardDescription>
            </div>
          </div>
          <Badge variant={userSubscription.isActive ? 'default' : 'secondary'}>
            {userSubscription.isActive ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {/* Usage Statistics */}
        <div className="space-y-4">
          {/* Token Usage */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Coins className="w-4 h-4 text-amber-600" />
                <span className="text-sm font-medium">Tokens this month</span>
              </div>
              <span className="text-sm text-muted-foreground">
                {userSubscription.tokensUsedThisMonth} / {currentTier.tokensIncluded}
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className={cn(
                  'h-2 rounded-full transition-all duration-300',
                  isApproachingTokenLimit
                    ? 'bg-gradient-to-r from-orange-400 to-red-500'
                    : 'bg-gradient-to-r from-green-400 to-blue-500'
                )}
                style={{ width: `${Math.min(tokenUsagePercentage, 100)}%` }}
              />
            </div>
          </div>

          {/* Story Usage (if limited) */}
          {currentTier.maxStoriesPerMonth > 0 && (
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Eye className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Stories this month</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {userSubscription.storiesCreatedThisMonth} / {currentTier.maxStoriesPerMonth}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    'h-2 rounded-full transition-all duration-300',
                    isApproachingStoryLimit
                      ? 'bg-gradient-to-r from-orange-400 to-red-500'
                      : 'bg-gradient-to-r from-green-400 to-blue-500'
                  )}
                  style={{ width: `${Math.min(storyUsagePercentage, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Next Billing */}
          {userSubscription.nextBillingDate && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              <span>
                Next billing: {new Date(userSubscription.nextBillingDate).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-4">
          {isOnFreeTier && (
            <Button onClick={() => setShowUpgradeDialog(true)} className="flex-1">
              <Crown className="w-4 h-4 mr-2" />
              Upgrade Plan
            </Button>
          )}
          <Button 
            variant="outline" 
            onClick={handleManageSubscription}
            className={isOnFreeTier ? 'flex-1' : ''}
          >
            <Shield className="w-4 h-4 mr-2" />
            Manage Subscription
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={cn('space-y-4', className)}>
      <SubscriptionStatus />

      {/* Upgrade Prompts */}
      {showUpgradePrompts && isOnFreeTier && (
        <div className="space-y-2">
          <UpgradePrompt feature="No Ads" requiredTier="premium" />
          <UpgradePrompt feature="Export Features" requiredTier="premium" />
          <UpgradePrompt feature="Advanced Analytics" requiredTier="pro" />
        </div>
      )}

      {/* Upgrade Dialog */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="text-2xl">Choose Your Plan</DialogTitle>
            <DialogDescription>
              Unlock more features and get more tokens for collaborative storytelling
            </DialogDescription>
          </DialogHeader>

          <div className="grid md:grid-cols-3 gap-4 mt-6">
            {subscriptionTiers.map((tier) => {
              const isCurrent = tier.id === userSubscription.tier;
              const isPopular = tier.id === 'premium';

              return (
                <Card
                  key={tier.id}
                  className={cn(
                    'relative transition-all duration-200 hover:shadow-lg',
                    isCurrent && 'ring-2 ring-primary',
                    isPopular && 'border-primary'
                  )}
                >
                  {isPopular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                        Most Popular
                      </Badge>
                    </div>
                  )}

                  <CardHeader className="text-center pb-2">
                    <div className={cn(
                      'mx-auto w-12 h-12 rounded-full bg-gradient-to-r flex items-center justify-center mb-4',
                      tier.gradient
                    )}>
                      <tier.icon className="w-6 h-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{tier.name}</CardTitle>
                    <CardDescription>{tier.description}</CardDescription>
                    <div className="mt-4">
                      <span className="text-3xl font-bold">${tier.price}</span>
                      {tier.price > 0 && <span className="text-muted-foreground">/month</span>}
                    </div>
                  </CardHeader>

                  <CardContent>
                    <ul className="space-y-2 mb-6">
                      {tier.features.map((feature, index) => (
                        <li key={index} className="flex items-center gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          {feature}
                        </li>
                      ))}
                    </ul>

                    <Button
                      className="w-full"
                      variant={isCurrent ? 'outline' : 'default'}
                      onClick={() => handleUpgrade(tier.id)}
                      disabled={isCurrent}
                    >
                      {isCurrent ? 'Current Plan' : `Upgrade to ${tier.name}`}
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SubscriptionIntegration; 