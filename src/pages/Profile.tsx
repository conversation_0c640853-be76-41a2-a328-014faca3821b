import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertCircle,
  Crown,
  Loader2,
  User,
  BookText,
  Award,
  Shield,
} from "lucide-react";
import { BadgeList, BadgeTier } from "@/components/ui/achievement-badge";
import { Separator } from "@/components/ui/separator";
import { RotatingAdvertisement } from "@/components/ads/RotatingAdvertisement";
import { useAds } from "@/contexts/AdsContext";
import { handleImageError } from "@/lib/utils";
import { AvatarSelection } from "@/components/profile/AvatarSelection";
import { UserAvatar } from "@/components/ui/user-avatar";
import { useDocumentTitle, createPageTitle } from "@/hooks/useDocumentTitle";

const ProfilePage: React.FC = () => {
  const { user, isAuthenticated, updateProfile, logout } = useAuth();
  const { isAdFreeUser } = useAds();
  const navigate = useNavigate();

  const [username, setUsername] = useState(user?.username || "");
  const [email, setEmail] = useState(user?.email || "");
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Set page title
  useDocumentTitle(createPageTitle("Your Profile"));

  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const userBadges = (user.achievements?.badges as BadgeTier[]) || [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setIsUpdating(true);

    try {
      await updateProfile({
        username,
        email,
      });

      setSuccess("Profile updated successfully!");
    } catch (err) {
      setError("Failed to update profile. Please try again.");
      console.error("Update error:", err);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate("/");
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-serif">Your Profile</h1>
        <p className="text-gray-600 mt-1">Manage your account settings</p>
      </div>

      {!isAdFreeUser && <RotatingAdvertisement placement="header" className="mb-6" />}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Profile Picture</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarImage
                  src={user.profilePicture}
                  alt={user.username}
                  onError={handleImageError}
                />
                <AvatarFallback className="bg-literary-navy/10 text-literary-navy">
                  {user.username ? user.username.slice(0, 2).toUpperCase() : 'U'}
                </AvatarFallback>
              </Avatar>

              <div className="text-center">
                <h3 className="font-medium text-lg">{user.username}</h3>
                <p className="text-sm text-gray-500">{user.email}</p>

                <div className="mt-2">
                  {userBadges && userBadges.length > 0 ? (
                    <BadgeList badges={userBadges} size="sm" />
                  ) : (
                    <span className="text-xs text-gray-400">
                      No badges earned yet
                    </span>
                  )}
                </div>

                <div className="mt-3 px-3 py-1 inline-flex items-center rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  <Crown size={12} className="mr-1" />
                  {user.credits} Credits
                </div>
              </div>
            </CardContent>
          </Card>

          {user.achievements && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-lg">Your Achievements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-sm">
                      <BookText size={16} className="mr-2" />
                      Stories Created
                    </span>
                    <span className="font-semibold">
                      {user.achievements.storiesCreated}
                    </span>
                  </div>
                  <Separator />

                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-sm">
                      <User size={16} className="mr-2" />
                      Stories Joined
                    </span>
                    <span className="font-semibold">
                      {user.achievements.storiesParticipated}
                    </span>
                  </div>
                  <Separator />

                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-sm">
                      <Award size={16} className="mr-2" />
                      Words Contributed
                    </span>
                    <span className="font-semibold">
                      {user.achievements.wordsContributed}
                    </span>
                  </div>
                  <Separator />

                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-sm">
                      <Crown size={16} className="mr-2" />
                      Votes Received
                    </span>
                    <span className="font-semibold">
                      {user.achievements.votesReceived}
                    </span>
                  </div>
                </div>

                {userBadges.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Badges Earned</h4>
                    <BadgeList
                      badges={userBadges}
                      size="md"
                      showLabels={true}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {!isAdFreeUser && (
            <RotatingAdvertisement placement="sidebar" className="mt-4" />
          )}
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edit Profile</CardTitle>
              <CardDescription>
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="bg-red-50 text-red-800 p-3 rounded-md flex items-start mb-4 text-sm">
                  <AlertCircle
                    size={16}
                    className="mr-2 mt-0.5 flex-shrink-0"
                  />
                  <span>{error}</span>
                </div>
              )}

              {success && (
                <div className="bg-green-50 text-green-800 p-3 rounded-md mb-4 text-sm">
                  {success}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="input-primary"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input-primary"
                  />
                </div>



                <Button
                  type="submit"
                  disabled={isUpdating}
                  className="bg-literary-burgundy hover:bg-opacity-90"
                >
                  {isUpdating ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : null}
                  Save Changes
                </Button>
              </form>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <Button variant="outline" onClick={handleLogout}>
                Log Out
              </Button>

              <Button
                onClick={() => navigate("/subscription")}
                className="bg-literary-gold hover:bg-opacity-90"
              >
                <Crown className="h-4 w-4 mr-2" />
                Buy Credits
              </Button>
            </CardFooter>
          </Card>

          {/* Avatar Selection Component */}
          <AvatarSelection className="mt-6" />

          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Subscription Options</CardTitle>
              <CardDescription>
                Manage your subscription settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Ad-Free Experience</h3>
                  <p className="text-sm text-gray-500">
                    Remove all advertisements
                  </p>
                </div>
                <div>
                  {isAdFreeUser ? (
                    <span className="text-green-600 text-sm font-medium flex items-center">
                      <Shield size={14} className="mr-1" />
                      Active
                    </span>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate("/ad-free")}
                    >
                      Purchase
                    </Button>
                  )}
                </div>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Credits</h3>
                  <p className="text-sm text-gray-500">
                    Buy credits for special features
                  </p>
                </div>
                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate("/subscription")}
                  >
                    <Crown className="h-3 w-3 mr-1" />
                    Buy Credits
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {!isAdFreeUser && <RotatingAdvertisement placement="footer" className="mt-6" />}
    </div>
  );
};

export default ProfilePage;
