CREATE TABLE public.presence (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    username TEXT, -- Store username for easier display
    is_typing BOOLEAN DEFAULT FALSE,
    last_activity TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT unique_user_story_presence UNIQUE (story_id, user_id)
);
COMMENT ON TABLE public.presence IS 'Tracks user presence and typing status within stories.';

ALTER TABLE public.presence ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to presence in accessible stories"
ON public.presence FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.stories s
        WHERE s.id = presence.story_id AND 
              (s.is_public = true OR 
               s.author_id = auth.uid() OR 
               EXISTS (SELECT 1 FROM public.contributions c WHERE c.story_id = s.id AND c.user_id = auth.uid())
              )
    )
);

CREATE POLICY "Allow users to insert their own presence"
ON public.presence FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to update their own presence"
ON public.presence FOR UPDATE USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow users to delete their own presence"
ON public.presence FOR DELETE USING (auth.uid() = user_id); 