# Vercel CLI Quick Reference

See DEPLOY_AND_RUN.md for full run & deploy instructions.

## Install & Login

```bash
yarn global add vercel
vercel login
```

## Link Project

```bash
vercel link
```

## Pull Environment Variables

```bash
yarn setup:vercel-env
```

## Local Dev with Vercel Functions

```bash
npx vercel dev
```

## Deploy

```bash
yarn deploy:development
yarn deploy:preview
yarn deploy:production
```
