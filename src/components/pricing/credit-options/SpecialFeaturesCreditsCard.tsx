import React from "react";
import { CreditOptionCard } from "@/components/pricing/CreditOptionCard";
import { Sparkles } from "lucide-react";

interface SpecialFeaturesCreditsCardProps {
  onBuy: (type: string, amount: number, cents: number) => void;
  isLoading: boolean;
}

export const SpecialFeaturesCreditsCard: React.FC<
  SpecialFeaturesCreditsCardProps
> = ({ onBuy, isLoading }) => {
  const options = [
    {
      label: "500",
      creditsLabel: "500 credits",
      credits: 500,
      cents: 1499,
      price: "$14.99",
    },
    {
      label: "1000",
      creditsLabel: "1000 credits",
      credits: 1000,
      cents: 2499,
      price: "$24.99",
    },
    {
      label: "2500",
      creditsLabel: "2500 credits",
      credits: 2500,
      cents: 4999,
      price: "$49.99",
    },
  ];

  return (
    <CreditOptionCard
      icon={<Sparkles className="h-4 w-4 text-literary-gold" />}
      title="Large Pack"
      description="Best value for power users. Enough credits for multiple premium features at the lowest per-credit cost."
      options={options}
      onBuy={onBuy}
      isLoading={isLoading}
      type="Credits"
    />
  );
};
