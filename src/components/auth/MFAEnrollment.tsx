import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Check, AlertCircle, Loader2 } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/hooks/use-toast";
import MFASetup from "./MFASetup";

interface MFAEnrollmentProps {
  onComplete?: () => void;
}

const MFAEnrollment: React.FC<MFAEnrollmentProps> = ({ onComplete }) => {
  const [showSetup, setShowSetup] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { user, isMFAEnabled, completeMFASetup } = useAuth();
  const { toast } = useToast();

  const handleSetupComplete = async () => {
    setIsLoading(true);
    try {
      // Fixed to not pass any arguments as per the interface definition
      await completeMFASetup();
      setShowSetup(false);
      toast({
        title: "MFA Enabled",
        description:
          "Your account is now protected with multi-factor authentication",
      });
      if (onComplete) onComplete();
    } catch (error) {
      toast({
        title: "Setup Error",
        description: "There was an issue completing the MFA setup",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) return null;

  if (isMFAEnabled()) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-600" />
            <span>Two-Factor Authentication</span>
          </CardTitle>
          <CardDescription>
            Your account is protected with an additional security layer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-green-50 text-green-800 p-3 rounded-md flex items-start">
            <Check size={16} className="mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium">MFA is enabled on your account</p>
              <p className="text-sm mt-1">
                You'll be asked to verify your identity using your phone when
                signing in from unfamiliar devices.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (showSetup) {
    return (
      <MFASetup
        onComplete={handleSetupComplete}
        onCancel={() => setShowSetup(false)}
      />
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-literary-burgundy" />
          <span>Two-Factor Authentication</span>
        </CardTitle>
        <CardDescription>
          Add an extra layer of security to your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-amber-50 text-amber-800 p-3 rounded-md flex items-start mb-4">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium">Your account could be more secure</p>
            <p className="text-sm mt-1">
              Enable two-factor authentication to add an extra layer of
              security. This helps protect your account from unauthorized
              access.
            </p>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          When enabled, you'll need to provide a verification code sent to your
          phone whenever you sign in from a new device or browser.
        </p>
      </CardContent>
      <CardFooter>
        <Button
          onClick={() => setShowSetup(true)}
          className="bg-literary-burgundy hover:bg-opacity-90"
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
          Set Up Two-Factor Authentication
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MFAEnrollment;
