[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [alert-dialog](../README.md) / AlertDialogContent

# Variable: AlertDialogContent

> `const` **AlertDialogContent**: `ForwardRefExoticComponent`\<`Omit`\<`AlertDialogContentProps` & `RefAttributes`\<`HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/alert-dialog.tsx:28](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/alert-dialog.tsx#L28)
