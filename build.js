// Custom build script to work around Rollup issues
import { execSync } from "child_process";

// Set environment variable to skip Rollup native modules
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = "true";

try {
  console.log("Building the application...");
  execSync("npx vite build", {
    stdio: "inherit",
    env: {
      ...process.env,
      ROLLUP_SKIP_NODEJS_BUNDLE: "true",
    },
  });
  console.log("Build completed successfully");
} catch (error) {
  console.error("Error building the application:", error);
  process.exit(1);
}
