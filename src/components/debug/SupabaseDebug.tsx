import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/auth/FirebaseAuthContext';

export const SupabaseDebug: React.FC = () => {
  const [status, setStatus] = useState<{
    connection: 'checking' | 'success' | 'error';
    auth: 'checking' | 'authenticated' | 'unauthenticated' | 'error';
    notifications: 'checking' | 'success' | 'error';
    details: string[];
  }>({
    connection: 'checking',
    auth: 'checking', 
    notifications: 'checking',
    details: []
  });

  const { user, isAuthenticated, loading: authLoading } = useAuth();

  useEffect(() => {
    const runDiagnostics = async () => {
      const details: string[] = [];
      
      // Check environment variables
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
      const serviceRoleKey = import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;
      
      details.push(`Supabase URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
      details.push(`Supabase Anon Key: ${supabaseKey ? '✅ Set' : '❌ Missing'}`);
      details.push(`Supabase Service Role Key: ${serviceRoleKey ? '✅ Set' : '❌ Missing (needed for notifications)'}`);
      details.push(`Current Domain: ${window.location.origin}`);
      
      if (!supabaseUrl || !supabaseKey) {
        setStatus(prev => ({ 
          ...prev, 
          connection: 'error',
          details: [...details, '❌ Missing required environment variables']
        }));
        return;
      }

      // Test basic connection with better error handling
      try {
        const { data, error } = await supabase.from('stories').select('id').limit(1);
        if (error) {
          throw new Error(`DB Error: ${error.message} (Code: ${error.code})`);
        }
        setStatus(prev => ({ ...prev, connection: 'success' }));
        details.push('✅ Database connection successful');
      } catch (error: any) {
        setStatus(prev => ({ ...prev, connection: 'error' }));
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          details.push(`❌ Network/CORS Error: Cannot reach Supabase from ${window.location.origin}`);
        } else {
          details.push(`❌ Database connection failed: ${error.message}`);
        }
      }

      // Check auth status using context (Firebase)
      details.push(`Firebase Auth Context - isAuthenticated: ${isAuthenticated}`);
      details.push(`Firebase Auth Context - user: ${user ? `✅ ${user.email || user.id}` : '❌ null'}`);

      // Skip Supabase session check since we use Firebase auth
      // Firebase handles authentication, Supabase only handles data storage

      // Test notifications table access
      if (user && user.id) {
        try {
          const { data, error } = await supabase
            .from('notifications')
            .select('id')
            .eq('user_id', user.id)
            .limit(1);
            
          if (error) throw error;
          setStatus(prev => ({ ...prev, notifications: 'success' }));
          details.push('✅ Notifications table accessible');
        } catch (error: any) {
          setStatus(prev => ({ ...prev, notifications: 'error' }));
          details.push(`❌ Notifications access failed: ${error.message}`);
        }
      } else {
        details.push('⚠️ Cannot test notifications - no user ID');
      }

      setStatus(prev => ({ ...prev, details }));
    };

    if (!authLoading) {
      runDiagnostics();
    } else {
      setStatus(prev => ({ 
        ...prev, 
        connection: 'checking',
        auth: 'checking',
        notifications: 'checking',
        details: ['🔄 Checking Firebase auth state...']
      }));
    }
  }, [user, isAuthenticated, authLoading]);

  // Hide debug component from production users
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md z-50 text-black">
      <h3 className="font-bold text-sm mb-2">🔧 Supabase Debug</h3>
      <div className="text-xs space-y-1 max-h-64 overflow-y-auto">
        {status.details.map((detail, index) => (
          <div key={index} className="break-words">{detail}</div>
        ))}
      </div>
      <button 
        onClick={() => window.location.reload()} 
        className="mt-2 text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
      >
        Refresh
      </button>
    </div>
  );
}; 