import React, { useEffect, useRef } from 'react';
import { useAds } from '@/contexts/AdsContext';

interface AdProps {
  adSlot: string;
  adFormat?: string;
  responsive?: string; // e.g., 'true'
  style?: React.CSSProperties;
  className?: string;
}

export const AdComponent: React.FC<AdProps> = ({
  adSlot,
  adFormat = 'auto',
  responsive = 'true',
  style = {},
  className = 'ad-container',
}) => {
  const { isAdFree, adSettings } = useAds();
  const adRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only attempt to load ads if ads should be shown globally and user is not ad-free
    if (adSettings.showAds && !isAdFree && adRef.current) {
      try {
        // Check if an ad has already been loaded in this slot to prevent duplicates on re-renders
        if (adRef.current.innerHTML.trim() === '') {
          const adScript = document.createElement('script');
          adScript.async = true;
          adScript.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${import.meta.env.VITE_ADSENSE_CLIENT_ID}`;
          adScript.crossOrigin = 'anonymous';
          // It's generally recommended to put the main AdSense script in the <head> once.
          // This component will focus on pushing the ad to the specific slot.
          // document.head.appendChild(adScript); // AdsContext already handles loading the main script.

          const adElement = document.createElement('ins');
          adElement.className = 'adsbygoogle';
          adElement.style.display = 'block';
          adElement.setAttribute('data-ad-client', import.meta.env.VITE_ADSENSE_CLIENT_ID || 'ca-pub-TEST_PUBLISHER_ID'); // Fallback for safety
          adElement.setAttribute('data-ad-slot', adSlot);
          if (adFormat !== 'auto') { // Only set ad-format if it's not auto, as auto usually handles it
             adElement.setAttribute('data-ad-format', adFormat);
          }
          if (responsive === 'true') {
            adElement.setAttribute('data-full-width-responsive', 'true');
          }

          adRef.current.appendChild(adElement);
          ((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
        }
      } catch (e) {
        console.error('AdSense error:', e);
      }
    }
  }, [adSlot, adFormat, responsive, isAdFree, adSettings.showAds]);

  // Do not render the div if the user is subscribed or ads are globally turned off
  if (isAdFree || !adSettings.showAds) {
    return null;
  }

  // Render the div that will hold the ad
  return <div ref={adRef} style={style} className={className} />;
};

export default AdComponent; 