/**
 * Permission Service
 * Handles role-based access control for read-only participants and permission enforcement
 */

import { supabase } from '@/lib/supabase';

export type UserRole = 'creator' | 'contributor' | 'viewer';
export type UserStatus = 'active' | 'muted' | 'banned';

export interface UserPermissions {
  // Story-level permissions
  canCreateStory: boolean;
  canEditStoryDetails: boolean;
  canDeleteStory: boolean;
  canManageParticipants: boolean;
  canCreateInvitations: boolean;
  
  // Contribution permissions
  canContribute: boolean;
  canEditOwnContributions: boolean;
  canDeleteOwnContributions: boolean;
  
  // Messaging permissions
  canSendMessages: boolean;
  canReactToMessages: boolean;
  
  // View permissions
  canViewStory: boolean;
  canViewMessages: boolean;
  canViewParticipants: boolean;
  
  // Moderation permissions
  canReportContent: boolean;
  canModerateContent: boolean;
  
  // UI state
  role: UserRole;
  status: UserStatus;
  isCreator: boolean;
  isMuted: boolean;
  isBanned: boolean;
}

export interface StoryParticipantData {
  user_id: string;
  role: UserRole;
  status: UserStatus;
  story_id: string;
}

/**
 * Get user's role and status in a specific story
 */
export const getUserStoryRole = async (
  storyId: string,
  userId: string
): Promise<StoryParticipantData | null> => {
  try {
    const { data, error } = await supabase
      .from('story_participants')
      .select('user_id, role, status, story_id')
      .eq('story_id', storyId)
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    return data as StoryParticipantData;
  } catch (error) {
    console.error('Error getting user story role:', error);
    return null;
  }
};

/**
 * Calculate user permissions based on role and status
 */
export const calculatePermissions = (
  role: UserRole,
  status: UserStatus,
  isStoryCreator: boolean = false
): UserPermissions => {
  const isMuted = status === 'muted';
  const isBanned = status === 'banned';
  const isActive = status === 'active';

  // Base permissions for all users
  const basePermissions: UserPermissions = {
    // Story-level permissions (creator only)
    canCreateStory: true, // Anyone can create their own stories
    canEditStoryDetails: isStoryCreator,
    canDeleteStory: isStoryCreator,
    canManageParticipants: isStoryCreator,
    canCreateInvitations: isStoryCreator,
    
    // Contribution permissions
    canContribute: false,
    canEditOwnContributions: false,
    canDeleteOwnContributions: false,
    
    // Messaging permissions
    canSendMessages: false,
    canReactToMessages: false,
    
    // View permissions
    canViewStory: !isBanned,
    canViewMessages: !isBanned,
    canViewParticipants: !isBanned,
    
    // Moderation permissions
    canReportContent: isActive && !isBanned,
    canModerateContent: false,
    
    // UI state
    role,
    status,
    isCreator: isStoryCreator,
    isMuted,
    isBanned,
  };

  // Apply role-specific permissions
  switch (role) {
    case 'creator':
      return {
        ...basePermissions,
        canContribute: isActive,
        canEditOwnContributions: isActive,
        canDeleteOwnContributions: isActive,
        canSendMessages: isActive,
        canReactToMessages: isActive,
        canModerateContent: true,
      };

    case 'contributor':
      return {
        ...basePermissions,
        canContribute: isActive && !isMuted,
        canEditOwnContributions: isActive && !isMuted,
        canDeleteOwnContributions: isActive && !isMuted,
        canSendMessages: isActive && !isMuted,
        canReactToMessages: isActive && !isMuted,
      };

    case 'viewer':
      return {
        ...basePermissions,
        // Viewers are read-only
        canContribute: false,
        canEditOwnContributions: false,
        canDeleteOwnContributions: false,
        canSendMessages: false,
        canReactToMessages: true, // Allow reactions but not messages
      };

    default:
      return basePermissions;
  }
};

/**
 * Get user permissions for a specific story
 */
export const getUserStoryPermissions = async (
  storyId: string,
  userId: string
): Promise<UserPermissions | null> => {
  try {
    const participantData = await getUserStoryRole(storyId, userId);
    
    if (!participantData) {
      // User is not a participant, return minimal permissions
      return {
        canCreateStory: true,
        canEditStoryDetails: false,
        canDeleteStory: false,
        canManageParticipants: false,
        canCreateInvitations: false,
        canContribute: false,
        canEditOwnContributions: false,
        canDeleteOwnContributions: false,
        canSendMessages: false,
        canReactToMessages: false,
        canViewStory: false, // Non-participants can't view private stories
        canViewMessages: false,
        canViewParticipants: false,
        canReportContent: false,
        canModerateContent: false,
        role: 'viewer',
        status: 'active',
        isCreator: false,
        isMuted: false,
        isBanned: false,
      };
    }

    const isCreator = participantData.role === 'creator';
    return calculatePermissions(participantData.role, participantData.status, isCreator);
  } catch (error) {
    console.error('Error getting user story permissions:', error);
    return null;
  }
};

/**
 * Check if user can perform a specific action
 */
export const canUserPerformAction = async (
  storyId: string,
  userId: string,
  action: keyof UserPermissions
): Promise<boolean> => {
  try {
    const permissions = await getUserStoryPermissions(storyId, userId);
    return permissions ? permissions[action] as boolean : false;
  } catch (error) {
    console.error('Error checking user action permission:', error);
    return false;
  }
};

/**
 * Validate user can contribute to story
 */
export const validateContributionPermission = async (
  storyId: string,
  userId: string
): Promise<{ canContribute: boolean; reason?: string }> => {
  try {
    const permissions = await getUserStoryPermissions(storyId, userId);
    
    if (!permissions) {
      return { canContribute: false, reason: 'You are not a participant in this story' };
    }

    if (permissions.isBanned) {
      return { canContribute: false, reason: 'You have been banned from this story' };
    }

    if (permissions.isMuted) {
      return { canContribute: false, reason: 'You have been muted and cannot contribute' };
    }

    if (permissions.role === 'viewer') {
      return { canContribute: false, reason: 'Viewers cannot contribute content' };
    }

    if (!permissions.canContribute) {
      return { canContribute: false, reason: 'You do not have permission to contribute' };
    }

    return { canContribute: true };
  } catch (error) {
    console.error('Error validating contribution permission:', error);
    return { canContribute: false, reason: 'Error checking permissions' };
  }
};

/**
 * Validate user can send messages
 */
export const validateMessagingPermission = async (
  storyId: string,
  userId: string
): Promise<{ canMessage: boolean; reason?: string }> => {
  try {
    const permissions = await getUserStoryPermissions(storyId, userId);
    
    if (!permissions) {
      return { canMessage: false, reason: 'You are not a participant in this story' };
    }

    if (permissions.isBanned) {
      return { canMessage: false, reason: 'You have been banned from this story' };
    }

    if (permissions.isMuted) {
      return { canMessage: false, reason: 'You have been muted and cannot send messages' };
    }

    if (permissions.role === 'viewer') {
      return { canMessage: false, reason: 'Viewers cannot send messages' };
    }

    if (!permissions.canSendMessages) {
      return { canMessage: false, reason: 'You do not have permission to send messages' };
    }

    return { canMessage: true };
  } catch (error) {
    console.error('Error validating messaging permission:', error);
    return { canMessage: false, reason: 'Error checking permissions' };
  }
};

/**
 * Get permission summary for UI display
 */
export const getPermissionSummary = (permissions: UserPermissions): {
  label: string;
  description: string;
  capabilities: string[];
  restrictions: string[];
} => {
  switch (permissions.role) {
    case 'creator':
      return {
        label: 'Story Creator',
        description: 'Full control over the story and all participants',
        capabilities: [
          'Add and edit story content',
          'Manage participants and invitations',
          'Send messages and react',
          'Moderate content and behavior',
          'Control story settings'
        ],
        restrictions: permissions.isMuted ? ['Currently muted'] : []
      };

    case 'contributor':
      return {
        label: 'Contributor',
        description: 'Can actively participate in story creation',
        capabilities: [
          'Add story content',
          'Edit own contributions',
          'Send messages and react',
          'View all story content'
        ],
        restrictions: [
          ...(permissions.isMuted ? ['Currently muted - cannot contribute or message'] : []),
          'Cannot manage other participants',
          'Cannot change story settings'
        ]
      };

    case 'viewer':
      return {
        label: 'Viewer (Read-Only)',
        description: 'Can follow the story as it develops',
        capabilities: [
          'View story content as it updates',
          'See messages and conversations',
          'React to messages',
          'View participant list'
        ],
        restrictions: [
          'Cannot add story content',
          'Cannot send messages',
          'Cannot edit or delete content',
          'Cannot manage participants'
        ]
      };

    default:
      return {
        label: 'Unknown',
        description: 'Role not recognized',
        capabilities: [],
        restrictions: ['All actions restricted']
      };
  }
};

/**
 * Check if story is public or if user has access
 */
export const checkStoryAccess = async (
  storyId: string,
  userId?: string
): Promise<{ hasAccess: boolean; reason?: string }> => {
  try {
    // Check story visibility
    const { data: story, error: storyError } = await supabase
      .from('stories')
      .select('visibility, author_id')
      .eq('id', storyId)
      .single();

    if (storyError || !story) {
      return { hasAccess: false, reason: 'Story not found' };
    }

    // Public stories are accessible to everyone
    if (story.visibility === 'public') {
      return { hasAccess: true };
    }

    // Private/hidden stories require participation
    if (!userId) {
      return { hasAccess: false, reason: 'This story is private' };
    }

    // Check if user is a participant or author
    if (story.author_id === userId) {
      return { hasAccess: true };
    }

    const participantData = await getUserStoryRole(storyId, userId);
    if (participantData && participantData.status !== 'banned') {
      return { hasAccess: true };
    }

    return { hasAccess: false, reason: 'You do not have access to this private story' };
  } catch (error) {
    console.error('Error checking story access:', error);
    return { hasAccess: false, reason: 'Error checking access' };
  }
};