-- Migration: Add moderation and enhanced participant management
-- This migration adds support for:
-- 1. Story participants with roles (including read-only)
-- 2. Content reporting and strike tracking system
-- 3. Story invitations system
-- 4. Three-strike moderation for story visibility

-- Story Participants Table
-- Manages participant roles and permissions for stories
CREATE TABLE public.story_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'contributor' CHECK (role IN ('creator', 'contributor', 'viewer')),
    joined_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'muted', 'banned')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Ensure unique participant per story
    CONSTRAINT unique_story_participant UNIQUE (story_id, user_id),
    
    -- Ensure creator role constraints
    CONSTRAINT check_creator_role CHECK (
        role != 'creator' OR invited_by IS NULL
    )
);

COMMENT ON TABLE public.story_participants IS 'Manages participant roles and permissions for collaborative stories';
COMMENT ON COLUMN public.story_participants.role IS 'Participant role: creator (story owner), contributor (can add content), viewer (read-only)';
COMMENT ON COLUMN public.story_participants.status IS 'Participant status: active, muted (temporarily silenced), banned (removed)';

-- Story Invitations Table
-- Manages invitation links and permissions for joining stories
CREATE TABLE public.story_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    inviter_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    invite_code TEXT NOT NULL UNIQUE,
    email TEXT, -- Optional: specific email invitation
    permissions TEXT NOT NULL DEFAULT 'contributor' CHECK (permissions IN ('contributor', 'viewer')),
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + INTERVAL '7 days'),
    max_uses INTEGER DEFAULT 1,
    current_uses INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.story_invitations IS 'Manages invitation links for joining collaborative stories';
COMMENT ON COLUMN public.story_invitations.invite_code IS 'Unique code for invitation link';
COMMENT ON COLUMN public.story_invitations.permissions IS 'Role granted when invitation is accepted';

-- Content Reports Table
-- Tracks user reports of inappropriate content
CREATE TABLE public.content_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type TEXT NOT NULL CHECK (content_type IN ('story', 'contribution', 'comment')),
    content_id UUID NOT NULL, -- References stories.id, contributions.id, etc.
    reported_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    report_reason TEXT NOT NULL,
    report_details TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    reviewed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    review_notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Prevent duplicate reports from same user for same content
    CONSTRAINT unique_user_content_report UNIQUE (content_type, content_id, reported_by)
);

COMMENT ON TABLE public.content_reports IS 'Tracks user reports of inappropriate content across the platform';
COMMENT ON COLUMN public.content_reports.content_id IS 'ID of the reported content (story, contribution, etc.)';
COMMENT ON COLUMN public.content_reports.status IS 'Report processing status';

-- Story Strikes Table
-- Tracks moderation strikes against stories for the 3-strike system
CREATE TABLE public.story_strikes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    strike_reason TEXT NOT NULL,
    strike_details TEXT,
    report_id UUID REFERENCES public.content_reports(id) ON DELETE SET NULL,
    issued_by UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- Moderator who issued strike
    auto_generated BOOLEAN NOT NULL DEFAULT false, -- Whether strike was auto-generated
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

COMMENT ON TABLE public.story_strikes IS 'Tracks moderation strikes against stories for the 3-strike visibility system';
COMMENT ON COLUMN public.story_strikes.auto_generated IS 'Whether this strike was automatically generated vs. manually issued';

-- Add strike_count and visibility fields to stories table
ALTER TABLE public.stories 
ADD COLUMN strike_count INTEGER NOT NULL DEFAULT 0,
ADD COLUMN visibility TEXT NOT NULL DEFAULT 'public' CHECK (visibility IN ('public', 'private', 'hidden'));

COMMENT ON COLUMN public.stories.strike_count IS 'Current number of moderation strikes (max 3 before going private)';
COMMENT ON COLUMN public.stories.visibility IS 'Story visibility: public (visible to all), private (only participants), hidden (admin only)';

-- Indexes for performance
CREATE INDEX idx_story_participants_story_id ON public.story_participants(story_id);
CREATE INDEX idx_story_participants_user_id ON public.story_participants(user_id);
CREATE INDEX idx_story_participants_role ON public.story_participants(role);
CREATE INDEX idx_story_invitations_story_id ON public.story_invitations(story_id);
CREATE INDEX idx_story_invitations_invite_code ON public.story_invitations(invite_code);
CREATE INDEX idx_story_invitations_status ON public.story_invitations(status);
CREATE INDEX idx_content_reports_content ON public.content_reports(content_type, content_id);
CREATE INDEX idx_content_reports_reported_by ON public.content_reports(reported_by);
CREATE INDEX idx_content_reports_status ON public.content_reports(status);
CREATE INDEX idx_story_strikes_story_id ON public.story_strikes(story_id);
CREATE INDEX idx_story_strikes_created_at ON public.story_strikes(created_at);
CREATE INDEX idx_stories_visibility ON public.stories(visibility);
CREATE INDEX idx_stories_strike_count ON public.stories(strike_count);

-- Triggers for updated_at timestamps
CREATE TRIGGER set_story_participants_timestamp
BEFORE UPDATE ON public.story_participants
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_story_invitations_timestamp  
BEFORE UPDATE ON public.story_invitations
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_content_reports_timestamp
BEFORE UPDATE ON public.content_reports  
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Function to automatically update story visibility based on strike count
CREATE OR REPLACE FUNCTION update_story_visibility_on_strike()
RETURNS TRIGGER AS $$
BEGIN
    -- Update story strike count and visibility
    UPDATE public.stories 
    SET 
        strike_count = (
            SELECT COUNT(*) 
            FROM public.story_strikes 
            WHERE story_id = NEW.story_id
        ),
        visibility = CASE 
            WHEN (
                SELECT COUNT(*) 
                FROM public.story_strikes 
                WHERE story_id = NEW.story_id
            ) >= 3 THEN 'private'
            ELSE visibility
        END,
        updated_at = now()
    WHERE id = NEW.story_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update story visibility when strikes are added
CREATE TRIGGER trigger_update_story_visibility
AFTER INSERT ON public.story_strikes
FOR EACH ROW
EXECUTE FUNCTION update_story_visibility_on_strike();

-- Function to create story creator as participant
CREATE OR REPLACE FUNCTION add_story_creator_as_participant()
RETURNS TRIGGER AS $$
BEGIN
    -- Add story creator as participant with 'creator' role
    INSERT INTO public.story_participants (story_id, user_id, role, joined_at)
    VALUES (NEW.id, NEW.author_id, 'creator', NEW.created_at)
    ON CONFLICT (story_id, user_id) DO NOTHING;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically add story creator as participant
CREATE TRIGGER trigger_add_story_creator_participant
AFTER INSERT ON public.stories
FOR EACH ROW
EXECUTE FUNCTION add_story_creator_as_participant();

-- Function to handle invitation expiration
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
    UPDATE public.story_invitations 
    SET status = 'expired', updated_at = now()
    WHERE status = 'pending' 
    AND expires_at < now();
END;
$$ LANGUAGE plpgsql;

-- Function to recalculate story strikes after strike removal
CREATE OR REPLACE FUNCTION recalculate_story_strikes(target_story_id UUID)
RETURNS void AS $$
BEGIN
    -- Recalculate strike count and update visibility
    UPDATE public.stories 
    SET 
        strike_count = (
            SELECT COUNT(*) 
            FROM public.story_strikes 
            WHERE story_id = target_story_id
        ),
        visibility = CASE 
            WHEN (
                SELECT COUNT(*) 
                FROM public.story_strikes 
                WHERE story_id = target_story_id
            ) >= 3 THEN 'private'
            WHEN (
                SELECT COUNT(*) 
                FROM public.story_strikes 
                WHERE story_id = target_story_id
            ) = 0 THEN 'public'
            ELSE visibility -- Keep current visibility if 1-2 strikes
        END,
        updated_at = now()
    WHERE id = target_story_id;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies

-- Story Participants RLS
ALTER TABLE public.story_participants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view participants of stories they have access to"
ON public.story_participants FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.story_participants sp
        WHERE sp.story_id = story_participants.story_id 
        AND sp.user_id = auth.uid()
        AND sp.status = 'active'
    )
    OR 
    EXISTS (
        SELECT 1 FROM public.stories s
        WHERE s.id = story_participants.story_id 
        AND s.visibility = 'public'
    )
);

CREATE POLICY "Story creators can manage participants"
ON public.story_participants FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.story_participants sp
        WHERE sp.story_id = story_participants.story_id 
        AND sp.user_id = auth.uid()
        AND sp.role = 'creator'
    )
);

CREATE POLICY "Users can join via valid invitations"
ON public.story_participants FOR INSERT
WITH CHECK (user_id = auth.uid());

-- Story Invitations RLS  
ALTER TABLE public.story_invitations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Story creators can manage invitations"
ON public.story_invitations FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.story_participants sp
        WHERE sp.story_id = story_invitations.story_id 
        AND sp.user_id = auth.uid()
        AND sp.role = 'creator'
    )
);

CREATE POLICY "Anyone can view pending invitations by invite code"
ON public.story_invitations FOR SELECT
USING (status = 'pending' AND expires_at > now());

-- Content Reports RLS
ALTER TABLE public.content_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can create content reports"
ON public.content_reports FOR INSERT  
WITH CHECK (reported_by = auth.uid());

CREATE POLICY "Users can view their own reports"
ON public.content_reports FOR SELECT
USING (reported_by = auth.uid());

CREATE POLICY "Admins can view and manage all reports"
ON public.content_reports FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = auth.uid() 
        AND up.is_admin = true
    )
);

-- Story Strikes RLS  
ALTER TABLE public.story_strikes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view strikes for stories they participate in"
ON public.story_strikes FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.story_participants sp
        WHERE sp.story_id = story_strikes.story_id 
        AND sp.user_id = auth.uid()
        AND sp.status = 'active'
    )
);

CREATE POLICY "Admins can manage all strikes"
ON public.story_strikes FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = auth.uid() 
        AND up.is_admin = true
    )
);