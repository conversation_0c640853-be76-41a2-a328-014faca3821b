# Redis Configuration for Word-by-Word Story API Caching
# Optimized for $49.99 monthly API tier cost reduction

# Basic server configuration
port 6379
bind 127.0.0.1

# Memory management for cost optimization
maxmemory 256mb
maxmemory-policy allkeys-lru  # Evict least recently used keys when memory limit is reached

# Persistence configuration (balanced for performance vs durability)
save 900 1    # Save if at least 1 key changed in 900 seconds
save 300 10   # Save if at least 10 keys changed in 300 seconds  
save 60 10000 # Save if at least 10000 keys changed in 60 seconds

# Disable Redis snapshots for faster performance (use only if you have backups)
# save ""

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Security
# requirepass your_redis_password_here

# Performance optimizations
tcp-keepalive 300
timeout 0

# Enable lazy freeing for better performance
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes

# Hash and list optimizations for story data
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0

# Disable some commands for security (optional)
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command KEYS ""
rename-command CONFIG CONFIG_SECURE_$(date +%s)

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Slow log configuration
slowlog-log-slower-than 10000
slowlog-max-len 128

# Advanced memory optimizations
activerehashing yes
rdbcompression yes
rdbchecksum yes

# Network optimizations
tcp-backlog 511
tcp-keepalive 60

# Threading (Redis 6.0+)
# io-threads 4
# io-threads-do-reads yes 