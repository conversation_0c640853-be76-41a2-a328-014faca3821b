# Word-by-Word Story - Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# ===================================================================
# REQUIRED: SUPABASE CONFIGURATION
# ===================================================================
# Get these from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# For server-side operations (API routes, edge functions)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database connection strings (for migrations and direct access)
SUPABASE_DB_DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres
SUPABASE_DB_POOLER_URL=postgres://postgres:<EMAIL>:6543/postgres

# ===================================================================
# REQUIRED: STRIPE CONFIGURATION (FOR SUBSCRIPTIONS)
# ===================================================================
# Get these from your Stripe dashboard
STRIPE_SECRET_KEY=sk_test_... # Use sk_live_... for production
VITE_STRIPE_PUBLIC_KEY=pk_test_... # Use pk_live_... for production
STRIPE_PRICE_ID=price_... # Your subscription price ID
STRIPE_WEBHOOK_SIGNING_SECRET=whsec_... # For webhook verification

# ===================================================================
# OPTIONAL: GOOGLE ADSENSE (FOR MONETIZATION)
# ===================================================================
# Get these from your Google AdSense account
VITE_ADSENSE_CLIENT_ID=ca-pub-your-publisher-id
VITE_ADSENSE_SLOT_HEADER=your-header-slot-id
VITE_ADSENSE_SLOT_CONTENT=your-content-slot-id
VITE_ADSENSE_SLOT_STORY_END=your-story-end-slot-id
VITE_ADSENSE_SLOT_FOOTER=your-footer-slot-id
VITE_ADSENSE_SLOT_STORY_SIDEBAR=your-sidebar-slot-id

# ===================================================================
# REQUIRED: FIREBASE AUTHENTICATION
# ===================================================================
# Get these from your Firebase project settings
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=*********
VITE_FIREBASE_APP_ID=1:*********:web:abcdef123456

# ===================================================================
# OPTIONAL: POSTHOG ANALYTICS
# ===================================================================
# Get these from your PostHog project
VITE_POSTHOG_KEY=phc_your-project-key
VITE_POSTHOG_HOST=https://us.i.posthog.com
VITE_POSTHOG_DISABLE_IN_DEV=false # Set to true to disable in development

# ===================================================================
# OPTIONAL: REDIS (FOR CACHING)
# ===================================================================
# Redis configuration for API response caching
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# ===================================================================
# OPTIONAL: AI SERVICES
# ===================================================================
# For AI-powered features (story suggestions, content moderation)
VITE_DEEPINFRA_API_KEY=your-deepinfra-api-key

# ===================================================================
# APPLICATION CONFIGURATION
# ===================================================================
# Environment type
NODE_ENV=development # development | production

# Admin configuration
ADMIN_EMAIL=<EMAIL>

# ===================================================================
# DEVELOPMENT ONLY
# ===================================================================
# These are only needed for development/testing

# For local development server
PORT=3001

# For testing Stripe webhooks locally
STRIPE_CLI_WEBHOOK_SECRET=whsec_test_...

# ===================================================================
# PRODUCTION ADDITIONAL VARIABLES
# ===================================================================
# These are typically set in your hosting provider's dashboard

# Domain configuration
VERCEL_URL=your-app.vercel.app
NEXT_PUBLIC_VERCEL_URL=your-app.vercel.app

# Security
NEXTAUTH_SECRET=your-nextauth-secret # If using NextAuth
NEXTAUTH_URL=https://yourdomain.com

# Monitoring (optional)
SENTRY_DSN=your-sentry-dsn # If using Sentry for error tracking

# ===================================================================
# NOTES
# ===================================================================
# 
# 1. NEVER commit .env files with real values to version control
# 2. Use different values for development, staging, and production
# 3. For Vercel deployment, set these in the Vercel dashboard
# 4. For local development, copy this to .env.local
# 5. Some variables are prefixed with VITE_ to make them available in the browser
# 6. Server-only variables (like STRIPE_SECRET_KEY) should NOT have VITE_ prefix
# 
# For more information, see:
# - PRODUCTION_DEPLOYMENT.md for production setup
# - DEPLOY_AND_RUN.md for development setup 