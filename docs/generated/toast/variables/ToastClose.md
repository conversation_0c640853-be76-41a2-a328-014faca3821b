[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toast](../README.md) / ToastClose

# Variable: ToastClose

> `const` **ToastClose**: `ForwardRefExoticComponent`\<`Omit`\<`ToastCloseProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/toast.tsx:71](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toast.tsx#L71)
