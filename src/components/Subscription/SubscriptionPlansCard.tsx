import React, { useState } from 'react';
import { PricingPlan } from './SubscriptionPage';

interface SubscriptionPlansCardProps {
  plans: PricingPlan[];
  onSubscribe: (priceId: string) => Promise<void>;
  isLoading?: boolean;
}

const SubscriptionPlansCard: React.FC<SubscriptionPlansCardProps> = ({
  plans,
  onSubscribe,
  isLoading = false
}) => {
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);

  const handleSubscribeClick = async (priceId: string) => {
    setLoadingPlanId(priceId);
    try {
      await onSubscribe(priceId);
    } catch (error) {
      console.error('Error subscribing to plan:', error);
      // Error handling is typically done in the parent component
    } finally {
      setLoadingPlanId(null);
    }
  };

  const getBestValuePlan = () => {
    // Simple logic to identify the "best value" plan
    // You can customize this based on your pricing strategy
    if (plans.length <= 1) return null;
    
    // For this example, we'll assume the plan with "year" in the name or ID is the best value
    return plans.find(plan => 
      plan.name.toLowerCase().includes('year') || 
      plan.id.toLowerCase().includes('year') ||
      plan.features.some(feature => feature.toLowerCase().includes('save'))
    );
  };

  const bestValuePlan = getBestValuePlan();

  if (plans.length === 0) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-8 text-center">
        <h3 className="text-xl font-bold text-gray-900 mb-2">No Plans Available</h3>
        <p className="text-gray-600">Subscription plans are currently being set up. Please check back later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Choose Your Ad-Free Plan</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Support the platform and enjoy an uninterrupted, ad-free storytelling experience.
        </p>
      </div>

      {/* Plans Grid */}
      <div className={`grid gap-6 ${plans.length === 1 ? 'max-w-md mx-auto' : 'md:grid-cols-2 lg:grid-cols-3'}`}>
        {plans.map((plan) => {
          const isBestValue = bestValuePlan?.id === plan.id;
          const isCurrentlyLoading = loadingPlanId === plan.id;
          
          return (
            <div
              key={plan.id}
              className={`relative bg-white border rounded-lg shadow-lg overflow-hidden transition-transform duration-200 hover:scale-105 ${
                isBestValue ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'
              }`}
            >
              {/* Best Value Badge */}
              {isBestValue && (
                <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-sm font-semibold rounded-bl-lg">
                  Best Value
                </div>
              )}

              <div className="p-6 space-y-4">
                {/* Plan Header */}
                <div className="text-center">
                  <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                  <p className="text-gray-600 mt-1">{plan.description}</p>
                </div>

                {/* Pricing */}
                <div className="text-center">
                  <div className="text-4xl font-bold text-gray-900">{plan.price}</div>
                  {plan.name.toLowerCase().includes('year') && (
                    <p className="text-sm text-gray-500 mt-1">Billed annually</p>
                  )}
                  {plan.name.toLowerCase().includes('month') && (
                    <p className="text-sm text-gray-500 mt-1">Billed monthly</p>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-3">
                  <h4 className="font-semibold text-gray-900 text-center">What's included:</h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-gray-700">
                        <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Subscribe Button */}
                <button
                  onClick={() => handleSubscribeClick(plan.id)}
                  disabled={isLoading || isCurrentlyLoading}
                  className={`w-full py-3 px-4 rounded-lg font-semibold transition duration-150 ${
                    isBestValue
                      ? 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400'
                      : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400'
                  } disabled:cursor-not-allowed`}
                >
                  {isCurrentlyLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Redirecting...
                    </span>
                  ) : (
                    `Subscribe to ${plan.name}`
                  )}
                </button>
              </div>

              {/* Additional Info for Best Value */}
              {isBestValue && (
                <div className="bg-blue-50 border-t border-blue-200 px-6 py-3">
                  <p className="text-sm text-blue-700 text-center font-medium">
                    Most popular choice
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Additional Information */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="text-center text-sm text-gray-600 space-y-2">
          <p>
            <strong>🔒 Secure Payment:</strong> All payments are processed securely through Stripe.
          </p>
          <p>
            <strong>📧 Easy Cancellation:</strong> Cancel anytime through your subscription management page.
          </p>
          <p>
            <strong>💝 Support the Platform:</strong> Your subscription helps us keep the lights on and continue improving the experience.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlansCard;