import posthog from 'posthog-js';
import { hasConsentFor } from '@/utils/cookie-consent';

// PostHog configuration
const POSTHOG_KEY = import.meta.env.VITE_POSTHOG_KEY;
const POSTHOG_HOST = import.meta.env.VITE_POSTHOG_HOST || 'https://app.posthog.com';

// Initialize PostHog only in browser environment
if (typeof window !== 'undefined' && POSTHOG_KEY) {
  const isDisabled = import.meta.env.DEV && import.meta.env.VITE_POSTHOG_DISABLE_IN_DEV === 'true';
  const hasAnalyticsConsent = hasConsentFor('analytics');
  
  console.log('🔧 PostHog Configuration:', {
    hasKey: !!POSTHOG_KEY,
    host: POSTHOG_HOST,
    isDev: import.meta.env.DEV,
    disableSetting: import.meta.env.VITE_POSTHOG_DISABLE_IN_DEV,
    willBeDisabled: isDisabled,
    hasAnalyticsConsent,
  });

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    // Enable/disable specific features
    autocapture: hasAnalyticsConsent, // Only capture automatically if user consented
    capture_pageview: hasAnalyticsConsent, // Only capture page views if user consented
    capture_pageleave: hasAnalyticsConsent, // Only capture page leaves if user consented
    // Disable in development or if no analytics consent
    disabled: isDisabled || !hasAnalyticsConsent,
    // Cross-subdomain cookies
    cross_subdomain_cookie: false,
    // Session recording only with consent
    session_recording: hasAnalyticsConsent ? {
      maskAllInputs: true, // Mask all inputs for privacy
    } : undefined,
    // Data persistence
    persistence: 'localStorage+cookie',
    // Advanced settings
    secure_cookie: true,
    respect_dnt: true, // Respect Do Not Track
    // Person profiles
    person_profiles: 'identified_only', // Only create profiles for identified users
  });

  console.log('✅ PostHog initialized!', 
    isDisabled ? '(DISABLED in dev)' : 
    !hasAnalyticsConsent ? '(DISABLED - no consent)' : 
    '(ACTIVE)');

  // Listen for cookie consent changes and update PostHog accordingly
  window.addEventListener('cookieConsentChanged', (event: any) => {
    const newConsent = event.detail;
    if (newConsent && newConsent.analytics) {
      // User granted analytics consent, enable PostHog
      posthog.set_config({ 
        disabled: isDisabled,
        autocapture: true,
        capture_pageview: true,
        capture_pageleave: true,
      });
      console.log('✅ PostHog enabled after consent');
    } else {
      // User revoked analytics consent, disable PostHog
      posthog.set_config({ 
        disabled: true,
        autocapture: false,
        capture_pageview: false,
        capture_pageleave: false,
      });
      console.log('❌ PostHog disabled after consent revoked');
    }
  });

  // Set up error handling
  window.addEventListener('error', (event) => {
    // Only track critical errors, not development noise, and only with consent
    if (!import.meta.env.DEV && hasAnalyticsConsent) {
      posthog.captureException(event.error);
    }
  });
} else {
  console.log('❌ PostHog NOT initialized:', {
    isWindow: typeof window !== 'undefined',
    hasKey: !!POSTHOG_KEY,
    keyValue: POSTHOG_KEY ? 'Set (hidden)' : 'Missing'
  });
}

// Export the configured PostHog instance
export { posthog };

// Event tracking utilities
export const analytics = {
  // User identification
  identify: (userId: string, properties?: Record<string, any>) => {
    if (typeof window !== 'undefined') {
      posthog.identify(userId, properties);
    }
  },

  // Reset user identity (useful for logout)
  reset: () => {
    if (typeof window !== 'undefined') {
      posthog.reset();
    }
  },

  // Track custom events
  track: (eventName: string, properties?: Record<string, any>) => {
    if (typeof window !== 'undefined') {
      posthog.capture(eventName, properties);
    }
  },

  // Set user properties
  setUserProperties: (properties: Record<string, any>) => {
    if (typeof window !== 'undefined') {
      posthog.people.set(properties);
    }
  },

  // Track page views manually (if autocapture is disabled)
  pageView: (pageName?: string) => {
    if (typeof window !== 'undefined') {
      posthog.capture('$pageview', { page: pageName || window.location.pathname });
    }
  },

  // Feature flags
  isFeatureEnabled: (flagKey: string): boolean => {
    if (typeof window !== 'undefined') {
      return posthog.isFeatureEnabled(flagKey);
    }
    return false;
  },

  // Get feature flag variant
  getFeatureFlag: (flagKey: string): string | boolean => {
    if (typeof window !== 'undefined') {
      return posthog.getFeatureFlag(flagKey);
    }
    return false;
  },
};

// Specific event tracking functions for the application
export const trackStoryEvent = {
  storyCreated: (storyId: string, properties?: Record<string, any>) => {
    analytics.track('story_created', {
      story_id: storyId,
      ...properties,
    });
  },

  storyContribution: (storyId: string, contributionType: 'word' | 'sentence' | 'paragraph', properties?: Record<string, any>) => {
    analytics.track('story_contribution', {
      story_id: storyId,
      contribution_type: contributionType,
      ...properties,
    });
  },

  storyCompleted: (storyId: string, totalContributions: number, properties?: Record<string, any>) => {
    analytics.track('story_completed', {
      story_id: storyId,
      total_contributions: totalContributions,
      ...properties,
    });
  },

  storyViewed: (storyId: string, properties?: Record<string, any>) => {
    analytics.track('story_viewed', {
      story_id: storyId,
      ...properties,
    });
  },

  storyVoted: (storyId: string, voteType: 'up' | 'down', properties?: Record<string, any>) => {
    analytics.track('story_voted', {
      story_id: storyId,
      vote_type: voteType,
      ...properties,
    });
  },
};

export const trackUserEvent = {
  userRegistered: (userId: string, registrationMethod: string, properties?: Record<string, any>) => {
    analytics.track('user_registered', {
      user_id: userId,
      registration_method: registrationMethod,
      ...properties,
    });
  },

  userLoggedIn: (userId: string, loginMethod: string, properties?: Record<string, any>) => {
    analytics.track('user_logged_in', {
      user_id: userId,
      login_method: loginMethod,
      ...properties,
    });
  },

  userLoggedOut: (properties?: Record<string, any>) => {
    analytics.track('user_logged_out', properties);
  },
};

export const trackSubscriptionEvent = {
  subscriptionStarted: (planId: string, planName: string, properties?: Record<string, any>) => {
    analytics.track('subscription_started', {
      plan_id: planId,
      plan_name: planName,
      ...properties,
    });
  },

  subscriptionCancelled: (planId: string, cancellationReason?: string, properties?: Record<string, any>) => {
    analytics.track('subscription_cancelled', {
      plan_id: planId,
      cancellation_reason: cancellationReason,
      ...properties,
    });
  },

  subscriptionUpgraded: (fromPlan: string, toPlan: string, properties?: Record<string, any>) => {
    analytics.track('subscription_upgraded', {
      from_plan: fromPlan,
      to_plan: toPlan,
      ...properties,
    });
  },

  paymentSuccessful: (amount: number, currency: string, planId: string, properties?: Record<string, any>) => {
    analytics.track('payment_successful', {
      amount,
      currency,
      plan_id: planId,
      ...properties,
    });
  },

  paymentFailed: (planId: string, errorCode?: string, properties?: Record<string, any>) => {
    analytics.track('payment_failed', {
      plan_id: planId,
      error_code: errorCode,
      ...properties,
    });
  },
};

export const trackEngagementEvent = {
  featureUsed: (featureName: string, properties?: Record<string, any>) => {
    analytics.track('feature_used', {
      feature_name: featureName,
      ...properties,
    });
  },

  timeSpent: (section: string, timeInSeconds: number, properties?: Record<string, any>) => {
    analytics.track('time_spent', {
      section,
      time_seconds: timeInSeconds,
      ...properties,
    });
  },

  searchPerformed: (query: string, resultsCount: number, properties?: Record<string, any>) => {
    analytics.track('search_performed', {
      query,
      results_count: resultsCount,
      ...properties,
    });
  },

  errorEncountered: (errorType: string, errorMessage: string, properties?: Record<string, any>) => {
    analytics.track('error_encountered', {
      error_type: errorType,
      error_message: errorMessage,
      ...properties,
    });
  },
};

export default posthog;