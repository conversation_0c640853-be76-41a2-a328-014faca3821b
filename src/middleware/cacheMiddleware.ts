import { Request, Response, NextFunction } from 'express';
import { cacheService, CacheKeys } from '../services/cacheService';
import crypto from 'crypto';

// Cache configuration for different endpoint types
interface CacheStrategy {
  ttl: number; // Time to live in seconds
  vary?: string[]; // Headers to vary cache by
  skipCache?: boolean; // Skip caching entirely
  keyGenerator?: (req: Request) => string; // Custom cache key generator
}

// Default cache strategies for different API endpoints
const CACHE_STRATEGIES: Record<string, CacheStrategy> = {
  // Story operations
  'GET:/api/stories/:id': {
    ttl: 1800, // 30 minutes
    vary: ['authorization'],
    keyGenerator: (req) => `story:${req.params.id}:${req.headers.authorization?.slice(-8) || 'anon'}`
  },
  'GET:/api/stories/:id/contributions': {
    ttl: 900, // 15 minutes
    vary: ['authorization'],
    keyGenerator: (req) => `story_contributions:${req.params.id}:${req.headers.authorization?.slice(-8) || 'anon'}`
  },
  'GET:/api/stories': {
    ttl: 600, // 10 minutes
    vary: ['authorization'],
    keyGenerator: (req) => {
      const query = new URLSearchParams(req.query as any).toString();
      const auth = req.headers.authorization?.slice(-8) || 'anon';
      return `stories_list:${auth}:${crypto.createHash('md5').update(query).digest('hex')}`;
    }
  },
  
  // Session operations (shorter cache times)
  'GET:/api/stories/:id/sessions': {
    ttl: 300, // 5 minutes
    vary: ['authorization'],
    keyGenerator: (req) => `sessions:${req.params.id}:${req.headers.authorization?.slice(-8) || 'anon'}`
  },
  
  // User operations
  'GET:/api/users/:id': {
    ttl: 3600, // 1 hour
    vary: ['authorization'],
    keyGenerator: (req) => `user:${req.params.id}:${req.headers.authorization?.slice(-8) || 'anon'}`
  },
  
  // Default strategy for unconfigured endpoints
  'DEFAULT': {
    ttl: 300, // 5 minutes
    vary: ['authorization'],
  }
};

// Methods that should never be cached
const NON_CACHEABLE_METHODS = ['POST', 'PUT', 'PATCH', 'DELETE'];

// Headers that indicate the response shouldn't be cached
const NON_CACHEABLE_RESPONSE_HEADERS = [
  'set-cookie',
  'x-rate-limit-remaining',
  'authorization'
];

// Cache control configuration
interface CacheControlOptions {
  maxAge?: number;
  private?: boolean;
  noCache?: boolean;
  noStore?: boolean;
  mustRevalidate?: boolean;
}

export class APICacheMiddleware {
  
  static generateCacheKey(req: Request, strategy?: CacheStrategy): string {
    // Use custom key generator if available
    if (strategy?.keyGenerator) {
      return strategy.keyGenerator(req);
    }
    
    // Default key generation
    const method = req.method;
    const path = req.route?.path || req.path;
    const query = new URLSearchParams(req.query as any).toString();
    const auth = req.headers.authorization?.slice(-8) || 'anon';
    
    // Create a hash for query parameters to keep keys manageable
    const queryHash = query ? crypto.createHash('md5').update(query).digest('hex').slice(0, 8) : '';
    
    return CacheKeys.API_RESPONSE(`${method}:${path}`, `${auth}:${queryHash}`);
  }
  
  static getStrategyForRequest(req: Request): CacheStrategy {
    const method = req.method;
    const path = req.route?.path || req.path;
    const routeKey = `${method}:${path}`;
    
    // Check for exact match first
    if (CACHE_STRATEGIES[routeKey]) {
      return CACHE_STRATEGIES[routeKey];
    }
    
    // Check for pattern matches (simplified pattern matching)
    for (const [pattern, strategy] of Object.entries(CACHE_STRATEGIES)) {
      if (pattern.includes(':') && path.includes('/')) {
        const patternParts = pattern.split('/');
        const pathParts = path.split('/');
        
        if (patternParts.length === pathParts.length) {
          const matches = patternParts.every((part, index) => {
            return part.startsWith(':') || part === pathParts[index];
          });
          
          if (matches) {
            return strategy;
          }
        }
      }
    }
    
    return CACHE_STRATEGIES.DEFAULT;
  }
  
  static shouldSkipCache(req: Request, res: Response): boolean {
    // Skip non-GET requests
    if (NON_CACHEABLE_METHODS.includes(req.method)) {
      return true;
    }
    
    // Skip if response has non-cacheable headers
    const hasNonCacheableHeaders = NON_CACHEABLE_RESPONSE_HEADERS.some(header => 
      res.getHeader(header)
    );
    
    if (hasNonCacheableHeaders) {
      return true;
    }
    
    // Skip if response status is not successful
    if (res.statusCode >= 400) {
      return true;
    }
    
    // Skip if Cache-Control: no-cache or no-store is present
    const cacheControl = res.getHeader('cache-control') as string;
    if (cacheControl && (cacheControl.includes('no-cache') || cacheControl.includes('no-store'))) {
      return true;
    }
    
    return false;
  }
  
  static setCacheControlHeaders(res: Response, options: CacheControlOptions): void {
    const directives: string[] = [];
    
    if (options.maxAge !== undefined) {
      directives.push(`max-age=${options.maxAge}`);
    }
    
    if (options.private) {
      directives.push('private');
    } else {
      directives.push('public');
    }
    
    if (options.noCache) {
      directives.push('no-cache');
    }
    
    if (options.noStore) {
      directives.push('no-store');
    }
    
    if (options.mustRevalidate) {
      directives.push('must-revalidate');
    }
    
    res.setHeader('Cache-Control', directives.join(', '));
  }
  
  // Main cache middleware function
  static cache() {
    return async (req: Request, res: Response, next: NextFunction) => {
      // Skip caching for non-GET requests initially
      if (req.method !== 'GET') {
        return next();
      }
      
      const strategy = APICacheMiddleware.getStrategyForRequest(req);
      
      if (strategy.skipCache) {
        return next();
      }
      
      const cacheKey = APICacheMiddleware.generateCacheKey(req, strategy);
      
      try {
        // Try to get cached response
        const cachedResponse = await cacheService.get<{
          data: any;
          headers: Record<string, string>;
          statusCode: number;
          timestamp: number;
        }>(cacheKey);
        
        if (cachedResponse) {
          console.log(`🔥 API Cache HIT: ${req.method} ${req.path}`);
          
          // Set cached headers
          Object.entries(cachedResponse.headers).forEach(([key, value]) => {
            res.setHeader(key, value);
          });
          
          // Add cache headers
          res.setHeader('X-Cache', 'HIT');
          res.setHeader('X-Cache-Key', cacheKey);
          res.setHeader('X-Cache-Age', Math.floor((Date.now() - cachedResponse.timestamp) / 1000));
          
          // Set cache control
          APICacheMiddleware.setCacheControlHeaders(res, {
            maxAge: strategy.ttl,
            private: strategy.vary?.includes('authorization'),
          });
          
          return res.status(cachedResponse.statusCode).json(cachedResponse.data);
        }
        
        console.log(`💾 API Cache MISS: ${req.method} ${req.path}`);
        
        // Override res.json to capture response
        const originalJson = res.json;
        const originalSend = res.send;
        let responseData: any = null;
        let responseCaptured = false;
        
        res.json = function(data: any) {
          if (!responseCaptured) {
            responseData = data;
            responseCaptured = true;
            
            // Cache the response after sending
            setImmediate(async () => {
              try {
                if (!APICacheMiddleware.shouldSkipCache(req, res)) {
                  const cacheData = {
                    data: responseData,
                    headers: {
                      'content-type': res.getHeader('content-type') as string || 'application/json',
                    },
                    statusCode: res.statusCode,
                    timestamp: Date.now(),
                  };
                  
                  await cacheService.set(cacheKey, cacheData, strategy.ttl);
                  console.log(`💾 Cached API response: ${cacheKey} (TTL: ${strategy.ttl}s)`);
                }
              } catch (error) {
                console.error('❌ Error caching API response:', error);
              }
            });
          }
          
          // Add cache headers
          res.setHeader('X-Cache', 'MISS');
          res.setHeader('X-Cache-Key', cacheKey);
          
          // Set cache control
          APICacheMiddleware.setCacheControlHeaders(res, {
            maxAge: strategy.ttl,
            private: strategy.vary?.includes('authorization'),
          });
          
          return originalJson.call(this, data);
        };
        
        res.send = function(data: any) {
          if (!responseCaptured && res.getHeader('content-type')?.toString().includes('application/json')) {
            try {
              responseData = JSON.parse(data);
              responseCaptured = true;
              
              // Cache logic same as above
              setImmediate(async () => {
                try {
                  if (!APICacheMiddleware.shouldSkipCache(req, res)) {
                    const cacheData = {
                      data: responseData,
                      headers: {
                        'content-type': res.getHeader('content-type') as string || 'application/json',
                      },
                      statusCode: res.statusCode,
                      timestamp: Date.now(),
                    };
                    
                    await cacheService.set(cacheKey, cacheData, strategy.ttl);
                    console.log(`💾 Cached API response: ${cacheKey} (TTL: ${strategy.ttl}s)`);
                  }
                } catch (error) {
                  console.error('❌ Error caching API response:', error);
                }
              });
            } catch (parseError) {
              // Not JSON, skip caching
            }
          }
          
          return originalSend.call(this, data);
        };
        
        next();
        
      } catch (error) {
        console.error('❌ Cache middleware error:', error);
        next();
      }
    };
  }
  
  // Middleware to invalidate cache on mutations
  static invalidateOnMutation() {
    return async (req: Request, res: Response, next: NextFunction) => {
      if (NON_CACHEABLE_METHODS.includes(req.method)) {
        // After the response is sent, invalidate related cache entries
        const originalJson = res.json;
        
        res.json = function(data: any) {
          const result = originalJson.call(this, data);
          
          // Invalidate cache after successful mutations
          if (res.statusCode >= 200 && res.statusCode < 300) {
            setImmediate(async () => {
              await APICacheMiddleware.invalidateRelatedCache(req, data);
            });
          }
          
          return result;
        };
      }
      
      next();
    };
  }
  
  static async invalidateRelatedCache(req: Request, responseData: any): Promise<void> {
    try {
      const path = req.path;
      const method = req.method;
      
      console.log(`🗑️ Invalidating cache for ${method} ${path}`);
      
      // Story-related invalidations
      if (path.includes('/stories/')) {
        const storyIdMatch = path.match(/\/stories\/([^\/]+)/);
        if (storyIdMatch) {
          const storyId = storyIdMatch[1];
          
          // Clear story-related cache entries
          const keysToInvalidate = [
            `story:${storyId}:*`,
            `story_contributions:${storyId}:*`,
            `sessions:${storyId}:*`,
            'stories_list:*', // Clear list cache when stories are modified
          ];
          
          for (const pattern of keysToInvalidate) {
            await cacheService.deletePattern(pattern);
          }
        }
      }
      
      // User-related invalidations
      if (path.includes('/users/')) {
        const userIdMatch = path.match(/\/users\/([^\/]+)/);
        if (userIdMatch) {
          const userId = userIdMatch[1];
          await cacheService.deletePattern(`user:${userId}:*`);
        }
      }
      
      console.log(`✅ Cache invalidation completed for ${method} ${path}`);
    } catch (error) {
      console.error('❌ Error invalidating cache:', error);
    }
  }
  
  // Health check endpoint for cache status
  static cacheHealthEndpoint() {
    return async (req: Request, res: Response) => {
      try {
        const health = await cacheService.healthCheck();
        const stats = cacheService.getStats();
        
        res.json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          cache: {
            health,
            stats,
            strategies: Object.keys(CACHE_STRATEGIES),
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        });
      }
    };
  }
}

export default APICacheMiddleware; 