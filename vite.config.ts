import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Force Vite to use the JS implementation of Rollup
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = "true";
process.env.ROLLUP_NATIVE_DISABLE = "true";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    port: 5173,
    proxy: {
      "/api": {
        target: "http://localhost:3001",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''), // Remove /api prefix when proxying
      },
    },
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
