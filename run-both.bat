@echo off
echo ===================================================
echo Word by Word Story - Starting Development Environment
echo ===================================================

echo Step 1: Starting Express Server with AI Proxy...
start cmd /k "run-express.bat"

echo Step 2: Wait 3 seconds for Express to initialize...
timeout /t 3

echo Step 3: Starting Vite Development Server...
start cmd /k "run-vite-patched.bat"

echo Done! Application is running in two separate windows.
echo - Express server: http://localhost:8080
echo - Vite dev server: http://localhost:5173
echo ===================================================