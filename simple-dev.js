// Simple development server using live-server
const { execSync } = require("child_process");

try {
  console.log("Installing live-server...");
  execSync("npm install -g live-server", { stdio: "inherit" });

  console.log("Starting live-server...");
  execSync("live-server --port=8080 --entry-file=index.html", {
    stdio: "inherit",
  });
} catch (error) {
  console.error("Error:", error);
  process.exit(1);
}
