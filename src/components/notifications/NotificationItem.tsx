import React from 'react';
import { AppNotification, useNotifications } from '@/contexts/NotificationContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import TransferNotificationItem from './TransferNotificationItem';

interface NotificationItemProps {
  notification: AppNotification;
  onCloseDropdown: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onCloseDropdown }) => {
  const { markAsRead } = useNotifications();
  const navigate = useNavigate();

  // Check if this is a transfer notification
  const transferTypes = ['transfer_sent', 'transfer_received', 'transfer_failed', 'transfer_limit', 'transfer_summary'];
  const isTransferNotification = transferTypes.includes(notification.type);

  const handleNotificationClick = async () => {
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }
    if (notification.story_id) {
      let path = `/story/${notification.story_id}`;
      if (notification.contribution_id) {
        path += `#contribution-${notification.contribution_id}`;
      }
      navigate(path);
    }
    onCloseDropdown();
  };

  const handleViewTransfer = (transferId: string) => {
    // Navigate to transfer details or open transfer dialog
    console.log('View transfer:', transferId);
    // You could navigate to a transfers page or open a modal
    // navigate(`/transfers/${transferId}`);
    onCloseDropdown();
  };

  // If this is a transfer notification, use the specialized component
  if (isTransferNotification) {
    return (
      <div className="p-1">
        <TransferNotificationItem
          id={notification.id}
          type={notification.type as any}
          content={notification.content as any}
          isRead={notification.is_read}
          createdAt={notification.created_at}
          onMarkAsRead={markAsRead}
          onViewTransfer={handleViewTransfer}
          className="border-0 shadow-none hover:bg-gray-50 dark:hover:bg-gray-700"
        />
      </div>
    );
  }

  // Default notification rendering for non-transfer notifications
  const actorInitials = notification.content.actor_username 
    ? notification.content.actor_username.substring(0, 2).toUpperCase() 
    : 'AN';

  return (
    <div
      className={cn(
        "p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",
        !notification.is_read && "bg-blue-50 dark:bg-blue-900/30 font-medium"
      )}
      onClick={handleNotificationClick}
    >
      <div className="flex items-start space-x-3">
        {/* Example Avatar - replace with actual src if available */}
        {/* <Avatar className="h-8 w-8">
          <AvatarImage src={undefined} alt={notification.content.actor_username} />
          <AvatarFallback>{actorInitials}</AvatarFallback>
        </Avatar> */}
        <div className="flex-1 min-w-0">
          <p className={cn("text-sm text-gray-700 dark:text-gray-200", !notification.is_read && "font-semibold")}>
            {notification.content.message}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
            {new Date(notification.created_at).toLocaleString()} {/* Consider using a date formatting library */}
          </p>
        </div>
        {!notification.is_read && (
          <div className="h-2.5 w-2.5 bg-blue-500 rounded-full self-center flex-shrink-0" title="Unread"></div>
        )}
      </div>
    </div>
  );
};

export default NotificationItem; 