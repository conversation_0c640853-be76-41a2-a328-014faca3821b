import React from "react";
import Logo from "./Logo";
import DesktopNav from "./DesktopNav";
import UserMenu from "./UserMenu";
import { ModeToggle } from "@/components/ui/ModeToggle";
import NotificationIcon from "@/components/notifications/NotificationIcon";

const Navbar = () => {
  return (
    <div className="container mx-auto flex items-center justify-between">
      <Logo />
      <div className="flex items-center gap-4">
        <DesktopNav />
        <NotificationIcon />
        <UserMenu />
        <ModeToggle />
      </div>
    </div>
  );
};

export default Navbar;
