import { Request, Response, NextFunction } from 'express';
import { ApiKeyService, ApiTier, TIER_LIMITS } from '../services/apiKeyService';
import { StripeApiIntegration } from '../services/stripeApiIntegration';
import { body, param, query, validationResult } from 'express-validator';

/**
 * Create a new API key for a user with automatic tier assignment based on Stripe subscription
 */
export const createApiKey = [
  // Validation middleware
  body('name')
    .notEmpty()
    .withMessage('API key name is required')
    .isLength({ max: 100 })
    .withMessage('Name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('allowedOrigins')
    .optional()
    .isArray()
    .withMessage('Allowed origins must be an array'),
  body('allowedIPs')
    .optional()
    .isArray()
    .withMessage('Allowed IPs must be an array'),
  body('expiresInDays')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Expiration must be between 1 and 365 days'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { 
        name, 
        description, 
        allowedOrigins, 
        allowedIPs, 
        expiresInDays
      } = req.body;

      // Get userId from authentication middleware
      const userId = req.userId;
      
      if (!userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User ID not found in request'
        });
      }

      // Calculate expiration date
      const expiresAt = expiresInDays 
        ? new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000)
        : undefined;

      // 🚀 NEW: Create API key with automatic tier assignment based on Stripe subscription
      const result = await StripeApiIntegration.createApiKeyWithSubscriptionTier({
        userId,
        name,
        description,
        allowedOrigins,
        allowedIPs,
        expiresAt
      });

      if (!result) {
        return res.status(500).json({
          error: 'Failed to create API key',
          message: 'Please try again later'
        });
      }

      const { apiKey, plainKey } = result;

      // Return the key details (including the plain key - only shown once!)
      res.status(201).json({
        message: 'API key created successfully',
        apiKey: {
          id: apiKey.id,
          name: apiKey.name,
          description: apiKey.description,
          tier: apiKey.tier,
          preview: apiKey.keyPreview,
          isActive: apiKey.isActive,
          createdAt: apiKey.createdAt,
          expiresAt: apiKey.expiresAt,
          limits: TIER_LIMITS[apiKey.tier],
          subscriptionId: apiKey.subscriptionId
        },
        key: plainKey, // ⚠️ This is the only time the full key is shown!
        warning: 'Please save this API key now. You will not be able to see it again.',
        pricing: apiKey.tier === ApiTier.PREMIUM ? {
          monthlyFee: 49.99,
          currency: 'USD',
          billingCycle: 'monthly',
          features: TIER_LIMITS[ApiTier.PREMIUM].features
        } : undefined,
        stripeInfo: apiKey.tier === ApiTier.FREE ? {
          message: 'Upgrade to Premium API for $49.99/month to unlock:',
          benefits: [
            '10,000 requests/hour (100x more than free)',
            'Full CRUD operations',
            'Real-time updates',
            'Priority caching',
            'Analytics dashboard',
            'Priority support'
          ],
          upgradeUrl: '/upgrade'
        } : undefined
      });

    } catch (error) {
      console.error('❌ Error creating API key:', error);
      next(error);
    }
  }
];

/**
 * Get all API keys for a user
 */
export const getUserApiKeys = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get userId from authentication middleware
    const userId = req.userId;
    
    if (!userId) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'User ID not found in request'
      });
    }

    // TODO: Implement database query to get user's API keys
    // For now, return empty array
    const apiKeys: any[] = [];

    res.json({
      apiKeys: apiKeys.map(key => ({
        id: key.id,
        name: key.name,
        description: key.description,
        tier: key.tier,
        preview: key.keyPreview,
        isActive: key.isActive,
        createdAt: key.createdAt,
        lastUsedAt: key.lastUsedAt,
        expiresAt: key.expiresAt,
        totalRequests: key.totalRequests,
        monthlyRequests: key.monthlyRequests,
        billingStatus: key.billingStatus,
        limits: TIER_LIMITS[key.tier]
      }))
    });

  } catch (error) {
    console.error('❌ Error fetching API keys:', error);
    next(error);
  }
};

/**
 * Get specific API key details
 */
export const getApiKey = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;

      // TODO: Implement database query to get specific API key
      // For now, return not found
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist or you do not have access to it'
      });

    } catch (error) {
      console.error('❌ Error fetching API key:', error);
      next(error);
    }
  }
];

/**
 * Update API key settings
 */
export const updateApiKey = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  body('name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('allowedOrigins')
    .optional()
    .isArray()
    .withMessage('Allowed origins must be an array'),
  body('allowedIPs')
    .optional()
    .isArray()
    .withMessage('Allowed IPs must be an array'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;
      const updates = req.body;

      // TODO: Implement API key update logic
      // For now, return not found
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist or you do not have access to it'
      });

    } catch (error) {
      console.error('❌ Error updating API key:', error);
      next(error);
    }
  }
];

/**
 * Rotate API key (generate new key, invalidate old one)
 */
export const rotateApiKey = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  body('reason')
    .notEmpty()
    .withMessage('Reason for rotation is required')
    .isLength({ max: 200 })
    .withMessage('Reason must be less than 200 characters'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;
      const { reason } = req.body;

      // Get user ID from authentication middleware
      const rotatedBy = req.userId;
      
      if (!rotatedBy) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User ID not found in request'
        });
      }

      const result = await ApiKeyService.rotateApiKey(keyId, rotatedBy, reason);

      if (!result.success) {
        return res.status(400).json({
          error: 'Failed to rotate API key',
          message: result.error || 'Unknown error occurred'
        });
      }

      res.json({
        message: 'API key rotated successfully',
        newKey: result.newKey,
        warning: 'Please update your applications with the new API key. The old key is now invalid.',
        rotatedAt: new Date().toISOString(),
        reason
      });

    } catch (error) {
      console.error('❌ Error rotating API key:', error);
      next(error);
    }
  }
];

/**
 * Deactivate API key
 */
export const deactivateApiKey = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  body('reason')
    .notEmpty()
    .withMessage('Reason for deactivation is required')
    .isLength({ max: 200 })
    .withMessage('Reason must be less than 200 characters'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;
      const { reason } = req.body;

      const success = await ApiKeyService.deactivateApiKey(keyId, reason);

      if (!success) {
        return res.status(404).json({
          error: 'Failed to deactivate API key',
          message: 'Key not found or already deactivated'
        });
      }

      res.json({
        message: 'API key deactivated successfully',
        keyId,
        deactivatedAt: new Date().toISOString(),
        reason
      });

    } catch (error) {
      console.error('❌ Error deactivating API key:', error);
      next(error);
    }
  }
];

/**
 * Get API key usage statistics
 */
export const getApiKeyUsage = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  query('period')
    .optional()
    .isIn(['hour', 'day', 'month'])
    .withMessage('Period must be hour, day, or month'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;
      const period = (req.query.period as 'hour' | 'day' | 'month') || 'hour';

      const stats = await ApiKeyService.getUsageStats(keyId, period);

      if (!stats) {
        return res.status(404).json({
          error: 'Usage statistics not found',
          message: 'No usage data available for this API key'
        });
      }

      res.json({
        keyId,
        period,
        stats,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error fetching usage statistics:', error);
      next(error);
    }
  }
];

/**
 * Generate billing report for API key with Stripe integration
 */
export const getBillingReport = [
  param('keyId').notEmpty().withMessage('Key ID is required'),
  query('month')
    .notEmpty()
    .isInt({ min: 1, max: 12 })
    .withMessage('Month must be between 1 and 12'),
  query('year')
    .notEmpty()
    .isInt({ min: 2024, max: 2030 })
    .withMessage('Year must be between 2024 and 2030'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const { keyId } = req.params;
      const month = parseInt(req.query.month as string);
      const year = parseInt(req.query.year as string);

      // Get userId from authentication middleware
      const userId = req.userId;
      
      if (!userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User ID not found in request'
        });
      }

      // 🚀 NEW: Get comprehensive billing summary from Stripe integration
      const billingSummary = await StripeApiIntegration.getBillingSummary(userId, month, year);
      
      if (!billingSummary) {
        return res.status(404).json({
          error: 'Billing report not found',
          message: 'No billing data available for the specified period'
        });
      }

      // Get traditional usage report
      const report = await ApiKeyService.generateBillingReport(keyId, month, year);

      res.json({
        keyId,
        period: {
          month,
          year
        },
        billing: {
          subscription: {
            tier: billingSummary.tier,
            monthlyFee: billingSummary.subscriptionFee,
            currency: 'USD'
          },
          usage: {
            totalRequests: billingSummary.requestCount,
            apiUsageCost: billingSummary.apiUsageCost,
            cacheHitRate: billingSummary.cacheHitRate,
            breakdown: report?.breakdown || []
          },
          summary: {
            totalCost: billingSummary.totalCost,
            subscriptionFee: billingSummary.subscriptionFee,
            usageFee: billingSummary.apiUsageCost,
            savings: {
              caching: `${(billingSummary.cacheHitRate * 100).toFixed(1)}% cache hit rate`,
              estimatedSavings: `$${(billingSummary.apiUsageCost * 0.9).toFixed(2)} saved from caching`
            }
          }
        },
        generatedAt: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error generating billing report:', error);
      next(error);
    }
  }
];

/**
 * Get comprehensive billing summary for user (across all API keys)
 */
export const getBillingSummary = [
  query('month')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('Month must be between 1 and 12'),
  query('year')
    .optional()
    .isInt({ min: 2024, max: 2030 })
    .withMessage('Year must be between 2024 and 2030'),
  
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        });
      }

      // Get userId from authentication middleware
      const userId = req.userId;
      
      if (!userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User ID not found in request'
        });
      }
      
      const month = parseInt(req.query.month as string) || new Date().getMonth() + 1;
      const year = parseInt(req.query.year as string) || new Date().getFullYear();

      // Get comprehensive billing summary from Stripe integration
      const billingSummary = await StripeApiIntegration.getBillingSummary(userId, month, year);
      
      if (!billingSummary) {
        return res.status(404).json({
          error: 'Billing summary not found',
          message: 'No billing data available'
        });
      }

      res.json({
        period: { month, year },
        subscription: {
          tier: billingSummary.tier,
          monthlyFee: billingSummary.subscriptionFee,
          currency: 'USD',
          limits: TIER_LIMITS[billingSummary.tier]
        },
        usage: {
          totalRequests: billingSummary.requestCount,
          cacheHitRate: billingSummary.cacheHitRate,
          apiUsageCost: billingSummary.apiUsageCost
        },
        costs: {
          subscriptionFee: billingSummary.subscriptionFee,
          usageFee: billingSummary.apiUsageCost,
          totalCost: billingSummary.totalCost
        },
        optimization: {
          cacheHitRate: `${(billingSummary.cacheHitRate * 100).toFixed(1)}%`,
          estimatedSavings: `$${(billingSummary.apiUsageCost * 0.9).toFixed(2)}`,
          recommendations: billingSummary.tier === ApiTier.FREE ? [
            'Upgrade to Premium for 100x more requests',
            'Get priority caching to reduce costs',
            'Access real-time analytics'
          ] : [
            'Your premium caching is optimizing costs',
            'Consider Enterprise for higher limits',
            'Monitor usage patterns for efficiency'
          ]
        },
        generatedAt: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error generating billing summary:', error);
      next(error);
    }
  }
];

/**
 * Get pricing information for API tiers
 */
export const getPricingInfo = (req: Request, res: Response) => {
  res.json({
    tiers: {
      [ApiTier.FREE]: {
        name: 'Free',
        price: 0,
        currency: 'USD',
        billingCycle: 'monthly',
        limits: TIER_LIMITS[ApiTier.FREE],
        features: [
          'Basic story access',
          'Read-only operations',
          'Community support',
          '100 requests/hour',
          '1,000 requests/day'
        ]
      },
      [ApiTier.PREMIUM]: {
        name: 'Premium',
        price: 49.99,
        currency: 'USD',
        billingCycle: 'monthly',
        limits: TIER_LIMITS[ApiTier.PREMIUM],
        features: [
          'Full API access',
          'Create and modify stories',
          'Real-time updates',
          'Priority caching',
          'Analytics dashboard',
          'Priority support',
          '10,000 requests/hour',
          '100,000 requests/day',
          'Premium endpoints'
        ],
        popular: true
      },
      [ApiTier.ENTERPRISE]: {
        name: 'Enterprise',
        price: 'Custom',
        currency: 'USD',
        billingCycle: 'monthly',
        limits: TIER_LIMITS[ApiTier.ENTERPRISE],
        features: [
          'Everything in Premium',
          'Custom integrations',
          'Dedicated support',
          'SLA guarantees',
          '50,000 requests/hour',
          '500,000 requests/day',
          'White-label options'
        ],
        contact: true
      }
    },
    costCalculation: {
      baseEndpointCosts: {
        'GET requests': '0.1¢',
        'POST requests': '0.5-1.0¢',
        'PUT/DELETE requests': '0.5-0.8¢'
      },
      discounts: {
        cachedResponses: '90% off',
        premiumTier: 'Priority caching',
        enterpriseTier: 'Dedicated resources'
      }
    },
    upgradeUrl: '/upgrade',
    contactUrl: '/contact-sales'
  });
};

/**
 * Health check for API key management
 */
export const healthCheck = (req: Request, res: Response) => {
  res.json({
    status: 'healthy',
    service: 'api-key-management',
    timestamp: new Date().toISOString(),
    features: {
      keyGeneration: true,
      validation: true,
      ratelimiting: true,
      billing: true,
      analytics: true
    },
    supportedTiers: Object.values(ApiTier)
  });
}; 