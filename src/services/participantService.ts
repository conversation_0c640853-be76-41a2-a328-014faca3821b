/**
 * Participant Management Service
 * Handles story creator controls for dynamic participant management
 */

import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { v4 as uuidv4 } from 'uuid';

export interface StoryParticipant {
  id: string;
  story_id: string;
  user_id: string;
  role: 'creator' | 'contributor' | 'viewer';
  joined_at: string;
  invited_by?: string;
  status: 'active' | 'muted' | 'banned';
  created_at: string;
  updated_at: string;
  // Joined profile data
  profile?: {
    username?: string;
    avatar_url?: string;
  };
}

export interface StoryInvitation {
  id: string;
  story_id: string;
  inviter_user_id: string;
  invite_code: string;
  email?: string;
  permissions: 'contributor' | 'viewer';
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  expires_at: string;
  max_uses: number;
  current_uses: number;
  created_at: string;
  updated_at: string;
}

export interface ParticipantManagementResult {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Get all participants for a story
 */
export const getStoryParticipants = async (
  storyId: string
): Promise<StoryParticipant[]> => {
  try {
    const { data, error } = await supabase
      .from('story_participants')
      .select(`
        *,
        profile:user_id (
          username,
          avatar_url
        )
      `)
      .eq('story_id', storyId)
      .eq('status', 'active')
      .order('joined_at', { ascending: true });

    if (error) {
      console.error('Error fetching story participants:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getStoryParticipants:', error);
    return [];
  }
};

/**
 * Check if user is the story creator
 */
export const isStoryCreator = async (
  storyId: string,
  userId: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('story_participants')
      .select('role')
      .eq('story_id', storyId)
      .eq('user_id', userId)
      .eq('role', 'creator')
      .single();

    return !error && !!data;
  } catch (error) {
    console.error('Error checking story creator status:', error);
    return false;
  }
};

/**
 * Remove participant from story (creator only)
 */
export const removeParticipant = async (
  storyId: string,
  participantUserId: string,
  creatorUserId: string
): Promise<ParticipantManagementResult> => {
  try {
    // Verify creator permissions
    const isCreator = await isStoryCreator(storyId, creatorUserId);
    if (!isCreator) {
      return {
        success: false,
        error: 'Only the story creator can remove participants'
      };
    }

    // Prevent creator from removing themselves
    if (participantUserId === creatorUserId) {
      return {
        success: false,
        error: 'Story creator cannot remove themselves'
      };
    }

    // Remove participant
    const { error } = await supabase
      .from('story_participants')
      .delete()
      .eq('story_id', storyId)
      .eq('user_id', participantUserId);

    if (error) {
      console.error('Error removing participant:', error);
      return {
        success: false,
        error: 'Failed to remove participant'
      };
    }

    toast({
      title: 'Participant removed',
      description: 'The participant has been removed from the story.',
    });

    return { success: true };
  } catch (error) {
    console.error('Error in removeParticipant:', error);
    return {
      success: false,
      error: 'Failed to remove participant'
    };
  }
};

/**
 * Update participant role (creator only)
 */
export const updateParticipantRole = async (
  storyId: string,
  participantUserId: string,
  newRole: 'contributor' | 'viewer',
  creatorUserId: string
): Promise<ParticipantManagementResult> => {
  try {
    // Verify creator permissions
    const isCreator = await isStoryCreator(storyId, creatorUserId);
    if (!isCreator) {
      return {
        success: false,
        error: 'Only the story creator can update participant roles'
      };
    }

    // Prevent changing creator role
    if (participantUserId === creatorUserId) {
      return {
        success: false,
        error: 'Cannot change creator role'
      };
    }

    // Update participant role
    const { error } = await supabase
      .from('story_participants')
      .update({ 
        role: newRole,
        updated_at: new Date().toISOString()
      })
      .eq('story_id', storyId)
      .eq('user_id', participantUserId);

    if (error) {
      console.error('Error updating participant role:', error);
      return {
        success: false,
        error: 'Failed to update participant role'
      };
    }

    toast({
      title: 'Role updated',
      description: `Participant role has been updated to ${newRole}.`,
    });

    return { success: true };
  } catch (error) {
    console.error('Error in updateParticipantRole:', error);
    return {
      success: false,
      error: 'Failed to update participant role'
    };
  }
};

/**
 * Mute/unmute participant (creator only)
 */
export const toggleParticipantMute = async (
  storyId: string,
  participantUserId: string,
  mute: boolean,
  creatorUserId: string
): Promise<ParticipantManagementResult> => {
  try {
    // Verify creator permissions
    const isCreator = await isStoryCreator(storyId, creatorUserId);
    if (!isCreator) {
      return {
        success: false,
        error: 'Only the story creator can mute participants'
      };
    }

    // Prevent muting creator
    if (participantUserId === creatorUserId) {
      return {
        success: false,
        error: 'Cannot mute the story creator'
      };
    }

    // Update participant status
    const { error } = await supabase
      .from('story_participants')
      .update({ 
        status: mute ? 'muted' : 'active',
        updated_at: new Date().toISOString()
      })
      .eq('story_id', storyId)
      .eq('user_id', participantUserId);

    if (error) {
      console.error('Error updating participant mute status:', error);
      return {
        success: false,
        error: 'Failed to update participant status'
      };
    }

    toast({
      title: mute ? 'Participant muted' : 'Participant unmuted',
      description: mute 
        ? 'The participant has been muted and cannot contribute until unmuted.'
        : 'The participant can now contribute to the story again.',
    });

    return { success: true };
  } catch (error) {
    console.error('Error in toggleParticipantMute:', error);
    return {
      success: false,
      error: 'Failed to update participant status'
    };
  }
};

/**
 * Create story invitation
 */
export const createStoryInvitation = async (
  storyId: string,
  permissions: 'contributor' | 'viewer',
  creatorUserId: string,
  options: {
    email?: string;
    maxUses?: number;
    expiresInDays?: number;
  } = {}
): Promise<ParticipantManagementResult> => {
  try {
    // Verify creator permissions
    const isCreator = await isStoryCreator(storyId, creatorUserId);
    if (!isCreator) {
      return {
        success: false,
        error: 'Only the story creator can create invitations'
      };
    }

    const {
      email,
      maxUses = 1,
      expiresInDays = 7
    } = options;

    // Generate unique invite code
    const inviteCode = uuidv4().substring(0, 8);
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    // Create invitation
    const { data, error } = await supabase
      .from('story_invitations')
      .insert({
        story_id: storyId,
        inviter_user_id: creatorUserId,
        invite_code: inviteCode,
        email,
        permissions,
        expires_at: expiresAt.toISOString(),
        max_uses: maxUses
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating story invitation:', error);
      return {
        success: false,
        error: 'Failed to create invitation'
      };
    }

    toast({
      title: 'Invitation created',
      description: `Invitation created with code: ${inviteCode}`,
    });

    return { 
      success: true, 
      data: { 
        invitation: data,
        inviteLink: `${window.location.origin}/join/${inviteCode}`
      }
    };
  } catch (error) {
    console.error('Error in createStoryInvitation:', error);
    return {
      success: false,
      error: 'Failed to create invitation'
    };
  }
};

/**
 * Accept story invitation
 */
export const acceptStoryInvitation = async (
  inviteCode: string,
  userId: string
): Promise<ParticipantManagementResult> => {
  try {
    // Find valid invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('story_invitations')
      .select('*')
      .eq('invite_code', inviteCode)
      .eq('status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .single();

    if (inviteError || !invitation) {
      return {
        success: false,
        error: 'Invalid or expired invitation code'
      };
    }

    // Check usage limits
    if (invitation.current_uses >= invitation.max_uses) {
      return {
        success: false,
        error: 'Invitation has reached its usage limit'
      };
    }

    // Check if user is already a participant
    const { data: existingParticipant } = await supabase
      .from('story_participants')
      .select('id')
      .eq('story_id', invitation.story_id)
      .eq('user_id', userId)
      .single();

    if (existingParticipant) {
      return {
        success: false,
        error: 'You are already a participant in this story'
      };
    }

    // Add participant
    const { error: participantError } = await supabase
      .from('story_participants')
      .insert({
        story_id: invitation.story_id,
        user_id: userId,
        role: invitation.permissions,
        invited_by: invitation.inviter_user_id
      });

    if (participantError) {
      console.error('Error adding participant:', participantError);
      return {
        success: false,
        error: 'Failed to join story'
      };
    }

    // Update invitation usage
    const { error: updateError } = await supabase
      .from('story_invitations')
      .update({ 
        current_uses: invitation.current_uses + 1,
        status: invitation.current_uses + 1 >= invitation.max_uses ? 'accepted' : 'pending'
      })
      .eq('id', invitation.id);

    if (updateError) {
      console.error('Error updating invitation usage:', updateError);
    }

    toast({
      title: 'Welcome to the story!',
      description: `You have successfully joined as a ${invitation.permissions}.`,
    });

    return { 
      success: true,
      data: { 
        storyId: invitation.story_id,
        role: invitation.permissions
      }
    };
  } catch (error) {
    console.error('Error in acceptStoryInvitation:', error);
    return {
      success: false,
      error: 'Failed to accept invitation'
    };
  }
};

/**
 * Get story invitations (creator only)
 */
export const getStoryInvitations = async (
  storyId: string,
  creatorUserId: string
): Promise<StoryInvitation[]> => {
  try {
    // Verify creator permissions
    const isCreator = await isStoryCreator(storyId, creatorUserId);
    if (!isCreator) {
      return [];
    }

    const { data, error } = await supabase
      .from('story_invitations')
      .select('*')
      .eq('story_id', storyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching story invitations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getStoryInvitations:', error);
    return [];
  }
};

/**
 * Revoke story invitation (creator only)
 */
export const revokeStoryInvitation = async (
  invitationId: string,
  creatorUserId: string
): Promise<ParticipantManagementResult> => {
  try {
    // Get invitation to verify creator permissions
    const { data: invitation, error: inviteError } = await supabase
      .from('story_invitations')
      .select('story_id')
      .eq('id', invitationId)
      .single();

    if (inviteError || !invitation) {
      return {
        success: false,
        error: 'Invitation not found'
      };
    }

    // Verify creator permissions
    const isCreator = await isStoryCreator(invitation.story_id, creatorUserId);
    if (!isCreator) {
      return {
        success: false,
        error: 'Only the story creator can revoke invitations'
      };
    }

    // Revoke invitation
    const { error } = await supabase
      .from('story_invitations')
      .update({ 
        status: 'revoked',
        updated_at: new Date().toISOString()
      })
      .eq('id', invitationId);

    if (error) {
      console.error('Error revoking invitation:', error);
      return {
        success: false,
        error: 'Failed to revoke invitation'
      };
    }

    toast({
      title: 'Invitation revoked',
      description: 'The invitation has been revoked and can no longer be used.',
    });

    return { success: true };
  } catch (error) {
    console.error('Error in revokeStoryInvitation:', error);
    return {
      success: false,
      error: 'Failed to revoke invitation'
    };
  }
};

/**
 * Clean up expired invitations (utility function)
 */
export const cleanupExpiredInvitations = async (): Promise<void> => {
  try {
    const { error } = await supabase.rpc('expire_old_invitations');
    
    if (error) {
      console.error('Error cleaning up expired invitations:', error);
    }
  } catch (error) {
    console.error('Error in cleanupExpiredInvitations:', error);
  }
};