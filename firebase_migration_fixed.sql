-- Firebase Auth Compatibility Migration - Fixed Version
-- This handles existing data and constraints more carefully

-- First, check if notifications table has any data
DO $$
BEGIN
    -- Drop the trigger and function first
    DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
    DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
    
    -- Drop all foreign key constraints on notifications table
    ALTER TABLE public.notifications 
        DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_story_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_contribution_id_fkey;
    
    -- Drop all foreign key constraints that reference user_profiles.id
    ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
    ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;
    
    -- Now we can safely alter the column types
    -- First, clear any existing data if it exists (optional - comment out if you want to keep data)
    -- TRUNCATE TABLE public.notifications CASCADE;
    
    -- Change notification columns to TEXT
    ALTER TABLE public.notifications 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT,
        ALTER COLUMN actor_id TYPE TEXT USING actor_id::TEXT;
    
    -- Change user_profiles id to TEXT
    ALTER TABLE public.user_profiles 
        ALTER COLUMN id TYPE TEXT USING id::TEXT;
    
    -- Change stories author_id to TEXT
    ALTER TABLE public.stories 
        ALTER COLUMN author_id TYPE TEXT USING author_id::TEXT;
    
    -- Change contributions user_id to TEXT
    ALTER TABLE public.contributions 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;
END $$;

-- Drop and recreate indexes
DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
DROP INDEX IF EXISTS idx_notifications_user_id_created_at;

CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);

-- Drop all existing RLS policies
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications (mark as read/unread)" ON public.notifications;
DROP POLICY IF EXISTS "Users can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Stories are viewable by everyone" ON public.stories;
DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;
DROP POLICY IF EXISTS "Contributions are viewable by everyone" ON public.contributions;
DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;

-- Create new service role policies
CREATE POLICY "Service role can manage all notifications"
ON public.notifications FOR ALL 
USING (true)
WITH CHECK (true);

CREATE POLICY "Service role can manage all profiles"
ON public.user_profiles FOR ALL 
USING (true)
WITH CHECK (true);

CREATE POLICY "Service role can manage all stories"
ON public.stories FOR ALL 
USING (true)
WITH CHECK (true);

CREATE POLICY "Service role can manage all contributions"
ON public.contributions FOR ALL 
USING (true)
WITH CHECK (true); 