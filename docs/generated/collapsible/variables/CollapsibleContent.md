[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [collapsible](../README.md) / CollapsibleContent

# Variable: CollapsibleContent

> `const` **CollapsibleContent**: `ForwardRefExoticComponent`\<`CollapsibleContentProps` & `RefAttributes`\<`HTMLDivElement`\>\> = `CollapsiblePrimitive.CollapsibleContent`

Defined in: [src/components/ui/collapsible.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/collapsible.tsx#L7)
