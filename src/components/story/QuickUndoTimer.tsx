import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Undo2, Clock } from 'lucide-react';

interface QuickUndoTimerProps {
  contributionId: string;
  contributionContent: string;
  onUndo: (contributionId: string) => Promise<void>;
  onExpire: () => void;
  duration?: number; // in seconds, default 3
}

export const QuickUndoTimer: React.FC<QuickUndoTimerProps> = ({
  contributionId,
  contributionContent,
  onUndo,
  onExpire,
  duration = 3,
}) => {
  const [timeLeft, setTimeLeft] = useState(duration);
  const [isUndoing, setIsUndoing] = useState(false);

  useEffect(() => {
    if (timeLeft <= 0) {
      onExpire();
      return;
    }

    const timer = setTimeout(() => {
      setTimeLeft(timeLeft - 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [timeLeft, onExpire]);

  const handleUndo = async () => {
    setIsUndoing(true);
    try {
      await onUndo(contributionId);
    } catch (error) {
      console.error('Quick undo failed:', error);
    } finally {
      setIsUndoing(false);
    }
  };

  // Calculate progress percentage for visual indicator
  const progress = ((duration - timeLeft) / duration) * 100;

  return (
    <Card className="border-amber-200 bg-amber-50 shadow-lg">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Clock className="h-5 w-5 text-amber-600" />
            <div>
              <p className="text-sm font-medium text-amber-800">
                Just submitted: "{contributionContent.length > 50 ? contributionContent.substring(0, 50) + '...' : contributionContent}"
              </p>
              <p className="text-xs text-amber-600">
                Quick undo available for {timeLeft} more second{timeLeft !== 1 ? 's' : ''}
              </p>
            </div>
          </div>

          <Button
            onClick={handleUndo}
            disabled={isUndoing || timeLeft <= 0}
            size="sm"
            className="bg-amber-600 hover:bg-amber-700 text-white"
          >
            {isUndoing ? (
              <>
                <div className="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full" />
                Undoing...
              </>
            ) : (
              <>
                <Undo2 className="h-4 w-4 mr-2" />
                Undo ({timeLeft}s)
              </>
            )}
          </Button>
        </div>

        {/* Progress bar */}
        <div className="mt-3 w-full bg-amber-200 rounded-full h-2">
          <div
            className="bg-amber-600 h-2 rounded-full transition-all duration-1000 ease-linear"
            style={{ width: `${progress}%` }}
          />
        </div>
      </CardContent>
    </Card>
  );
}; 