import React from "react";
import { Pen<PERSON><PERSON>, <PERSON>, BookOpen } from "lucide-react";

const HowItWorksSection: React.FC = () => {
  return (
    <div className="bg-literary-paper py-16">
      <div className="container mx-auto">
        <h2 className="text-3xl font-serif font-bold text-center mb-12">
          How It Works
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <StepItem
            icon={<PenLine size={32} className="text-literary-burgundy" />}
            title="Start a Story"
            description="Begin with a title and the first word. Invite friends to join in and contribute."
          />
          <StepItem
            icon={<Users size={32} className="text-literary-burgundy" />}
            title="Take Turns"
            description="Each participant adds one word at a time, building the story collaboratively."
          />
          <StepItem
            icon={<BookOpen size={32} className="text-literary-burgundy" />}
            title="Share & Publish"
            description="Once completed, publish your story and share it in the community gallery."
          />
        </div>
      </div>
    </div>
  );
};

interface StepItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const StepItem: React.FC<StepItemProps> = ({ icon, title, description }) => (
  <div className="flex flex-col items-center text-center">
    <div className="bg-literary-burgundy/10 p-4 rounded-full mb-4">{icon}</div>
    <h3 className="text-xl font-medium mb-2">{title}</h3>
    <p className="text-gray-600">{description}</p>
  </div>
);

export default HowItWorksSection;
