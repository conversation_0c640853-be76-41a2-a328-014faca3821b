# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Lint/Test Commands

- Dev server: `yarn start:development`
- Build: `yarn build` (production) or `yarn build:dev` (development)
- Lint: `yarn lint`
- Test: `yarn test` (run all tests)
- Test single file: `yarn test src/__tests__/example.test.ts`
- Test watch mode: `yarn test:watch`

## Code Style Guidelines

- TypeScript with relaxed settings (non-strict, allows any, no unused vars/params)
- React functional components with hooks
- Use Tailwind CSS for styling with shadcn UI components
- Use literary-inspired color palette from tailwind config
- Follow existing import patterns in files (React/hooks first, then components)
- Error handling: use try/catch blocks for async operations
- Naming: PascalCase for components/types, camelCase for variables/functions

## Rules

- Use Azure Best Practices for any Azure-related code (from .github/copilot-instructions.md)
- Verify functionality with tests wherever possible
- DeepInfra API is used for AI suggestions with key: `DEEPINFRA_API_KEY="ATpyHKztK8CFLeJwHn7IewW8ifIgHwWX"` (set in Vercel environment)
- Default model for AI services is meta-llama/Llama-3-8b-chat-hf
