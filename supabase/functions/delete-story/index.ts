import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) {
    console.error('Auth error or no user:', error);
    throw { message: 'User not authenticated or auth error', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'DELETE') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use DELETE.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  let supabaseClient: SupabaseClient;

  try {
    const tempSupabase = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false }
    });
    await getAuthenticatedUser(req, tempSupabase); // Ensures user is authenticated

    supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false }
    });

    const url = new URL(req.url);
    const storyId = url.searchParams.get('id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'Story ID is required as a query parameter (e.g., ?id=your-story-id).' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const { error, count } = await supabaseClient
      .from('stories')
      .delete({ count: 'exact' }) // Request count of deleted rows
      .eq('id', storyId);

    if (error) {
      console.error('Supabase delete error:', error);
      // RLS denial might also result in an error or 0 count
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500, // Or a more specific code if available from error
      });
    }

    if (count === 0) {
      // This means no row matched the ID, or RLS prevented the delete on the row that did match.
      return new Response(JSON.stringify({ error: 'Story not found or delete not permitted.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404, 
      });
    }
    
    // If count is greater than 0, deletion was successful due to RLS allowing it.
    return new Response(null, { // 204 No Content
      headers: { ...corsHeaders },
      status: 204,
    });

  } catch (err) {
    console.error('Error processing DELETE request:', err);
    const status = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status,
    });
  }
}); 