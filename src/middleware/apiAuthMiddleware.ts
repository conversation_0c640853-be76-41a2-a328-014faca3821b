import { Request, Response, NextFunction } from 'express';
import { ApiKeyService, ApiTier, TIER_LIMITS, ApiKey, ApiUsage } from '../services/apiKeyService';

// Extend Express Request to include API key info
declare global {
  namespace Express {
    interface Request {
      apiKey?: ApiKey;
      tier?: ApiTier;
      rateLimitInfo?: {
        remaining: number;
        resetTime: Date;
      };
    }
  }
}

// Cost calculation per endpoint (in cents)
const ENDPOINT_COSTS = {
  'GET:/api/stories': 0.1,
  'GET:/api/stories/:id': 0.2,
  'GET:/api/stories/:id/contributions': 0.3,
  'POST:/api/stories': 1.0,
  'POST:/api/stories/:id/contributions': 0.5,
  'GET:/api/stories/:id/sessions': 0.1,
  'POST:/api/stories/:id/sessions': 0.1,
  'PUT:/api/stories/:id': 0.8,
  'DELETE:/api/stories/:id': 0.5,
  'default': 0.1
} as const;

// Feature access control per tier
const FEATURE_ACCESS = {
  [ApiTier.FREE]: new Set([
    'GET:/api/stories',
    'GET:/api/stories/:id',
    'GET:/api/stories/:id/contributions'
  ]),
  [ApiTier.PREMIUM]: new Set([
    'GET:/api/stories',
    'GET:/api/stories/:id', 
    'GET:/api/stories/:id/contributions',
    'POST:/api/stories',
    'POST:/api/stories/:id/contributions',
    'GET:/api/stories/:id/sessions',
    'POST:/api/stories/:id/sessions',
    'PUT:/api/stories/:id',
    'GET:/api/analytics',
    'GET:/api/premium/*'
  ]),
  [ApiTier.ENTERPRISE]: new Set(['*']) // All access
} as const;

export class ApiAuthMiddleware {
  
  // Extract API key from request
  static extractApiKey(req: Request): string | null {
    // Check Authorization header: Bearer <api-key>
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // Check x-api-key header
    const apiKeyHeader = req.headers['x-api-key'] as string;
    if (apiKeyHeader) {
      return apiKeyHeader;
    }
    
    // Check query parameter (less secure, for testing only)
    const apiKeyQuery = req.query.api_key as string;
    if (apiKeyQuery && process.env.NODE_ENV === 'development') {
      return apiKeyQuery;
    }
    
    return null;
  }
  
  // Calculate cost for endpoint
  static calculateCost(req: Request, responseTime: number, cached: boolean): number {
    const method = req.method;
    const path = req.route?.path || req.path;
    const endpoint = `${method}:${path}`;
    
    let baseCost = ENDPOINT_COSTS[endpoint as keyof typeof ENDPOINT_COSTS] || ENDPOINT_COSTS.default;
    
    // Reduce cost if served from cache
    if (cached) {
      baseCost *= 0.1; // 90% discount for cached responses
    }
    
    // Increase cost for slow responses (server load)
    if (responseTime > 1000) {
      baseCost *= 1.5;
    }
    
    return Math.round(baseCost * 100) / 100; // Round to 2 decimal places
  }
  
  // Check if endpoint is accessible for tier
  static hasAccess(tier: ApiTier, method: string, path: string): boolean {
    const endpoint = `${method}:${path}`;
    const allowedEndpoints = FEATURE_ACCESS[tier];
    
    // Enterprise has access to everything
    if (tier === ApiTier.ENTERPRISE) {
      return true;
    }
    
    // Check exact match
    if (allowedEndpoints.has(endpoint)) {
      return true;
    }
    
    // Check wildcard patterns
    for (const pattern of allowedEndpoints) {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        if (regex.test(endpoint)) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  // Main authentication middleware
  static authenticate(options: {
    required?: boolean;
    allowFree?: boolean;
    premiumOnly?: boolean;
    minTier?: ApiTier;
  } = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      const startTime = Date.now();
      
      try {
        const apiKey = ApiAuthMiddleware.extractApiKey(req);
        
        // Handle missing API key
        if (!apiKey) {
          if (options.required) {
            return res.status(401).json({
              error: 'API key required',
              message: 'Please provide a valid API key in the Authorization header or x-api-key header',
              code: 'API_KEY_MISSING'
            });
          }
          
          // Allow anonymous access for non-required endpoints
          req.tier = ApiTier.FREE;
          return next();
        }
        
        // Validate API key
        const validation = await ApiKeyService.validateApiKey(apiKey);
        
        if (!validation.valid || !validation.apiKey) {
          return res.status(401).json({
            error: 'Invalid API key',
            message: validation.error || 'API key validation failed',
            code: 'API_KEY_INVALID'
          });
        }
        
        const { apiKey: keyData } = validation;
        
        // Check tier requirements
        if (options.premiumOnly && keyData.tier === ApiTier.FREE) {
          return res.status(403).json({
            error: 'Premium subscription required',
            message: 'This endpoint requires a premium subscription ($49.99/month)',
            code: 'PREMIUM_REQUIRED',
            upgradeUrl: '/upgrade'
          });
        }
        
        if (options.minTier && !this.meetsTierRequirement(keyData.tier, options.minTier)) {
          return res.status(403).json({
            error: 'Insufficient subscription tier',
            message: `This endpoint requires ${options.minTier} tier or higher`,
            code: 'TIER_INSUFFICIENT'
          });
        }
        
        // Check feature access
        if (!ApiAuthMiddleware.hasAccess(keyData.tier, req.method, req.path)) {
          return res.status(403).json({
            error: 'Feature not available',
            message: `Access to ${req.method} ${req.path} not included in your ${keyData.tier} plan`,
            code: 'FEATURE_NOT_AVAILABLE',
            tier: keyData.tier,
            upgradeUrl: '/upgrade'
          });
        }
        
        // Check rate limits
        const rateLimit = await ApiKeyService.checkRateLimit(keyData.id, keyData.tier);
        
        if (!rateLimit.allowed) {
          // Set rate limit headers
          res.setHeader('X-RateLimit-Limit', TIER_LIMITS[keyData.tier].requestsPerHour);
          res.setHeader('X-RateLimit-Remaining', rateLimit.remaining);
          res.setHeader('X-RateLimit-Reset', Math.ceil(rateLimit.resetTime.getTime() / 1000));
          
          return res.status(429).json({
            error: 'Rate limit exceeded',
            message: rateLimit.error || 'Too many requests',
            code: 'RATE_LIMIT_EXCEEDED',
            resetTime: rateLimit.resetTime.toISOString(),
            tier: keyData.tier,
            limits: TIER_LIMITS[keyData.tier]
          });
        }
        
        // Set rate limit headers for successful requests
        res.setHeader('X-RateLimit-Limit', TIER_LIMITS[keyData.tier].requestsPerHour);
        res.setHeader('X-RateLimit-Remaining', rateLimit.remaining);
        res.setHeader('X-RateLimit-Reset', Math.ceil(rateLimit.resetTime.getTime() / 1000));
        
        // Set tier information headers
        res.setHeader('X-API-Tier', keyData.tier);
        res.setHeader('X-API-Key-Preview', keyData.keyPreview);
        
        // Attach API key info to request
        req.apiKey = keyData;
        req.tier = keyData.tier;
        req.rateLimitInfo = {
          remaining: rateLimit.remaining,
          resetTime: rateLimit.resetTime
        };
        
        // Override res.json to record usage
        const originalJson = res.json;
        
        res.json = function(data: any) {
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          const cached = res.getHeader('X-Cache') === 'HIT';
          
          // Record usage asynchronously
          setImmediate(async () => {
            try {
              const usage: ApiUsage = {
                keyId: keyData.id,
                timestamp: new Date(startTime),
                endpoint: `${req.method}:${req.path}`,
                method: req.method,
                responseTime,
                cached,
                bytes: Buffer.byteLength(JSON.stringify(data), 'utf8'),
                statusCode: res.statusCode,
                userAgent: req.headers['user-agent'],
                ip: req.ip || req.connection.remoteAddress,
                cost: ApiAuthMiddleware.calculateCost(req, responseTime, cached)
              };
              
              await ApiKeyService.recordUsage(usage);
            } catch (error) {
              console.error('❌ Error recording API usage:', error);
            }
          });
          
          return originalJson.call(this, data);
        };
        
        next();
        
      } catch (error) {
        console.error('❌ API authentication error:', error);
        
        res.status(500).json({
          error: 'Authentication service error',
          message: 'Please try again later',
          code: 'AUTH_SERVICE_ERROR'
        });
      }
    };
  }
  
  // Helper to check tier requirements
  private static meetsTierRequirement(userTier: ApiTier, requiredTier: ApiTier): boolean {
    const tierHierarchy = {
      [ApiTier.FREE]: 0,
      [ApiTier.PREMIUM]: 1,
      [ApiTier.ENTERPRISE]: 2
    };
    
    return tierHierarchy[userTier] >= tierHierarchy[requiredTier];
  }
  
  // Middleware specifically for premium endpoints
  static requirePremium() {
    return ApiAuthMiddleware.authenticate({
      required: true,
      premiumOnly: true
    });
  }
  
  // Middleware specifically for enterprise endpoints
  static requireEnterprise() {
    return ApiAuthMiddleware.authenticate({
      required: true,
      minTier: ApiTier.ENTERPRISE
    });
  }
  
  // Middleware for optional authentication (allows both authenticated and anonymous)
  static optional() {
    return ApiAuthMiddleware.authenticate({
      required: false,
      allowFree: true
    });
  }
  
  // Middleware for endpoints that require any valid API key
  static requireApiKey() {
    return ApiAuthMiddleware.authenticate({
      required: true,
      allowFree: true
    });
  }
  
  // Health check for API key service
  static healthCheck() {
    return async (req: Request, res: Response) => {
      try {
        // Test key generation
        const testResult = ApiKeyService.generateApiKey();
        
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          service: 'api-key-auth',
          features: {
            keyGeneration: !!testResult.key,
            cacheService: true, // Assuming cache service is healthy
            tiers: Object.keys(ApiTier),
            rateLimits: TIER_LIMITS
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          service: 'api-key-auth',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };
  }
  
  // Generate usage report endpoint
  static usageReport() {
    return async (req: Request, res: Response) => {
      try {
        if (!req.apiKey) {
          return res.status(401).json({ error: 'API key required' });
        }
        
        const period = (req.query.period as 'hour' | 'day' | 'month') || 'hour';
        const stats = await ApiKeyService.getUsageStats(req.apiKey.id, period);
        
        if (!stats) {
          return res.status(500).json({ error: 'Failed to retrieve usage statistics' });
        }
        
        res.json({
          keyPreview: req.apiKey.keyPreview,
          tier: req.apiKey.tier,
          period,
          stats,
          limits: TIER_LIMITS[req.apiKey.tier],
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('❌ Error generating usage report:', error);
        res.status(500).json({
          error: 'Failed to generate usage report',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };
  }
}

export default ApiAuthMiddleware; 