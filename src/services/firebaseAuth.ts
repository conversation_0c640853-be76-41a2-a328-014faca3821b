import {
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser,
  sendPasswordResetEmail,
  sendEmailVerification
} from 'firebase/auth';
import { auth, googleProvider } from '@/lib/firebase';
import { supabase } from '@/lib/supabase';

export interface User {
  id: string;
  email: string | null;
  username: string;
  tier: "wordsmith" | "storyteller" | "authors-guild" | "free";
  credits: number;
  createdAt: string;
  updatedAt: string;
  termsAccepted?: boolean;
}

// Sync Firebase user to Supabase database
const syncUserToSupabase = async (firebaseUser: FirebaseUser): Promise<User> => {
  try {
    // First, check if user profile already exists
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('terms_accepted, is_admin')
      .eq('id', firebaseUser.uid)
      .single();

    // Sync to user_profiles table
    const profileData = {
      id: firebaseUser.uid,
      username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
      avatar_url: firebaseUser.photoURL || null,
      terms_accepted: existingProfile?.terms_accepted ?? false, // Preserve existing value or default to false
      is_admin: existingProfile?.is_admin ?? false, // Preserve existing value or default to false
      created_at: firebaseUser.metadata.creationTime,
      updated_at: new Date().toISOString(),
    };

    const { data: profileResult, error: profileError } = await supabase
      .from('user_profiles')
      .upsert(profileData, { onConflict: 'id' })
      .select()
      .single();

    if (profileError) {
      console.error('Error syncing user profile to Supabase:', profileError);
      // Return user data even if sync fails
      return {
        id: firebaseUser.uid,
        email: firebaseUser.email,
        username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        tier: "free" as const,
        credits: 0,
        createdAt: firebaseUser.metadata.creationTime || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        termsAccepted: false,
      };
    }

    // Return user data with terms acceptance status from profile
    return {
      id: profileResult.id,
      email: firebaseUser.email,
      username: profileResult.username,
      tier: "free" as const, // Default tier
      credits: 0, // Default credits
      createdAt: profileResult.created_at,
      updatedAt: profileResult.updated_at,
      termsAccepted: profileResult.terms_accepted || false,
    };
  } catch (error) {
    console.error('Error in syncUserToSupabase:', error);
    // Return basic user data if everything fails
    return {
      id: firebaseUser.uid,
      email: firebaseUser.email,
      username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
      tier: "free" as const,
      credits: 0,
      createdAt: firebaseUser.metadata.creationTime || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      termsAccepted: false,
    };
  }
};

// Sign in with Google
export const signInWithGoogle = async (): Promise<User> => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

// Sign in with email and password
export const signInWithEmail = async (email: string, password: string): Promise<User> => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);

    // Check if email is verified
    if (!result.user.emailVerified) {
      throw new Error('Please verify your email address before signing in. Check your inbox for a verification email.');
    }

    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing in with email:', error);
    throw error;
  }
};

// Sign up with email and password
export const signUpWithEmail = async (email: string, password: string, username?: string): Promise<User> => {
  try {
    const result = await createUserWithEmailAndPassword(auth, email, password);

    // Send email verification
    if (result.user) {
      await sendEmailVerification(result.user);
      console.log('Email verification sent to:', email);
    }

    // Update display name if username provided
    if (username && result.user) {
      await (result.user as any).updateProfile({ displayName: username });
    }

    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing up with email:', error);
    throw error;
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

// Auth state listener
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        const user = await syncUserToSupabase(firebaseUser);
        callback(user);
      } catch (error) {
        console.error('Error syncing user:', error);
        callback(null);
      }
    } else {
      callback(null);
    }
  });
};