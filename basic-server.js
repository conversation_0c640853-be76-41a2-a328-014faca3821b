// Basic HTTP server for development
const http = require("http");
const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Configuration
const PORT = 8080;
const PUBLIC_DIR = path.join(__dirname, "public");

// Create public directory if it doesn't exist
if (!fs.existsSync(PUBLIC_DIR)) {
  fs.mkdirSync(PUBLIC_DIR, { recursive: true });
}

// Create a basic index.html if it doesn't exist
const indexPath = path.join(PUBLIC_DIR, "index.html");
if (!fs.existsSync(indexPath)) {
  // Copy from dist if available
  const distIndexPath = path.join(__dirname, "dist", "index.html");
  if (fs.existsSync(distIndexPath)) {
    console.log("Copying index.html from dist to public...");
    fs.copyFileSync(distIndexPath, indexPath);
  } else {
    // Create a basic HTML file
    const basicHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Word by Word Story</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
    }
    h1, h2 { color: #333; }
    .card {
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1.5rem;
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <h1>Word by Word Story</h1>
  <div class="card">
    <h2>Development Server</h2>
    <p>This is a simple HTTP server for development.</p>
    <p>To build the application, run: <code>npm run build</code></p>
  </div>
</body>
</html>
    `;
    fs.writeFileSync(indexPath, basicHTML);
  }
}

// MIME types
const MIME_TYPES = {
  ".html": "text/html",
  ".css": "text/css",
  ".js": "text/javascript",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".jpeg": "image/jpeg",
  ".gif": "image/gif",
  ".svg": "image/svg+xml",
  ".ico": "image/x-icon",
  ".txt": "text/plain",
};

// API endpoint handlers
const apiHandlers = {
  "/api/ai-proxy": async (req, res) => {
    console.log("Handling AI proxy request");

    // Get request body
    let body = "";
    req.on("data", (chunk) => {
      body += chunk.toString();
    });

    req.on("end", async () => {
      try {
        // Use curl to forward the request to DeepInfra
        const authHeader = req.headers.authorization || "";
        const command = `curl -X POST -H "Content-Type: application/json" -H "Authorization: ${authHeader}" -d '${body}' https://api.deepinfra.com/v1/inference`;

        console.log("Forwarding request to DeepInfra API...");
        const response = execSync(command, { encoding: "utf8" });

        // Return the response
        res.writeHead(200, { "Content-Type": "application/json" });
        res.end(response);
      } catch (error) {
        console.error("Error forwarding request:", error.message);
        res.writeHead(500, { "Content-Type": "application/json" });
        res.end(JSON.stringify({ error: "Failed to forward request" }));
      }
    });
  },
};

// Create HTTP server
const server = http.createServer((req, res) => {
  console.log(`${req.method} ${req.url}`);

  // CORS headers
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS",
  );
  res.setHeader(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization",
  );

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    res.writeHead(204);
    res.end();
    return;
  }

  // Handle API requests
  if (req.url.startsWith("/api/")) {
    const handler = apiHandlers[req.url];
    if (handler) {
      handler(req, res);
      return;
    }

    // API not found
    res.writeHead(404, { "Content-Type": "application/json" });
    res.end(JSON.stringify({ error: "API endpoint not found" }));
    return;
  }

  // Serve static files
  let filePath = path.join(
    PUBLIC_DIR,
    req.url === "/" ? "index.html" : req.url,
  );

  // Check if the file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File not found, serve index.html for SPA routing
      filePath = indexPath;
    }

    // Get file extension and MIME type
    const ext = path.extname(filePath);
    const contentType = MIME_TYPES[ext] || "application/octet-stream";

    // Read and serve the file
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(500);
        res.end("Server Error");
        return;
      }

      res.writeHead(200, { "Content-Type": contentType });
      res.end(data);
    });
  });
});

// Start the server
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`Press Ctrl+C to stop the server`);
});
