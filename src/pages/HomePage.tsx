import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ContributionMode } from '@/services/realTimeService';
import { useDocumentTitle, createPageTitle } from '@/hooks/useDocumentTitle';
import { useAuth } from '@/contexts/auth';

interface Story {
  id: string;
  title: string;
  description: string;
  contributionMode: ContributionMode;
  wordCount: number;
  participantCount: number;
  createdAt: string;
  updatedAt: string;
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Set page title (home page shows just the app name)
  useDocumentTitle(createPageTitle("Collaborative Storytelling", false));

  // Fetch all stories
  useEffect(() => {
    const fetchStories = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/v1/stories');
        
        if (!response.ok) {
          throw new Error('Failed to fetch stories');
        }
        
        const data = await response.json();
        setStories(data);
      } catch (err) {
        setError((err as Error).message);
      } finally {
        setLoading(false);
      }
    };

    fetchStories();
  }, []);

  // Join a story
  const handleJoinStory = async (storyId: string) => {
    if (!isAuthenticated || !user) {
      setError('Please log in to join a story');
      navigate('/login');
      return;
    }

    try {
      const response = await fetch(`/api/v1/stories/${storyId}/join`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          username: user.username,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to join story');
      }
      
      // Navigate to the story page
      navigate(`/stories/${storyId}`);
    } catch (err) {
      setError((err as Error).message);
    }
  };

  // Get contribution mode display name
  const getModeName = (mode: ContributionMode): string => {
    switch (mode) {
      case ContributionMode.WORD:
        return 'Word-by-Word';
      case ContributionMode.MULTI_WORD:
        return 'Multi-Word';
      case ContributionMode.SENTENCE:
        return 'Sentence';
      case ContributionMode.PARAGRAPH:
        return 'Paragraph';
      default:
        return mode;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Word-by-Word Stories</h1>
        <Link
          to="/create"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Create New Story
        </Link>
      </div>
      
      {error && (
        <div className="p-3 text-sm text-red-700 bg-red-100 rounded-md mb-6">
          {error}
        </div>
      )}
      
      {stories.length === 0 ? (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-lg text-gray-600 mb-4">No stories available yet.</p>
          <Link
            to="/create"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Create the first story
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stories.map((story) => (
            <div key={story.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-2">{story.title}</h2>
                {story.description && (
                  <p className="text-gray-600 mb-4 line-clamp-2">{story.description}</p>
                )}
                <div className="flex flex-wrap gap-2 mb-4">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {getModeName(story.contributionMode)}
                  </span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    {story.wordCount} words
                  </span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    {story.participantCount} participants
                  </span>
                </div>
                <p className="text-sm text-gray-500 mb-4">
                  Created {new Date(story.createdAt).toLocaleDateString()}
                </p>
                <div className="flex justify-between">
                  <Link
                    to={`/stories/${story.id}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View Story
                  </Link>
                  <button
                    onClick={() => handleJoinStory(story.id)}
                    className="text-green-600 hover:text-green-800"
                  >
                    Join Story
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default HomePage;
