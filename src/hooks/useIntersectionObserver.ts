import { useEffect, useRef, useState } from 'react';

interface IntersectionObserverOptions extends IntersectionObserverInit {
  freezeOnceVisible?: boolean; // Option to stop observing once the element becomes visible
}

function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  { threshold = 0.1, root = null, rootMargin = '0%', freezeOnceVisible = false }: IntersectionObserverOptions = {},
  callback: () => void,
  dependencies: any[] = [] // Dependencies for the useEffect hook that sets up the observer
): void {
  const observer = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (observer.current) {
      observer.current.disconnect();
    }

    if (!elementRef.current) {
        return;
    }

    observer.current = new IntersectionObserver(
      (entries) => {
        const firstEntry = entries[0];
        if (firstEntry.isIntersecting) {
          callback();
          if (freezeOnceVisible && elementRef.current && observer.current) {
            observer.current.unobserve(elementRef.current);
          }
        }
      },
      { threshold, root, rootMargin }
    );

    const { current: currentElement } = elementRef;
    if (currentElement) {
      observer.current.observe(currentElement);
    }

    return () => {
      if (observer.current && currentElement) {
        observer.current.unobserve(currentElement);
      }
      // Optional: fully disconnect if preferred when dependencies change significantly
      // or on component unmount, though unobserve usually suffices for dynamic lists.
      // if (observer.current) {
      //   observer.current.disconnect();
      // }
    };
   
  }, [elementRef, threshold, root, rootMargin, freezeOnceVisible, callback, ...dependencies]);
}

export default useIntersectionObserver; 