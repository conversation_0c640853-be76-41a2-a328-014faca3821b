[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [navigation-menu](../README.md) / NavigationMenuTrigger

# Variable: NavigationMenuTrigger

> `const` **NavigationMenuTrigger**: `ForwardRefExoticComponent`\<`Omit`\<`NavigationMenuTriggerProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/navigation-menu.tsx:47](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/navigation-menu.tsx#L47)
