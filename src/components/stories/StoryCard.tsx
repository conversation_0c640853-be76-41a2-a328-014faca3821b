import React, { useEffect, useState, useCallback } from 'react';
import { Story } from '@/types/story'; // Assuming Author is also in story or not needed directly here
import { Link } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button'; // Import Button
import { ThumbsUp, ThumbsDown, MessageCircle, Loader2 } from 'lucide-react';
import clsx from 'clsx'; // For conditional classes
import { trackStoryEvent } from '@/lib/posthog';

interface StoryCardProps {
  story: Story;
}

interface VoteCounts {
  upvote_count: number;
  downvote_count: number;
}

const StoryCard: React.FC<StoryCardProps> = ({ story }) => {
  const [voteCounts, setVoteCounts] = useState<VoteCounts>({ 
    upvote_count: story.upvote_count || 0, 
    downvote_count: story.downvote_count || 0 
  });
  const [currentUserVote, setCurrentUserVote] = useState<'upvote' | 'downvote' | null>(null);
  const [loadingInitialVotes, setLoadingInitialVotes] = useState<boolean>(true);
  const [isVoteProcessing, setIsVoteProcessing] = useState<boolean>(false); // For disabling buttons during vote RPC
  const [errorVotes, setErrorVotes] = useState<string | null>(null);

  const fetchInitialVoteData = useCallback(async () => {
    if (!story.id) return;
    setLoadingInitialVotes(true);
    setErrorVotes(null);
    try {
      // Fetch total vote counts
      const { data: countsData, error: countsError } = await supabase.rpc('get_story_vote_counts', { p_story_id: story.id });
      if (countsError) throw countsError;
      if (countsData && countsData.length > 0) {
        setVoteCounts(countsData[0]);
      } else {
        // This case should ideally not happen if stories table has default 0 counts
        // Or if get_story_vote_counts always returns a row
        setVoteCounts({ upvote_count: 0, downvote_count: 0 });
      }

      // Fetch current user's vote
      const { data: userVoteData, error: userVoteError } = await supabase.rpc('get_my_vote_for_story', { p_story_id: story.id });
      if (userVoteError) throw userVoteError;
      setCurrentUserVote(userVoteData as 'upvote' | 'downvote' | null);

    } catch (err: any) {
      console.error("Error fetching initial vote data:", err);
      setErrorVotes(err.message || 'Failed to load vote data');
      // Fallback counts from story prop if available, otherwise 0
      setVoteCounts({ 
        upvote_count: story.upvote_count || 0, 
        downvote_count: story.downvote_count || 0 
      });
    } finally {
      setLoadingInitialVotes(false);
    }
  }, [story.id, story.upvote_count, story.downvote_count]);

  useEffect(() => {
    fetchInitialVoteData();
  }, [fetchInitialVoteData]);

  const handleVote = async (voteType: 'upvote' | 'downvote') => {
    if (isVoteProcessing) return; // Prevent multiple submissions
    setIsVoteProcessing(true);
    setErrorVotes(null);

    const previousVoteCounts = { ...voteCounts };
    const previousUserVote = currentUserVote;

    // Optimistic update
    let newUpvoteCount = voteCounts.upvote_count;
    let newDownvoteCount = voteCounts.downvote_count;
    let newUserVote: 'upvote' | 'downvote' | null = null;

    if (currentUserVote === voteType) { // Clicking the same vote again (toggling off)
      newUserVote = null;
      if (voteType === 'upvote') newUpvoteCount--;
      else newDownvoteCount--;
    } else { // New vote or changing vote
      newUserVote = voteType;
      if (voteType === 'upvote') {
        newUpvoteCount++;
        if (currentUserVote === 'downvote') newDownvoteCount--; // Was downvote, now upvote
      } else { // voteType is 'downvote'
        newDownvoteCount++;
        if (currentUserVote === 'upvote') newUpvoteCount--; // Was upvote, now downvote
      }
    }
    
    setVoteCounts({ upvote_count: Math.max(0, newUpvoteCount), downvote_count: Math.max(0, newDownvoteCount) });
    setCurrentUserVote(newUserVote);

    try {
      const { data: newCounts, error: rpcError } = await supabase.rpc('cast_vote_and_update_counts', {
        p_story_id: story.id,
        p_vote_type: voteType,
      });

      if (rpcError) throw rpcError;

      if (newCounts && newCounts.length > 0) {
        // Set counts from server response for consistency
        setVoteCounts({
          upvote_count: newCounts[0].final_upvote_count,
          downvote_count: newCounts[0].final_downvote_count
        });
        
        // Track successful vote
        trackStoryEvent.storyVoted(story.id, voteType, {
          story_title: story.title,
          previous_vote: previousUserVote,
          vote_toggle: previousUserVote === voteType,
          final_upvotes: newCounts[0].final_upvote_count,
          final_downvotes: newCounts[0].final_downvote_count,
          vote_timestamp: new Date().toISOString(),
        });
        
        // currentUserVote is already optimistically set, and RPC handles the toggle logic server-side
        // If the RPC indicated a vote removal, we might need to explicitly set currentUserVote to null
        // For now, we assume the optimistic update of currentUserVote is correct.
        // A more robust way: RPC could return the user's current vote status post-operation.
      } else {
        // This shouldn't happen if RPC is correct
        throw new Error("Failed to get updated counts from server.");
      }
    } catch (err: any) {
      console.error("Error casting vote:", err);
      setErrorVotes(err.message || 'Failed to cast vote');
      // Rollback optimistic update
      setVoteCounts(previousVoteCounts);
      setCurrentUserVote(previousUserVote);
    } finally {
      setIsVoteProcessing(false);
    }
  };

  const authorDisplayName = story.author?.username || 'Anonymous';
  const descriptionExcerpt = story.description ? 
    (story.description.length > 100 ? story.description.substring(0, 97) + '...' : story.description) 
    : 'No description available.';

  return (
    <Card className="flex flex-col h-full hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <CardTitle className="text-xl font-semibold hover:text-blue-600">
          <Link to={`/story/${story.id}`}>{story.title}</Link>
        </CardTitle>
        <CardDescription className="text-sm text-gray-500">
          By {authorDisplayName} - {new Date(story.created_at).toLocaleDateString()}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        {story.cover_image_url && (
          <img 
            src={story.cover_image_url} 
            alt={`${story.title} cover`} 
            className="w-full h-40 object-cover rounded-md mb-3"
          />
        )}
        <p className="text-gray-700 text-sm mb-3">
          {descriptionExcerpt}
        </p>
        <div className="flex items-center space-x-2 text-xs text-gray-500 mb-2">
          <MessageCircle size={14} /> 
          <span>{story.contributions?.length || 0} contributions</span> 
        </div>
        {story.genre && <Badge variant="outline" className="text-xs">{story.genre}</Badge>}
      </CardContent>
      <CardFooter className="border-t pt-3 flex justify-between items-center text-sm">
        <div className="flex items-center space-x-2"> {/* Reduced space-x-4 to space-x-2 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVote('upvote')}
            disabled={isVoteProcessing || loadingInitialVotes}
            className={clsx("p-1 h-auto", currentUserVote === 'upvote' ? 'text-green-500' : 'text-gray-500 hover:text-green-500')}
            aria-label="Upvote"
          >
            {isVoteProcessing && currentUserVote === 'upvote' ? <Loader2 className="h-4 w-4 animate-spin" /> : <ThumbsUp size={16} />}
            <span className="ml-1 font-medium">{loadingInitialVotes ? '...' : voteCounts.upvote_count}</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleVote('downvote')}
            disabled={isVoteProcessing || loadingInitialVotes}
            className={clsx("p-1 h-auto", currentUserVote === 'downvote' ? 'text-red-500' : 'text-gray-500 hover:text-red-500')}
            aria-label="Downvote"
          >
            {isVoteProcessing && currentUserVote === 'downvote' ? <Loader2 className="h-4 w-4 animate-spin" /> : <ThumbsDown size={16} />}
            <span className="ml-1 font-medium">{loadingInitialVotes ? '...' : voteCounts.downvote_count}</span>
          </Button>
        </div>
        {errorVotes && <p className="text-xs text-red-500 ml-2">Error: {errorVotes.length > 20 ? errorVotes.substring(0,17)+'...' : errorVotes}</p>}
        <div> {/* Ensure status badges are grouped or have a container if errorVotes pushes them */}
          {story.status === 'completed' && <Badge variant="secondary" className="bg-green-100 text-green-700">Completed</Badge>}
          {story.status === 'in_progress' && <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">In Progress</Badge>}
          {story.status === 'abandoned' && <Badge variant="destructive">Abandoned</Badge>}
        </div>
      </CardFooter>
    </Card>
  );
};

export default StoryCard; 