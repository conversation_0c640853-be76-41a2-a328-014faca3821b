// Script to deploy to production environment
import { execSync } from "child_process";

console.log("Deploying to production environment...");

try {
  // Generate Prisma client
  console.log("Generating Prisma client...");
  execSync("npx prisma generate", { stdio: "inherit" });

  // Deploy to Vercel production
  console.log("Deploying to Vercel production...");
  execSync("vercel --prod", { stdio: "inherit" });

  console.log("Deployment to production completed successfully!");
  console.log(
    "Note: Database migrations will be run automatically during the build process.",
  );
} catch (error) {
  console.error("Error deploying to production:", error);
  process.exit(1);
}
