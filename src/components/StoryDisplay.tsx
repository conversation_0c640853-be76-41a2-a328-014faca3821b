import React, { useMemo, useEffect } from 'react';
import { Contribution, Author, Story } from '@/types/story';
import { useStory } from '@/contexts/StoryContext';
import { trackStoryEvent } from '@/lib/posthog';
import { usePostHogTimeTracking } from '@/hooks/usePostHog';

interface StoryDisplayProps {
  story: Story;
  currentUserId?: string | null;
}

const StoryDisplay: React.FC<StoryDisplayProps> = ({
  story,
  currentUserId,
}) => {
  const sortedContributions = useMemo(() => {
    return [...story.contributions].sort((a, b) => a.order - b.order);
  }, [story.contributions]);

  const { typingUsers } = useStory();
  
  // Track time spent viewing this story
  usePostHogTimeTracking(`story_view_${story.id}`);
  
  // Track story view event on component mount
  useEffect(() => {
    if (story?.id) {
      trackStoryEvent.storyViewed(story.id, {
        story_title: story.title,
        story_status: story.status,
        total_contributions: story.contributions?.length || 0,
        word_count: story.current_word_count || 0,
        view_timestamp: new Date().toISOString(),
        viewer_is_participant: <PERSON><PERSON><PERSON>(currentUserId && story.contributions?.some(c => c.author.id === currentUserId)),
      });
    }
  }, [story.id, story.title, story.status, story.contributions?.length, story.current_word_count, currentUserId]);

  const getTypingMessage = () => {
    if (!typingUsers || typingUsers.length === 0) {
      return null;
    }
    const names = typingUsers.map(u => u.username || 'Someone');
    if (names.length === 1) {
      return `${names[0]} is typing...`;
    }
    if (names.length === 2) {
      return `${names[0]} and ${names[1]} are typing...`;
    }
    return `${names.slice(0, 2).join(', ')}, and ${names.length - 2} other(s) are typing...`;
  };

  if (!story) {
    return <p className="italic text-gray-500">Story not found.</p>;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4">{story.title}</h2>
      <div className="prose max-w-none border-t pt-4 space-y-3">
        {sortedContributions.length === 0 ? (
          <p className="italic text-gray-500">The story is just beginning...</p>
        ) : (
          sortedContributions.map((contribution, index) => (
            <div key={contribution.id} className={`contribution-item mb-2 p-2 rounded ${contribution.author.id === currentUserId ? 'bg-blue-50' : 'bg-gray-50'}`}>
              <p className="text-sm text-gray-600">
                <span className="font-semibold">{contribution.author.username || 'Anonymous'}</span>
                <span className="text-xs text-gray-400 ml-2">({new Date(contribution.created_at).toLocaleTimeString()})</span>
              </p>
              <p className="whitespace-pre-wrap">{contribution.content}</p>
            </div>
          ))
        )}
      </div>
      <div className="mt-4 text-sm text-gray-500">
        {story.contributions.length} contribution(s)
        {story.status === 'completed' && <span className="ml-2 font-semibold text-green-600"> (Completed)</span>}
        {story.status === 'abandoned' && <span className="ml-2 font-semibold text-red-600"> (Abandoned)</span>}
      </div>
      {typingUsers.length > 0 && (
        <div className="mt-2 text-sm italic text-gray-500 h-5">
          {getTypingMessage()}
        </div>
      )}
    </div>
  );
};

export default StoryDisplay;
