/**
 * Strike Management Service
 * Handles the 3-strike moderation system for stories
 */

import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface ContentReport {
  id: string;
  content_type: 'story' | 'contribution' | 'comment';
  content_id: string;
  reported_by: string;
  report_reason: string;
  report_details?: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  reviewed_by?: string;
  review_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface StoryStrike {
  id: string;
  story_id: string;
  strike_reason: string;
  strike_details?: string;
  report_id?: string;
  issued_by?: string;
  auto_generated: boolean;
  created_at: string;
}

export interface StoryModerationStatus {
  story_id: string;
  strike_count: number;
  visibility: 'public' | 'private' | 'hidden';
  strikes: StoryStrike[];
  can_receive_more_strikes: boolean;
}

/**
 * Submit a content report for moderation review
 */
export const submitContentReport = async (
  contentType: 'story' | 'contribution' | 'comment',
  contentId: string,
  reason: string,
  details?: string
): Promise<{ success: boolean; reportId?: string; error?: string }> => {
  try {
    const { data: report, error } = await supabase
      .from('content_reports')
      .insert({
        content_type: contentType,
        content_id: contentId,
        reported_by: (await supabase.auth.getUser()).data.user?.id,
        report_reason: reason,
        report_details: details
      })
      .select()
      .single();

    if (error) {
      console.error('Error submitting content report:', error);
      return { success: false, error: error.message };
    }

    // Auto-process report if it meets certain criteria
    await autoProcessReport(report.id, reason);

    toast({
      title: 'Content reported',
      description: 'Thank you for helping keep our community safe. We\'ll review this content.',
    });

    return { success: true, reportId: report.id };
  } catch (error) {
    console.error('Error in submitContentReport:', error);
    return { success: false, error: 'Failed to submit report' };
  }
};

/**
 * Auto-process reports that meet immediate action criteria
 */
const autoProcessReport = async (reportId: string, reason: string): Promise<void> => {
  // Define auto-strike triggers for immediate action
  const autoStrikeTriggers = [
    'hate speech',
    'harassment',
    'explicit content',
    'spam',
    'violence'
  ];

  const normalizedReason = reason.toLowerCase();
  const shouldAutoStrike = autoStrikeTriggers.some(trigger => 
    normalizedReason.includes(trigger)
  );

  if (shouldAutoStrike) {
    await processReportWithStrike(reportId, true, 'Automatic strike for policy violation');
  }
};

/**
 * Process a content report and optionally issue a strike
 */
export const processReportWithStrike = async (
  reportId: string,
  issueStrike: boolean,
  reviewNotes?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get the report details
    const { data: report, error: reportError } = await supabase
      .from('content_reports')
      .select('*')
      .eq('id', reportId)
      .single();

    if (reportError || !report) {
      return { success: false, error: 'Report not found' };
    }

    // Update report status
    const { error: updateError } = await supabase
      .from('content_reports')
      .update({
        status: issueStrike ? 'resolved' : 'dismissed',
        reviewed_by: (await supabase.auth.getUser()).data.user?.id,
        review_notes: reviewNotes
      })
      .eq('id', reportId);

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Issue strike if requested
    if (issueStrike) {
      const storyId = report.content_type === 'story' 
        ? report.content_id 
        : await getStoryIdFromContent(report.content_type, report.content_id);

      if (storyId) {
        await issueStrikeToStory(
          storyId,
          report.report_reason,
          report.report_details || '',
          reportId,
          true // auto_generated
        );
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error processing report:', error);
    return { success: false, error: 'Failed to process report' };
  }
};

/**
 * Issue a strike to a story (triggers visibility check)
 */
export const issueStrikeToStory = async (
  storyId: string,
  reason: string,
  details?: string,
  reportId?: string,
  autoGenerated: boolean = false
): Promise<{ success: boolean; newStrikeCount?: number; error?: string }> => {
  try {
    // Insert the strike (this will trigger the database function to update story visibility)
    const { data: strike, error: strikeError } = await supabase
      .from('story_strikes')
      .insert({
        story_id: storyId,
        strike_reason: reason,
        strike_details: details,
        report_id: reportId,
        issued_by: autoGenerated ? null : (await supabase.auth.getUser()).data.user?.id,
        auto_generated: autoGenerated
      })
      .select()
      .single();

    if (strikeError) {
      console.error('Error issuing strike:', strikeError);
      return { success: false, error: strikeError.message };
    }

    // Get updated story status
    const { data: story, error: storyError } = await supabase
      .from('stories')
      .select('strike_count, visibility, title')
      .eq('id', storyId)
      .single();

    if (storyError || !story) {
      return { success: false, error: 'Failed to get updated story status' };
    }

    // Notify story participants if visibility changed
    if (story.visibility === 'private' && story.strike_count >= 3) {
      await notifyStoryParticipants(
        storyId,
        `Story "${story.title}" has been made private due to community reports`
      );
    }

    toast({
      title: 'Strike issued',
      description: `Strike issued for: ${reason}. Story now has ${story.strike_count} strike(s).`,
      variant: story.strike_count >= 3 ? 'destructive' : 'default'
    });

    return { 
      success: true, 
      newStrikeCount: story.strike_count 
    };
  } catch (error) {
    console.error('Error in issueStrikeToStory:', error);
    return { success: false, error: 'Failed to issue strike' };
  }
};

/**
 * Get story moderation status including all strikes
 */
export const getStoryModerationStatus = async (
  storyId: string
): Promise<StoryModerationStatus | null> => {
  try {
    // Get story info
    const { data: story, error: storyError } = await supabase
      .from('stories')
      .select('strike_count, visibility')
      .eq('id', storyId)
      .single();

    if (storyError || !story) {
      return null;
    }

    // Get all strikes for the story
    const { data: strikes, error: strikesError } = await supabase
      .from('story_strikes')
      .select('*')
      .eq('story_id', storyId)
      .order('created_at', { ascending: false });

    if (strikesError) {
      console.error('Error fetching strikes:', strikesError);
      return null;
    }

    return {
      story_id: storyId,
      strike_count: story.strike_count,
      visibility: story.visibility,
      strikes: strikes || [],
      can_receive_more_strikes: story.strike_count < 3
    };
  } catch (error) {
    console.error('Error getting story moderation status:', error);
    return null;
  }
};

/**
 * Get all pending content reports (admin function)
 */
export const getPendingReports = async (): Promise<ContentReport[]> => {
  try {
    const { data: reports, error } = await supabase
      .from('content_reports')
      .select(`
        *,
        reporter:reported_by (
          username
        )
      `)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching pending reports:', error);
      return [];
    }

    return reports || [];
  } catch (error) {
    console.error('Error in getPendingReports:', error);
    return [];
  }
};

/**
 * Remove a strike from a story (admin function)
 */
export const removeStrike = async (
  strikeId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get strike info first
    const { data: strike, error: strikeError } = await supabase
      .from('story_strikes')
      .select('story_id')
      .eq('id', strikeId)
      .single();

    if (strikeError || !strike) {
      return { success: false, error: 'Strike not found' };
    }

    // Delete the strike
    const { error: deleteError } = await supabase
      .from('story_strikes')
      .delete()
      .eq('id', strikeId);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    // Manually update story strike count and potentially restore visibility
    const { data: updatedStory, error: updateError } = await supabase.rpc(
      'recalculate_story_strikes',
      { target_story_id: strike.story_id }
    );

    if (updateError) {
      console.error('Error recalculating strikes:', updateError);
    }

    toast({
      title: 'Strike removed',
      description: reason || 'Strike has been removed from the story.',
    });

    return { success: true };
  } catch (error) {
    console.error('Error removing strike:', error);
    return { success: false, error: 'Failed to remove strike' };
  }
};

/**
 * Helper function to get story ID from contribution or comment
 */
const getStoryIdFromContent = async (
  contentType: 'contribution' | 'comment',
  contentId: string
): Promise<string | null> => {
  try {
    if (contentType === 'contribution') {
      const { data, error } = await supabase
        .from('contributions')
        .select('story_id')
        .eq('id', contentId)
        .single();

      return error ? null : data?.story_id;
    }

    // For comments, you'd need to implement based on your comment structure
    // This is a placeholder
    return null;
  } catch (error) {
    console.error('Error getting story ID from content:', error);
    return null;
  }
};

/**
 * Notify story participants about moderation actions
 */
const notifyStoryParticipants = async (
  storyId: string,
  message: string
): Promise<void> => {
  try {
    // Get all active participants
    const { data: participants, error } = await supabase
      .from('story_participants')
      .select('user_id')
      .eq('story_id', storyId)
      .eq('status', 'active');

    if (error || !participants) {
      return;
    }

    // Create notifications for each participant
    const notifications = participants.map(p => ({
      user_id: p.user_id,
      type: 'moderation',
      title: 'Story Moderation Update',
      message: message,
      story_id: storyId
    }));

    const { error: notificationError } = await supabase
      .from('notifications')
      .insert(notifications);

    if (notificationError) {
      console.error('Error creating notifications:', notificationError);
    }
  } catch (error) {
    console.error('Error notifying participants:', error);
  }
};

/**
 * Get user's report history
 */
export const getUserReportHistory = async (
  userId?: string
): Promise<ContentReport[]> => {
  try {
    const targetUserId = userId || (await supabase.auth.getUser()).data.user?.id;
    
    if (!targetUserId) {
      return [];
    }

    const { data: reports, error } = await supabase
      .from('content_reports')
      .select('*')
      .eq('reported_by', targetUserId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user report history:', error);
      return [];
    }

    return reports || [];
  } catch (error) {
    console.error('Error in getUserReportHistory:', error);
    return [];
  }
};