import React, { useState, useEffect, useRef } from 'react';
import ContributionForm from '@/components/ContributionForm';
import StoryDisplay from '@/components/StoryDisplay';
import ParticipantsList from '@/components/ParticipantsList';
import { ContributionMode } from '@/services/realTimeService';
import { supabase } from '@/lib/supabase';
import type { Story } from '@/types/story';
import { 
  generateMagicTitle, 
  generateStoryDescription, 
  generateMagicSentence, 
  generateGotchaWord,
  generateSentenceWithGotchaWord,
  GotchaWordState,
  initGotchaWordState,
  useSkipCard 
} from '@/services/aiService';
import { useAuth } from '@/contexts/auth/hooks';
import { RotatingAdvertisement } from "@/components/ads/RotatingAdvertisement";

// Demo users for demonstration purposes (includes real user + demo users)
const getDemoUsers = (authenticatedUser: any) => {
  const baseUsers = [
    {
      id: 'demo-user-2',
      username: 'TestUser',
      color: '#EF4444' // red
    },
    {
      id: 'demo-user-3',
      username: 'GuestUser',
      color: '#10B981' // green
    }
  ];

  // If user is authenticated, add them as the first user
  if (authenticatedUser) {
    return [
      {
        id: authenticatedUser.id,
        username: authenticatedUser.username || authenticatedUser.email || 'You',
        color: '#3B82F6' // blue
      },
      ...baseUsers
    ];
  }

  // If not authenticated, use a default demo user
  return [
    {
      id: 'demo-user-1',
      username: 'DemoUser',
      color: '#3B82F6' // blue
    },
    ...baseUsers
  ];
};

// Demo story data (uses real user as creator if authenticated)
const getDemoStory = (authenticatedUser: any) => ({
  id: 'demo-story-1',
  title: 'Untitled Story',
  description: '',
  creatorId: authenticatedUser?.id || 'demo-user-1',
  contributionMode: ContributionMode.WORD,
  wordsPerContribution: 3,
  currentTurn: authenticatedUser?.id || 'demo-user-1',
});

const ContributionModesDemo: React.FC = () => {
  const { user: authenticatedUser } = useAuth(); // Get authenticated user for presence
  
  // Initialize demo data based on authenticated user
  const demoUsers = getDemoUsers(authenticatedUser);
  const demoStory = getDemoStory(authenticatedUser);
  
  const [mode, setMode] = useState<ContributionMode>(ContributionMode.WORD);
  const [wordsPerContribution, setWordsPerContribution] = useState(3);
  const [contributions, setContributions] = useState<any[]>([]);
  const [currentUserIndex, setCurrentUserIndex] = useState(0);
  const [currentTurnIndex, setCurrentTurnIndex] = useState(0);
  const [participants, setParticipants] = useState<any[]>(
    demoUsers.map(user => ({
      userId: user.id,
      username: user.username,
      isOnline: true,
      isTyping: false,
      lastActive: Date.now(),
    }))
  );
  const [storyTitle, setStoryTitle] = useState(demoStory.title);
  const [storyDescription, setStoryDescription] = useState<string>("");
  const [gotchaWordState, setGotchaWordState] = useState<GotchaWordState | null>(null);
  const [titleTopic, setTitleTopic] = useState<string>("");
  const [descriptionTopic, setDescriptionTopic] = useState<string>("");
  const [sentenceTopic, setSentenceTopic] = useState<string>("");
  const [gotchaWordDifficulty, setGotchaWordDifficulty] = useState<number>(2);
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isGeneratingSentence, setIsGeneratingSentence] = useState(false);
  const [isGeneratingGotcha, setIsGeneratingGotcha] = useState(false);
  const [isGeneratingGotchaSentence, setIsGeneratingGotchaSentence] = useState(false);
  const [points, setPoints] = useState<number>(10); // Points/credits for various AI features
  
  // Register user sessions when component mounts (REPLACED by Supabase Presence below)
  /* useEffect(() => {
    MOCK_USERS.forEach(user => {
      realTime.registerUserSession(MOCK_STORY.id, user.id, user.username);
    });
    return () => {
      MOCK_USERS.forEach(user => {
        realTime.unregisterUserSession(MOCK_STORY.id, user.id);
      });
    };
  }, []); */
  
  // Real-time subscription for new contributions (KEEP THIS AS IS - uses Supabase)
  useEffect(() => {
    if (!demoStory.id) return;
    const channelName = `contributions-story-${demoStory.id}`;
    const channel = supabase.channel(channelName);

    const subscriptionPayloadHandler = (payload: any) => {
      console.log('New contribution received:', payload.new);
      setContributions((prevContributions) => {
        if (prevContributions.find(c => c.id === payload.new.id)) {
          return prevContributions;
        }
        const authorOfNew = demoUsers.find(u => u.id === payload.new.user_id);
        const contributionWithColor = {
          ...payload.new,
          username: payload.new.username || authorOfNew?.username || 'Unknown User',
          color: authorOfNew?.color || '#888888'
        };
        return [...prevContributions, contributionWithColor];
      });
    };

    channel
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'contributions',
          filter: `story_id=eq.${demoStory.id}`
        },
        subscriptionPayloadHandler
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to contributions for story:', demoStory.id);
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
          console.error('Subscription error or closed on channel', channelName, ':', status, err);
          // Optionally, try to resubscribe or notify the user
        }
      });

    // Cleanup subscription on component unmount
    return () => {
      if (channel) {
        supabase.removeChannel(channel).then(removeStatus => {
          if (removeStatus === 'ok') { 
            console.log('Successfully unsubscribed from channel:', channelName);
          } else {
            // Log any other status as a warning/error. removeStatus might be an error string or an object.
            console.warn('Problem removing channel:', channelName, '; Status:', removeStatus);
          }
        }).catch(error => {
            console.error('Exception during removeChannel cleanup:', channelName, error);
        });
      }
    };
  }, [demoStory.id]);
  
  // Supabase Presence for active users in the story
  const presenceChannelRef = useRef<any>(null); // Using any for Supabase Channel type for simplicity

  useEffect(() => {
    if (!demoStory.id || !authenticatedUser) return;

    const presenceChannelName = `story-presence-${demoStory.id}`;
    const channel = supabase.channel(presenceChannelName, {
      config: {
        presence: {
          key: authenticatedUser.id, 
        },
      },
    });
    presenceChannelRef.current = channel; // Assign to ref after creation

    channel
      .on('presence', { event: 'sync' }, () => {
        const presenceState = channel.presenceState(); 
        console.log('Presence sync:', presenceState);
        const updatedParticipants = Object.values(presenceState).map((pStateArray: any) => {
          const pState = pStateArray[0]; 
          return {
            userId: pState.user_id,
            username: pState.username,
            isOnline: true, 
            isTyping: pState.isTyping || false, // Include isTyping from presence state
            lastActive: pState.last_active
          };
        });
        setParticipants(updatedParticipants);
      })
      .on('presence', { event: 'join' }, ({ newPresences }) => {
        console.log('Presence join:', newPresences);
        const newMappedPresences = newPresences.map(pState => ({
            userId: pState.user_id,
            username: pState.username,
            isOnline: true,
            isTyping: pState.isTyping || false, // Include isTyping
            lastActive: pState.last_active
        }));
        setParticipants(prevParticipants => {
          const existingIds = new Set(prevParticipants.map(p => p.userId));
          const uniqueNewPresences = newMappedPresences.filter(p => !existingIds.has(p.userId));
          return [...prevParticipants, ...uniqueNewPresences];
        });
      })
      .on('presence', { event: 'leave' }, ({ leftPresences }) => {
        console.log('Presence leave:', leftPresences);
        setParticipants(prevParticipants =>
          prevParticipants.filter(p => 
            !leftPresences.some(lp => lp.user_id === p.userId) // Compare tracked user_id with participant.userId
          )
        );
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to presence channel:', presenceChannelName);
          // Track the current user's presence
          const trackStatus = await presenceChannelRef.current.track({
            user_id: authenticatedUser.id,
            username: authenticatedUser.email, // Or a display name if available from useAuth
            // Add other info you want to share, e.g., avatar_url
            last_active: new Date().toISOString(),
            // color: from MOCK_USERS or user profile if available // Example from previous mock
          });
          if (trackStatus !== 'ok') {
            console.error('Failed to track presence:', trackStatus);
          }
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
          console.error('Presence subscription error or closed on channel', presenceChannelName, ':', status);
        }
      });

    return () => {
      if (presenceChannelRef.current) {
        presenceChannelRef.current.untrack().then(() => {
            supabase.removeChannel(presenceChannelRef.current).then(removeStatus => {
                if (removeStatus === 'ok') {
                    console.log('Successfully unsubscribed from presence channel:', presenceChannelName);
                } else {
                    console.warn('Problem removing presence channel:', presenceChannelName, '; Status:', removeStatus);
                }
            }).catch(error => {
                console.error('Exception during presence removeChannel cleanup:', presenceChannelName, error);
            });
        }).catch(untrackError => {
            console.error('Error untracking presence:', untrackError);
            // Still attempt to remove channel even if untrack fails
            supabase.removeChannel(presenceChannelRef.current).catch(e => console.error("Failed to remove presence channel post-untrack error", e));
        });
      }
    };
  // Ensure authenticatedUser is stable or use a memoized version of it if it changes frequently
  // demoStory.id is constant in demo
  }, [demoStory.id, authenticatedUser]); 
  
  // Get the current active user (This might need to be updated based on authenticatedUser now)
  const getCurrentUser = () => {
    return demoUsers[currentUserIndex];
  };
  
  // Get the user whose turn it is
  const getTurnUser = () => {
    return demoUsers[currentTurnIndex];
  };
  
  // Check if it's the current user's turn
  const isCurrentUserTurn = () => {
    // In the demo, always allow the current user to contribute
    // This makes testing easier as you can switch users and immediately contribute
    return true;
  };
  
  // Switch to the next user
  const switchUser = () => {
    const nextUserIndex = (currentUserIndex + 1) % demoUsers.length;
    setCurrentUserIndex(nextUserIndex);
    
    // For demo purposes, when switching users, also make it their turn if it's not already
    // This allows each user to contribute when you switch to them
    setCurrentTurnIndex(nextUserIndex);
  };
  
  // Advance to the next turn
  const advanceTurn = () => {
    setCurrentTurnIndex((currentTurnIndex + 1) % demoUsers.length);
  };
  
  // Handle contribution submission
  const handleContributionSubmit = (contribution: any) => {
    // Only allow submission if it's the current user's turn
    if (!isCurrentUserTurn()) {
      alert("It's not your turn to contribute!");
      return;
    }
    
    const currentUser = getCurrentUser();
    
    // Add the contribution to the list
    const newContribution = {
      id: `contribution-${contributions.length + 1}`,
      content: contribution.content,
      userId: currentUser.id,
      username: currentUser.username,
      position: contributions.length,
      type: mode,
      createdAt: new Date().toISOString(),
      color: currentUser.color,
    };
    
    setContributions([...contributions, newContribution]);
    
    // Advance to the next user's turn
    advanceTurn();
  };
  
  // Get the full story content from all contributions
  const getFullStoryContent = () => {
    return contributions.map(contribution => contribution.content).join(' ');
  };
  
  // Generate a magic title using AI
  const handleGenerateMagicTitle = async () => {
    setIsGeneratingTitle(true);
    try {
      const content = getFullStoryContent();
      const title = await generateMagicTitle(titleTopic, content);
      setStoryTitle(title);
      setTitleTopic(""); // Clear the topic input after generation
    } catch (error) {
      console.error('Error generating magic title:', error);
    } finally {
      setIsGeneratingTitle(false);
    }
  };
  
  // Generate a story description using AI
  const handleGenerateStoryDescription = async () => {
    setIsGeneratingDescription(true);
    try {
      const description = await generateStoryDescription(storyTitle, descriptionTopic);
      setStoryDescription(description);
      setDescriptionTopic(""); // Clear the topic input after generation
    } catch (error) {
      console.error('Error generating story description:', error);
    } finally {
      setIsGeneratingDescription(false);
    }
  };
  
  // Get cost for the current mode
  const getModeCost = () => {
    switch (mode) {
      case ContributionMode.WORD:
        return 1; // 1 credit for a single word
      case ContributionMode.MULTI_WORD:
        return Math.min(3, wordsPerContribution); // 1-3 credits based on word count
      case ContributionMode.SENTENCE:
        return 5; // 5 credits for a sentence
      case ContributionMode.PARAGRAPH:
        return 10; // 10 credits for a paragraph
      default:
        return 1;
    }
  };
  
  // Get mode-appropriate content type name
  const getContentTypeName = () => {
    switch (mode) {
      case ContributionMode.WORD:
        return "Word";
      case ContributionMode.MULTI_WORD:
        return `${wordsPerContribution} Words`;
      case ContributionMode.SENTENCE:
        return "Sentence";
      case ContributionMode.PARAGRAPH:
        return "Paragraph";
      default:
        return "Content";
    }
  };
  
  // Generate AI content based on the current mode
  const handleGenerateMagicContent = async () => {
    const cost = getModeCost();
    
    // Check if user has enough points
    if (points < cost) {
      alert(`You need ${cost} credits to generate a ${getContentTypeName().toLowerCase()}. You only have ${points} credits.`);
      return;
    }
    
    setIsGeneratingSentence(true);
    try {
      const content = getFullStoryContent();
      let generatedContent = "";
      const contributionType = mode;
      
      // Generate content based on mode
      switch (mode) {
        case ContributionMode.WORD: {
          // Generate a single word
          const words = await generateMagicSentence(content, sentenceTopic);
          generatedContent = words.split(/\s+/)[0]; // Take just the first word
          break;
        }
        case ContributionMode.MULTI_WORD: {
          // Generate multiple words
          const multiWords = await generateMagicSentence(content, sentenceTopic);
          generatedContent = multiWords.split(/\s+/).slice(0, wordsPerContribution).join(" ");
          break;
        }
        case ContributionMode.SENTENCE:
          // Generate a sentence
          generatedContent = await generateMagicSentence(content, sentenceTopic, gotchaWordState?.word);
          break;
        case ContributionMode.PARAGRAPH: {
          // Generate a paragraph (multiple sentences)
          const sentences = [];
          for (let i = 0; i < 3; i++) { // Generate 3 sentences for a paragraph
            const sentence = await generateMagicSentence(content + " " + sentences.join(" "), sentenceTopic, 
              i === 0 ? gotchaWordState?.word : undefined); // Only use gotcha word in first sentence
            sentences.push(sentence);
          }
          generatedContent = sentences.join(" ");
          break;
        }
      }
      
      // Add the AI-generated content as a new contribution
      const newContribution = {
        id: `contribution-ai-${Date.now()}`,
        content: generatedContent,
        userId: 'ai-assistant',
        username: 'AI Assistant',
        position: contributions.length,
        type: contributionType,
        createdAt: new Date().toISOString(),
      };
      
      setContributions([...contributions, newContribution]);
      setSentenceTopic(""); // Clear the topic input after generation
      
      // Deduct points
      setPoints(prevPoints => prevPoints - cost);
      
      // If there was an active gotcha word, mark it as used
      if (gotchaWordState?.isActive && !gotchaWordState.forNextTurn) {
        setGotchaWordState(null);
      }
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setIsGeneratingSentence(false);
    }
  };
  
  // Generate a gotcha word using AI
  const handleGenerateGotchaWord = async () => {
    // Check if there's already an active gotcha word
    if (gotchaWordState && gotchaWordState.isActive) {
      alert('You already have an active gotcha word. You can only have one at a time.');
      return;
    }
    
    setIsGeneratingGotcha(true);
    try {
      const word = await generateGotchaWord(gotchaWordDifficulty);
      
      // Create a new gotcha word state with a note that it will be used after the current turn
      const newGotchaWordState = initGotchaWordState(word, 1);
      newGotchaWordState.forNextTurn = true; // Flag to indicate this is for the next turn
      
      setGotchaWordState(newGotchaWordState);
    } catch (error) {
      console.error('Error generating gotcha word:', error);
    } finally {
      setIsGeneratingGotcha(false);
    }
  };
  
  // Generate content that includes the gotcha word, based on the current mode
  const handleGenerateGotchaSentence = async () => {
    if (!gotchaWordState || !gotchaWordState.isActive) return;
    
    // Calculate cost based on the current mode
    const cost = getModeCost();
    
    // Check if user has enough points
    if (points < cost) {
      alert(`You need ${cost} credits to get AI help with this gotcha word. You only have ${points} credits.`);
      return;
    }
    
    setIsGeneratingGotchaSentence(true);
    try {
      const context = getFullStoryContent();
      let generatedContent = "";
      const contributionType = mode;
      
      // Generate content based on mode
      switch (mode) {
        case ContributionMode.WORD:
          // In word mode, just use the gotcha word itself
          generatedContent = gotchaWordState.word;
          break;
        case ContributionMode.MULTI_WORD: {
          // For multi-word, create a phrase with the gotcha word
          const phrase = await generateSentenceWithGotchaWord(gotchaWordState.word, context);
          // Extract just the number of words needed
          const words = phrase.split(/\s+/);
          // Make sure the gotcha word is included
          const selectedWords = [];
          let gotchaIncluded = false;
          
          // First, find the gotcha word in the phrase
          for (let i = 0; i < words.length && selectedWords.length < wordsPerContribution; i++) {
            if (words[i].toLowerCase().includes(gotchaWordState.word.toLowerCase())) {
              selectedWords.push(words[i]);
              gotchaIncluded = true;
              break;
            }
          }
          
          // If gotcha word wasn't found, add it as the first word
          if (!gotchaIncluded) {
            selectedWords.push(gotchaWordState.word);
          }
          
          // Fill remaining words needed
          for (let i = 0; i < words.length && selectedWords.length < wordsPerContribution; i++) {
            if (!selectedWords.includes(words[i])) {
              selectedWords.push(words[i]);
            }
            if (selectedWords.length >= wordsPerContribution) break;
          }
          
          generatedContent = selectedWords.slice(0, wordsPerContribution).join(" ");
          break;
        }
        case ContributionMode.SENTENCE:
          // Generate a sentence with the gotcha word
          generatedContent = await generateSentenceWithGotchaWord(gotchaWordState.word, context);
          break;
        case ContributionMode.PARAGRAPH: {
          // Generate a paragraph with the gotcha word in the first sentence
          const sentences = [];
          // First sentence must include the gotcha word
          sentences.push(await generateSentenceWithGotchaWord(gotchaWordState.word, context));
          
          // Add 2 more sentences for a complete paragraph
          for (let i = 0; i < 2; i++) {
            const additionalSentence = await generateMagicSentence(
              context + " " + sentences.join(" "), 
              "", // No specific topic for continuity
              undefined // No need to include gotcha word again
            );
            sentences.push(additionalSentence);
          }
          
          generatedContent = sentences.join(" ");
          break;
        }
      }
      
      // Add the AI-generated content as a new contribution
      const newContribution = {
        id: `contribution-ai-${Date.now()}`,
        content: generatedContent,
        userId: 'ai-assistant',
        username: 'AI Assistant',
        position: contributions.length,
        type: contributionType,
        createdAt: new Date().toISOString(),
      };
      
      setContributions([...contributions, newContribution]);
      
      // Deduct points
      setPoints(prevPoints => prevPoints - cost);
      
      // Mark the gotcha word as used
      setGotchaWordState(null);
    } catch (error) {
      console.error('Error generating content with gotcha word:', error);
    } finally {
      setIsGeneratingGotchaSentence(false);
    }
  };
  
  // Use a skip card on the current gotcha word
  const handleSkipGotchaWord = () => {
    if (!gotchaWordState || points <= 0) return;
    
    // Use a point to skip the gotcha word
    setPoints(points - 1);
    setGotchaWordState(useSkipCard(gotchaWordState));
  };
  
  // Construct the story object for StoryDisplay
  const storyForDisplay: Story = {
    id: MOCK_STORY.id,
    title: storyTitle,
    description: storyDescription,
    author: MOCK_USERS.find(u => u.id === MOCK_STORY.creatorId) || { id: MOCK_STORY.creatorId, username: 'Creator' }, // Basic author mock
    status: 'in_progress', // Mock status
    max_contributors: 10, // Mock value
    max_words_per_contribution: MOCK_STORY.wordsPerContribution, // from mock
    current_word_count: contributions.reduce((acc, c) => acc + (c.content?.split(/\s+/).length || 0), 0), // Calculate from contributions
    is_public: true, // Mock value
    created_at: new Date().toISOString(), // Mock value
    updated_at: new Date().toISOString(), // Mock value
    contributions: contributions, // The live contributions array
    // genre, cover_image_url, etc., can be added if needed by StoryDisplay or if MOCK_STORY has them
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Contribution Modes Demo</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Story: {storyTitle}</h2>
          
          {/* Permission indicator */}
          <div className="text-sm text-gray-500 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {MOCK_STORY.creatorId === getCurrentUser().id ? 
              "You can edit story settings as the creator" : 
              "Only the story creator can edit these settings"}
          </div>
        </div>
        
        <p className="text-gray-600 mb-6">{storyDescription || 'No description yet.'}</p>
        
        <div className="flex space-x-4 mb-6">
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2">Magic Title</h3>
            <div className="mb-1 text-sm text-gray-500">Topic (optional)</div>
            <div className="flex space-x-2">
              <input
                type="text"
                value={titleTopic}
                onChange={(e) => setTitleTopic(e.target.value)}
                placeholder="Enter a topic or leave empty"
                className="flex-1 p-2 border rounded-md"
                disabled={MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0}
              />
              <button
                onClick={handleGenerateMagicTitle}
                disabled={isGeneratingTitle || (MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center h-10"
                title={MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0 ? "Only the story creator can change the title after the story has started" : ""}
              >
                {isGeneratingTitle ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  'Generate'
                )}
              </button>
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold mb-2">Story Description</h3>
            <div className="mb-1 text-sm text-gray-500">Topic (optional)</div>
            <div className="flex space-x-2">
              <input
                type="text"
                value={descriptionTopic}
                onChange={(e) => setDescriptionTopic(e.target.value)}
                placeholder="Enter a topic or leave empty"
                className="flex-1 p-2 border rounded-md"
                disabled={MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0}
              />
              <button
                onClick={handleGenerateStoryDescription}
                disabled={isGeneratingDescription || (MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center h-10"
                title={MOCK_STORY.creatorId !== getCurrentUser().id && contributions.length > 0 ? "Only the story creator can change the description after the story has started" : ""}
              >
                {isGeneratingDescription ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  'Generate'
                )}
              </button>
            </div>
          </div>
        </div>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={() => setMode(ContributionMode.WORD)}
            className={`px-4 py-2 rounded-md ${mode === ContributionMode.WORD ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
            disabled={contributions.length > 0}
            title={contributions.length > 0 ? "Mode cannot be changed after the story has started" : ""}
          >
            Word Mode
          </button>
          <button
            onClick={() => setMode(ContributionMode.MULTI_WORD)}
            className={`px-4 py-2 rounded-md ${mode === ContributionMode.MULTI_WORD ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} ${contributions.length > 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={contributions.length > 0}
            title={contributions.length > 0 ? "Mode cannot be changed after the story has started" : ""}
          >
            Multi-Word Mode
          </button>
          <button
            onClick={() => setMode(ContributionMode.SENTENCE)}
            className={`px-4 py-2 rounded-md ${mode === ContributionMode.SENTENCE ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} ${contributions.length > 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={contributions.length > 0}
            title={contributions.length > 0 ? "Mode cannot be changed after the story has started" : ""}
          >
            Sentence Mode
          </button>
          <button
            onClick={() => setMode(ContributionMode.PARAGRAPH)}
            className={`px-4 py-2 rounded-md ${mode === ContributionMode.PARAGRAPH ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} ${contributions.length > 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={contributions.length > 0}
            title={contributions.length > 0 ? "Mode cannot be changed after the story has started" : ""}
          >
            Paragraph Mode
          </button>
        </div>
        
        {mode === ContributionMode.MULTI_WORD && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Words Per Contribution
            </label>
            <input
              type="number"
              min={2}
              max={10}
              value={wordsPerContribution}
              onChange={(e) => setWordsPerContribution(parseInt(e.target.value))}
              className="w-24 px-3 py-2 border rounded-md"
            />
          </div>
        )}
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="mb-4">
          <h2 className="text-xl font-bold">AI Features</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Magic {getContentTypeName()}</h3>
            <div className="flex items-end gap-2">
              <div className="flex-grow">
                <label className="block text-sm font-medium text-gray-700 mb-1">Topic (optional)</label>
                <input
                  type="text"
                  value={sentenceTopic}
                  onChange={(e) => setSentenceTopic(e.target.value)}
                  placeholder="Enter a topic or leave empty"
                  className="w-full px-3 py-2 border rounded-md"
                />
              </div>
              <button
                onClick={handleGenerateMagicContent}
                disabled={isGeneratingSentence || (gotchaWordState && gotchaWordState.isActive)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center h-10"
                title={(gotchaWordState && gotchaWordState.isActive) ? "Complete the gotcha word challenge first" : ""}
              >
                {isGeneratingSentence ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  <>Generate ({getModeCost()} credits)</>  
                )}
              </button>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">AI Gotcha Word</h3>
            <div className="flex items-end gap-2">
              <div className="flex-grow">
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                <select
                  value={gotchaWordDifficulty}
                  onChange={(e) => setGotchaWordDifficulty(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border rounded-md"
                  
                >
                  <option value={1}>Easy</option>
                  <option value={2}>Medium</option>
                  <option value={3}>Hard</option>
                </select>
              </div>
              <button
                onClick={handleGenerateGotchaWord}
                disabled={isGeneratingGotcha || (gotchaWordState && gotchaWordState.isActive)}
                className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 disabled:opacity-50 flex items-center h-10"
                title={(gotchaWordState && gotchaWordState.isActive) ? "You can only have one active gotcha word at a time" : ""}
              >
                {isGeneratingGotcha ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  'Generate'
                )}
              </button>
            </div>
          </div>
        </div>
        
        {gotchaWordState && (
          <div className="mt-4 p-4 bg-yellow-100 border border-yellow-300 rounded-md">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">AI Gotcha Word: 
                  <span className={`${gotchaWordState.isActive ? 'text-pink-600' : 'text-gray-500 line-through'} font-bold ml-1`}>
                    {gotchaWordState.word}
                  </span>
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  {!gotchaWordState.isActive 
                    ? "This word has been skipped." 
                    : gotchaWordState.forNextTurn 
                      ? "This word will be used after your current turn." 
                      : "You must use this word in your next contribution!"}
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <div className="text-sm text-gray-700">
                  <span className="font-medium">{points}</span> credits
                </div>
                {gotchaWordState.isActive && (
                  <div className="flex gap-2">
                    <button
                      onClick={handleGenerateGotchaSentence}
                      disabled={isGeneratingGotchaSentence || points < getModeCost()}
                      className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 disabled:opacity-50 flex items-center"
                      title={points < getModeCost() ? `You need ${getModeCost()} credits for AI Help` : ""}
                    >
                      {isGeneratingGotchaSentence ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          AI Help
                        </>
                      ) : (
                        `AI Help (${getModeCost()})`
                      )}
                    </button>
                    <button
                      onClick={handleSkipGotchaWord}
                      disabled={!gotchaWordState.canBeSkipped || points <= 0}
                      className="px-3 py-1 bg-orange-500 text-white text-sm rounded hover:bg-orange-600 disabled:opacity-50"
                    >
                      Skip ({points})
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <StoryDisplay
            story={storyForDisplay}
            currentUserId={getCurrentUser().id}
          />
          
          {/* AD PLACEMENT */}
          <div className="my-6">
            <RotatingAdvertisement placement="content" /> 
          </div>
          
          <div className="mt-6">
            <ContributionForm
              storyId={MOCK_STORY.id}
              userId={getCurrentUser().id}
              username={getCurrentUser().username}
              mode={mode}
              wordsPerContribution={wordsPerContribution}
              disabled={false} // Always enabled in this demo for easier testing
              onSubmit={handleContributionSubmit}
              gotchaWord={gotchaWordState?.isActive ? gotchaWordState.word : undefined}
            />
          </div>
        </div>
        
        <div>
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">Current Turn</h3>
            <div className="p-3 border rounded-md">
              <div className="flex items-center">
                <span 
                  className="inline-block w-4 h-4 rounded-full mr-2"
                  style={{ backgroundColor: getTurnUser().color }}
                ></span>
                <span className="font-medium">{getTurnUser().username}'s turn</span>
              </div>
            </div>
          </div>
          
          <ParticipantsList
            participants={participants}
            currentTurnUserId={getTurnUser().id}
            currentUserId={getCurrentUser().id}
          />
          
          <div className="bg-white rounded-lg shadow-md p-4 mt-6">
            <h3 className="text-lg font-semibold mb-3">Story Info</h3>
            <div className="space-y-2">
              <p>
                <span className="font-medium">Mode:</span>{' '}
                {mode === ContributionMode.WORD
                  ? 'Word-by-Word'
                  : mode === ContributionMode.MULTI_WORD
                  ? 'Multi-Word'
                  : mode === ContributionMode.SENTENCE
                  ? 'Sentence'
                  : 'Paragraph'}
              </p>
              {mode === ContributionMode.MULTI_WORD && (
                <p>
                  <span className="font-medium">Words per turn:</span>{' '}
                  {wordsPerContribution}
                </p>
              )}
              <p>
                <span className="font-medium">Word count:</span>{' '}
                {contributions.reduce((count, contribution) => {
                  if (contribution.type === ContributionMode.WORD || 
                      contribution.type === ContributionMode.MULTI_WORD) {
                    return count + 1;
                  } else {
                    return count + contribution.content.trim().split(/\s+/).length;
                  }
                }, 0)}
              </p>
              <p>
                <span className="font-medium">Contributions:</span>{' '}
                {contributions.length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContributionModesDemo;
