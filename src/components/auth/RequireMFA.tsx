import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle, Lock, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import MFASetup from "./MFASetup";

interface RequireMFAProps {
  onComplete?: () => void;
  onCancel?: () => void;
}

const RequireMFA: React.FC<RequireMFAProps> = ({ onComplete, onCancel }) => {
  const [showSetup, setShowSetup] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleCancel = () => {
    toast({
      title: "MFA Required",
      description:
        "Multi-factor authentication is required to use this application",
      variant: "destructive",
    });

    if (onCancel) {
      onCancel();
    } else {
      navigate("/login");
    }
  };

  const handleSetupComplete = () => {
    toast({
      title: "MFA Setup Complete",
      description:
        "You can now continue using the application with added security",
    });

    if (onComplete) {
      onComplete();
    } else {
      navigate("/dashboard");
    }
  };

  if (showSetup) {
    return (
      <MFASetup onComplete={handleSetupComplete} onCancel={handleCancel} />
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-literary-burgundy" />
          <span>Multi-Factor Authentication Required</span>
        </CardTitle>
        <CardDescription>
          Your account requires additional security
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="bg-amber-50 text-amber-800 p-3 rounded-md flex items-start mb-4">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <div>
            <p className="font-medium">
              Multi-factor authentication is required
            </p>
            <p className="text-sm mt-1">
              For your security, this application requires all accounts to set
              up multi-factor authentication. This helps protect your account
              from unauthorized access.
            </p>
          </div>
        </div>

        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-start">
            <Lock className="h-4 w-4 mr-2 mt-0.5" />
            <p>MFA adds an extra layer of security to your account</p>
          </div>
          <div className="flex items-start">
            <Shield className="h-4 w-4 mr-2 mt-0.5" />
            <p>
              You'll need to verify your identity with your phone each time you
              sign in
            </p>
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="ghost" onClick={handleCancel}>
          Cancel
        </Button>

        <Button
          onClick={() => setShowSetup(true)}
          className="bg-literary-burgundy hover:bg-opacity-90"
        >
          Set Up MFA Now
        </Button>
      </CardFooter>
    </Card>
  );
};

export default RequireMFA;
