@echo off
echo ===================================================
echo Word by Word Story - Development Environment
echo ===================================================

echo Step 1: Starting CORS proxy server...
start cmd /k "run-cors-proxy.bat"

echo Step 2: Waiting 3 seconds for server to start...
timeout /t 3

echo Step 3: Starting patched Vite server...
start cmd /k "run-vite-patched.bat"

echo.
echo Development environment started!
echo - CORS proxy: http://localhost:8080
echo - Application: http://localhost:5173
echo.
echo Press any key to exit this window...
pause > nul