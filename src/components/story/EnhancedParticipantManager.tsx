/**
 * Enhanced Participant Manager Component
 * Provides story creator controls for dynamic participant management
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Users,
  UserPlus,
  MoreVertical,
  Crown,
  Eye,
  Edit3,
  UserX,
  VolumeX,
  Volume2,
  Copy,
  Mail,
  Clock,
  Shield,
  AlertTriangle
} from 'lucide-react';
import {
  getStoryParticipants,
  removeParticipant,
  updateParticipantRole,
  toggleParticipantMute,
  createStoryInvitation,
  getStoryInvitations,
  revokeStoryInvitation,
  isStoryCreator,
  type StoryParticipant,
  type StoryInvitation
} from '@/services/participantService';

interface EnhancedParticipantManagerProps {
  storyId: string;
  storyTitle: string;
  isOpen: boolean;
  onClose: () => void;
  onParticipantsUpdated?: (participants: StoryParticipant[]) => void;
}

export const EnhancedParticipantManager: React.FC<EnhancedParticipantManagerProps> = ({
  storyId,
  storyTitle,
  isOpen,
  onClose,
  onParticipantsUpdated
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // State management
  const [participants, setParticipants] = useState<StoryParticipant[]>([]);
  const [invitations, setInvitations] = useState<StoryInvitation[]>([]);
  const [isCreator, setIsCreator] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Dialog states
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [selectedParticipant, setSelectedParticipant] = useState<StoryParticipant | null>(null);
  
  // Invite form state
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'contributor' | 'viewer'>('contributor');
  const [inviteMaxUses, setInviteMaxUses] = useState(1);
  const [inviteExpiryDays, setInviteExpiryDays] = useState(7);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen && user && storyId) {
      loadParticipantData();
    }
  }, [isOpen, user, storyId]);

  const loadParticipantData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Load participants and check creator status
      const [participantsData, creatorStatus] = await Promise.all([
        getStoryParticipants(storyId),
        isStoryCreator(storyId, user.id)
      ]);

      setParticipants(participantsData);
      setIsCreator(creatorStatus);
      onParticipantsUpdated?.(participantsData);

      // Load invitations if user is creator
      if (creatorStatus) {
        const invitationsData = await getStoryInvitations(storyId, user.id);
        setInvitations(invitationsData);
      }
    } catch (error) {
      console.error('Error loading participant data:', error);
      toast({
        title: 'Error loading participants',
        description: 'Failed to load participant data. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveParticipant = async () => {
    if (!selectedParticipant || !user) return;

    setIsLoading(true);
    try {
      const result = await removeParticipant(
        storyId,
        selectedParticipant.user_id,
        user.id
      );

      if (result.success) {
        await loadParticipantData();
        setShowRemoveDialog(false);
        setSelectedParticipant(null);
      } else {
        toast({
          title: 'Error removing participant',
          description: result.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error removing participant:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateRole = async (participantUserId: string, newRole: 'contributor' | 'viewer') => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await updateParticipantRole(storyId, participantUserId, newRole, user.id);

      if (result.success) {
        await loadParticipantData();
      } else {
        toast({
          title: 'Error updating role',
          description: result.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error updating participant role:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleMute = async (participantUserId: string, mute: boolean) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await toggleParticipantMute(storyId, participantUserId, mute, user.id);

      if (result.success) {
        await loadParticipantData();
      } else {
        toast({
          title: 'Error updating participant',
          description: result.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error toggling participant mute:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateInvitation = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await createStoryInvitation(storyId, inviteRole, user.id, {
        email: inviteEmail || undefined,
        maxUses: inviteMaxUses,
        expiresInDays: inviteExpiryDays
      });

      if (result.success) {
        await loadParticipantData();
        setShowInviteDialog(false);
        setInviteEmail('');
        
        // Copy invite link to clipboard
        if (result.data?.inviteLink) {
          navigator.clipboard.writeText(result.data.inviteLink);
          toast({
            title: 'Invitation created',
            description: 'Invite link copied to clipboard!',
          });
        }
      } else {
        toast({
          title: 'Error creating invitation',
          description: result.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating invitation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRevokeInvitation = async (invitationId: string) => {
    if (!user) return;

    try {
      const result = await revokeStoryInvitation(invitationId, user.id);

      if (result.success) {
        await loadParticipantData();
      } else {
        toast({
          title: 'Error revoking invitation',
          description: result.error,
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error revoking invitation:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'creator': return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'contributor': return <Edit3 className="w-4 h-4 text-blue-500" />;
      case 'viewer': return <Eye className="w-4 h-4 text-gray-500" />;
      default: return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active': return <Badge variant="default">Active</Badge>;
      case 'muted': return <Badge variant="secondary">Muted</Badge>;
      case 'banned': return <Badge variant="destructive">Banned</Badge>;
      default: return null;
    }
  };

  const getInvitationStatusBadge = (status: string) => {
    switch (status) {
      case 'pending': return <Badge variant="default">Pending</Badge>;
      case 'accepted': return <Badge variant="secondary">Used</Badge>;
      case 'expired': return <Badge variant="outline">Expired</Badge>;
      case 'revoked': return <Badge variant="destructive">Revoked</Badge>;
      default: return null;
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Main Participant Manager Dialog */}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Manage Participants - {storyTitle}
            </DialogTitle>
            <DialogDescription>
              {isCreator 
                ? 'Manage participants, roles, and invitations for your story.'
                : 'View story participants and their roles.'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Participants Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Participants ({participants.length})</h3>
                {isCreator && (
                  <Button
                    onClick={() => setShowInviteDialog(true)}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <UserPlus className="w-4 h-4" />
                    Invite
                  </Button>
                )}
              </div>

              <ScrollArea className="h-[300px] border rounded-lg p-4">
                <div className="space-y-3">
                  {participants.map((participant) => (
                    <div
                      key={participant.id}
                      className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {getRoleIcon(participant.role)}
                          <div>
                            <p className="font-medium">
                              {participant.profile?.username || 'Anonymous'}
                            </p>
                            <p className="text-sm text-muted-foreground capitalize">
                              {participant.role}
                            </p>
                          </div>
                        </div>
                        {getStatusBadge(participant.status)}
                      </div>

                      {isCreator && participant.role !== 'creator' && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleUpdateRole(
                                participant.user_id,
                                participant.role === 'contributor' ? 'viewer' : 'contributor'
                              )}
                            >
                              {participant.role === 'contributor' ? (
                                <>
                                  <Eye className="w-4 h-4 mr-2" />
                                  Make Viewer
                                </>
                              ) : (
                                <>
                                  <Edit3 className="w-4 h-4 mr-2" />
                                  Make Contributor
                                </>
                              )}
                            </DropdownMenuItem>
                            
                            <DropdownMenuItem
                              onClick={() => handleToggleMute(
                                participant.user_id,
                                participant.status !== 'muted'
                              )}
                            >
                              {participant.status === 'muted' ? (
                                <>
                                  <Volume2 className="w-4 h-4 mr-2" />
                                  Unmute
                                </>
                              ) : (
                                <>
                                  <VolumeX className="w-4 h-4 mr-2" />
                                  Mute
                                </>
                              )}
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />
                            
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedParticipant(participant);
                                setShowRemoveDialog(true);
                              }}
                              className="text-destructive"
                            >
                              <UserX className="w-4 h-4 mr-2" />
                              Remove
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>

            {/* Invitations Section (Creator Only) */}
            {isCreator && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Active Invitations ({invitations.length})</h3>
                
                <ScrollArea className="h-[300px] border rounded-lg p-4">
                  <div className="space-y-3">
                    {invitations.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <Mail className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p>No active invitations</p>
                        <p className="text-sm">Create an invitation to invite new participants</p>
                      </div>
                    ) : (
                      invitations.map((invitation) => (
                        <div
                          key={invitation.id}
                          className="p-3 bg-muted/30 rounded-lg space-y-2"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <code className="text-sm bg-background px-2 py-1 rounded">
                                {invitation.invite_code}
                              </code>
                              {getInvitationStatusBadge(invitation.status)}
                            </div>
                            
                            {invitation.status === 'pending' && (
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreVertical className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => {
                                      const inviteLink = `${window.location.origin}/join/${invitation.invite_code}`;
                                      navigator.clipboard.writeText(inviteLink);
                                      toast({
                                        title: 'Link copied',
                                        description: 'Invitation link copied to clipboard',
                                      });
                                    }}
                                  >
                                    <Copy className="w-4 h-4 mr-2" />
                                    Copy Link
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => handleRevokeInvitation(invitation.id)}
                                    className="text-destructive"
                                  >
                                    <UserX className="w-4 h-4 mr-2" />
                                    Revoke
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            )}
                          </div>
                          
                          <div className="text-sm text-muted-foreground">
                            <p>Role: {invitation.permissions}</p>
                            <p>Uses: {invitation.current_uses}/{invitation.max_uses}</p>
                            <p className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              Expires: {new Date(invitation.expires_at).toLocaleDateString()}
                            </p>
                            {invitation.email && (
                              <p className="flex items-center gap-1">
                                <Mail className="w-3 h-3" />
                                {invitation.email}
                              </p>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Invitation Dialog */}
      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Invitation</DialogTitle>
            <DialogDescription>
              Create an invitation link to invite new participants to your story.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="invite-role">Role</Label>
              <Select value={inviteRole} onValueChange={(value: 'contributor' | 'viewer') => setInviteRole(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="contributor">Contributor (can add content)</SelectItem>
                  <SelectItem value="viewer">Viewer (read-only)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="invite-email">Email (optional)</Label>
              <Input
                id="invite-email"
                type="email"
                placeholder="<EMAIL>"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="max-uses">Max Uses</Label>
                <Input
                  id="max-uses"
                  type="number"
                  min="1"
                  max="10"
                  value={inviteMaxUses}
                  onChange={(e) => setInviteMaxUses(parseInt(e.target.value) || 1)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expiry-days">Expires in (days)</Label>
                <Input
                  id="expiry-days"
                  type="number"
                  min="1"
                  max="30"
                  value={inviteExpiryDays}
                  onChange={(e) => setInviteExpiryDays(parseInt(e.target.value) || 7)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateInvitation} disabled={isLoading}>
              Create Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Participant Confirmation Dialog */}
      <Dialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-destructive" />
              Remove Participant
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to remove {selectedParticipant?.profile?.username || 'this participant'} from the story? 
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setShowRemoveDialog(false);
                setSelectedParticipant(null);
              }}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleRemoveParticipant}
              disabled={isLoading}
            >
              Remove Participant
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};