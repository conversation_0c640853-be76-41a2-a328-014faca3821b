import React from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Crown, 
  Zap, 
  MoreVertical, 
  UserPlus,
  Settings,
  Volume2,
  VolumeX
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';

export interface Participant {
  id: string;
  username: string;
  avatar_url?: string;
  role: 'creator' | 'contributor' | 'viewer';
  isOnline: boolean;
  lastSeen?: Date;
  contributionCount: number;
  joinedAt: Date;
  isTyping?: boolean;
}

export interface ParticipantsSidebarProps {
  participants: Participant[];
  currentUserId: string;
  storyCreatorId: string;
  isOpen: boolean;
  onClose: () => void;
  onInviteUser?: () => void;
  onKickUser?: (userId: string) => void;
  onPromoteUser?: (userId: string) => void;
  onMuteUser?: (userId: string) => void;
  className?: string;
}

export const ParticipantsSidebar: React.FC<ParticipantsSidebarProps> = ({
  participants,
  currentUserId,
  storyCreatorId,
  isOpen,
  onClose,
  onInviteUser,
  onKickUser,
  onPromoteUser,
  onMuteUser,
  className,
}) => {
  const currentUser = participants.find(p => p.id === currentUserId);
  const isCreator = currentUserId === storyCreatorId;

  // Sort participants: creator first, then by online status, then by contribution count
  const sortedParticipants = [...participants].sort((a, b) => {
    if (a.id === storyCreatorId) return -1;
    if (b.id === storyCreatorId) return 1;
    if (a.isOnline !== b.isOnline) return b.isOnline ? 1 : -1;
    return b.contributionCount - a.contributionCount;
  });

  const onlineCount = participants.filter(p => p.isOnline).length;
  const totalCount = participants.length;

  if (!isOpen) return null;

  return (
    <div className={cn(
      'fixed inset-y-0 right-0 z-50 w-80 bg-background border-l shadow-lg',
      'transform transition-transform duration-300 ease-in-out',
      'lg:relative lg:transform-none lg:shadow-none',
      className
    )}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold">Participants</h3>
            <Badge variant="secondary" className="text-xs">
              {onlineCount}/{totalCount}
            </Badge>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Stats */}
        <div className="p-4 border-b bg-muted/30">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{onlineCount}</div>
              <div className="text-xs text-muted-foreground">Online now</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {participants.reduce((sum, p) => sum + p.contributionCount, 0)}
              </div>
              <div className="text-xs text-muted-foreground">Total contributions</div>
            </div>
          </div>
        </div>

        {/* Invite Button */}
        {(isCreator || currentUser?.role === 'contributor') && (
          <div className="p-4 border-b">
            <Button 
              onClick={onInviteUser}
              className="w-full"
              variant="outline"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Invite Contributors
            </Button>
          </div>
        )}

        {/* Participants List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {sortedParticipants.map((participant) => (
              <div
                key={participant.id}
                className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors"
              >
                {/* Avatar with Online Status */}
                <div className="relative">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={participant.avatar_url} alt={participant.username} />
                    <AvatarFallback className="text-sm">
                      {participant.username.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  {/* Online indicator */}
                  <div className={cn(
                    'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background',
                    participant.isOnline ? 'bg-green-500' : 'bg-gray-400'
                  )} />
                  
                  {/* Typing indicator */}
                  {participant.isTyping && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                  )}
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm truncate">
                      {participant.username}
                    </span>
                    
                    {/* Role indicators */}
                    {participant.id === storyCreatorId && (
                      <Crown className="w-3 h-3 text-amber-500" />
                    )}
                    
                    {participant.isTyping && (
                      <span className="text-xs text-blue-500 animate-pulse">typing...</span>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{participant.contributionCount} contributions</span>
                    {!participant.isOnline && participant.lastSeen && (
                      <span>• {formatDistanceToNow(participant.lastSeen, { addSuffix: true })}</span>
                    )}
                  </div>
                </div>

                {/* Actions Menu */}
                {(isCreator && participant.id !== currentUserId) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {participant.role === 'viewer' && (
                        <DropdownMenuItem onClick={() => onPromoteUser?.(participant.id)}>
                          <Zap className="w-4 h-4 mr-2" />
                          Promote to Contributor
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => onMuteUser?.(participant.id)}>
                        <VolumeX className="w-4 h-4 mr-2" />
                        Mute User
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => onKickUser?.(participant.id)}
                        className="text-destructive"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Remove from Story
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="p-4 border-t bg-muted/30">
          <div className="text-xs text-muted-foreground text-center">
            Story created {formatDistanceToNow(new Date(), { addSuffix: true })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParticipantsSidebar;