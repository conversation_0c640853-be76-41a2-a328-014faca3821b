import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import {
  Home,
  Book,
  Plus,
  BookO<PERSON>,
  Crown,
  Bar<PERSON>hart,
  User,
} from "lucide-react";

const MobileNav = () => {
  const { isAuthenticated, isAdmin } = useAuth();
  const location = useLocation();

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-literary-navy text-white py-2 px-4 shadow-[0_-2px_10px_rgba(0,0,0,0.1)] z-50">
      <div className="flex items-center justify-around">
        {[
          { path: "/", icon: Home, label: "Home" },
          ...(isAuthenticated
            ? [
                { path: "/dashboard", icon: Book, label: "Stories" },
                { path: "/create-story", icon: Plus, label: "Create" },
              ]
            : []),
          { path: "/gallery", icon: BookO<PERSON>, label: "Gallery" },
          { path: "/pricing", icon: Crown, label: "Pricing" },
          ...(isAdmin()
            ? [{ path: "/admin", icon: Bar<PERSON>hart, label: "Admin" }]
            : []),
          { path: "/profile", icon: User, label: "Profile" },
        ].map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`
              flex flex-col items-center p-2 
              ${
                location.pathname === item.path
                  ? "text-literary-gold"
                  : "text-white/70 hover:text-literary-gold"
              }
              transition-colors group
            `}
          >
            <item.icon
              size={20}
              className="group-hover:scale-110 transition-transform"
            />
            <span className="text-xs mt-1 opacity-80 group-hover:opacity-100 transition-opacity">
              {item.label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default MobileNav;
