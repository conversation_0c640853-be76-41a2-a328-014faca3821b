import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertCircle,
  Loader2,
  LogIn,
  Shield,
  Wifi,
  WifiOff,
  CheckCircle,
  ExternalLink,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import OTPVerification from "@/components/auth/OTPVerification";
import TermsAcceptance from "@/components/auth/TermsAcceptance";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useDocumentTitle, createPageTitle } from "@/hooks/useDocumentTitle";
import FirebaseGoogleSignIn from "@/components/auth/FirebaseGoogleSignIn";

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [phoneOrEmail, setPhoneOrEmail] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isOTPSent, setIsOTPSent] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("password");
  const [isOnline, setIsOnline] = useState<boolean | null>(null);
  const [showTermsAcceptance, setShowTermsAcceptance] = useState(false);
  const [domainError, setDomainError] = useState<string | null>(null);

  const { login, user, loginWithGoogle } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // Set page title
  useDocumentTitle(createPageTitle("Login"));

  useEffect(() => {
    setIsOnline(navigator.onLine);
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isOnline) {
      setError("You appear to be offline. Please check your internet connection.");
      toast({ title: "Connection issue", description: "Please check your internet connection and try again", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setError("");
    try {
      await login(email, password, rememberMe);
      // Don't navigate immediately - let the auth state change handle it
      // The RequireTerms component will handle the redirect after checking terms
      toast({ title: "Login successful", description: "Welcome back!" });
    } catch (err: any) {
      setError(err.message || "Failed to log in. Please check your credentials.");
      toast({ title: "Login failed", description: err.message || "Please check your credentials", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    try {
      await loginWithGoogle();
      toast({ title: "Redirecting...", description: "Please wait while we redirect you to Google." });
    } catch (err: any) {
      setError(err.message || "Failed to login with Google");
      toast({ title: "Google login failed", description: err.message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoLogin = async () => {
    // ... (Keep demo login if needed, or remove if fully Supabase)
  };

  const handleTermsAccepted = () => {
    setShowTermsAcceptance(false);
    navigate("/dashboard");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-literary-cream p-4">
      <Card className="w-full max-w-md shadow-book">
        <CardHeader>
          <CardTitle className="text-3xl font-serif text-center text-literary-navy">Login</CardTitle>
          <CardDescription className="text-center text-literary-navy/80">
            Welcome back to Word By Word Story!
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isOnline === false && (
            <Alert variant="destructive" className="mb-4">
              <AlertTitle>Offline</AlertTitle>
              <AlertDescription>
                You are currently offline. Please check your internet connection.
              </AlertDescription>
            </Alert>
          )}
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTitle>Login Failed</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {showTermsAcceptance && user && (
            <TermsAcceptance onComplete={handleTermsAccepted} onCancel={() => setShowTermsAcceptance(false)} />
          )}
          {!showTermsAcceptance && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} required className="mt-1" />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input id="password" type="password" value={password} onChange={(e) => setPassword(e.target.value)} required className="mt-1" />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Checkbox id="remember-me" checked={rememberMe} onCheckedChange={(checked) => setRememberMe(Boolean(checked))} />
                  <Label htmlFor="remember-me" className="ml-2 block text-sm text-literary-navy/90">
                    Remember me
                  </Label>
                </div>
                <div className="text-sm">
                  <Link to="/forgot-password" className="font-medium text-literary-burgundy hover:text-literary-burgundy/80">
                    Forgot your password?
                  </Link>
                </div>
              </div>
              <Button type="submit" disabled={isLoading || isOnline === false} className="w-full bg-literary-burgundy hover:bg-opacity-90">
                {isLoading ? "Logging in..." : "Log in"}
              </Button>
            </form>
          )}
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-white px-2 text-gray-500">Or continue with</span>
              </div>
            </div>
            <FirebaseGoogleSignIn
              onSuccess={() => {
                // Don't navigate immediately - let the auth state change handle it
                // The RequireTerms component will handle the redirect after checking terms
                toast({ title: "Login successful", description: "Welcome back!" });
              }}
              onError={(error) => {
                setError(error.message || "Failed to login with Google");
              }}
            />
          </div>
        </CardContent>
        <CardFooter className="text-center text-sm text-literary-navy/80">
          Don't have an account?{" "}
          <Link to="/register" className="font-medium text-literary-burgundy hover:text-literary-burgundy/80">
            Sign up
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
};

export default LoginPage;
