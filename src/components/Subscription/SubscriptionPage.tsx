import React from 'react';
import { useSubscription } from '../../hooks/useSubscription';
import { createCheckoutSession, manageSubscription } from '../../services/subscription';
import CurrentSubscriptionCard from './CurrentSubscriptionCard';
import SubscriptionPlansCard from './SubscriptionPlansCard';
import { trackSubscriptionEvent } from '../../lib/posthog';

// Define a basic structure for what a pricing plan might look like
// This would typically come from your backend or Stripe configuration
export interface PricingPlan {
  id: string; // Stripe Price ID (e.g., price_xxxxxxxxxxxxxx)
  name: string;
  description: string;
  price: string; // Formatted price, e.g., "$10/month"
  features: string[];
  isCurrentPlan?: boolean; // Optional: if this is the user's current plan
}

const SubscriptionPage: React.FC = () => {
  const { isSubscribed, subscriptionStatus, loading, error } = useSubscription();

  // Placeholder for your actual pricing plans
  // In a real app, you might fetch these from Stripe or a backend endpoint
  const availablePlans: PricingPlan[] = [
    {
      id: 'YOUR_STRIPE_PRICE_ID_MONTHLY', // Replace with actual Stripe Price ID
      name: 'Ad-Free Monthly',
      description: 'Enjoy an ad-free experience across the platform.',
      price: '$1.99/month', // Updated price
      features: ['No advertisements', 'Support the platform'],
    },
    {
      id: 'YOUR_STRIPE_PRICE_ID_YEARLY', // Replace with actual Stripe Price ID
      name: 'Ad-Free Yearly',
      description: 'Get the best value with an annual ad-free subscription.',
      price: '$19.99/year', // Updated price
      features: ['No advertisements', 'Support the platform', 'Save 16%'],
    },
  ];

  const handleSubscribe = async (priceId: string) => {
    try {
      // Find the plan being subscribed to for tracking
      const selectedPlan = availablePlans.find(plan => plan.id === priceId);
      
      // Track subscription initiation
      trackSubscriptionEvent.subscriptionStarted(priceId, selectedPlan?.name || 'Unknown Plan', {
        plan_price: selectedPlan?.price,
        initiation_timestamp: new Date().toISOString(),
      });
      
      // Define your success and cancel URLs
      // These should be routes in your application that can display appropriate messages
      const successUrl = `${window.location.origin}/payment-success?session_id={CHECKOUT_SESSION_ID}`;
      const cancelUrl = `${window.location.origin}/payment-cancelled`;
      await createCheckoutSession(priceId, successUrl, cancelUrl);
    } catch (err) {
      console.error('Error creating checkout session:', err);
      
      // Track payment failure
      trackSubscriptionEvent.paymentFailed(priceId, err instanceof Error ? err.message : 'unknown_error', {
        error_timestamp: new Date().toISOString(),
        checkout_failed: true,
      });
      
      // Display error to user, e.g., using a toast notification
      alert(`Error: ${err instanceof Error ? err.message : 'Could not start subscription process.'}`);
    }
  };

  const handleManageSubscription = async () => {
    try {
      await manageSubscription();
    } catch (err) {
      console.error('Error redirecting to customer portal:', err);
      alert(`Error: ${err instanceof Error ? err.message : 'Could not open subscription management.'}`);
    }
  };

  if (loading) {
    return <div className="p-4">Loading subscription status...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-600">Error loading subscription status: {error.message}</div>;
  }

  return (
    <div className="container mx-auto p-4 space-y-8">
      <h1 className="text-3xl font-bold">Subscription Management</h1>
      
      {isSubscribed ? (
        <CurrentSubscriptionCard
          subscriptionStatus={subscriptionStatus}
          isSubscribed={isSubscribed}
          currentPlan={undefined} // You can pass current plan info here if available
          onManage={handleManageSubscription}
        />
      ) : (
        <SubscriptionPlansCard
          plans={availablePlans}
          onSubscribe={handleSubscribe}
          isLoading={loading}
        />
      )}
    </div>
  );
};

export default SubscriptionPage;
