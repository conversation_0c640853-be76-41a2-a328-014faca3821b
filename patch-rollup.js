// <PERSON>ript to patch the Rollup native.js file
const fs = require("fs");
const path = require("path");

const nativeJsPath = path.join(
  process.cwd(),
  "node_modules",
  "rollup",
  "dist",
  "native.js",
);

// Check if the file exists
if (fs.existsSync(nativeJsPath)) {
  console.log(`Patching ${nativeJsPath}...`);

  // Create the patched content
  const patchedContent = `// This is a patched version of the Rollup native.js file
// It forces Rollup to use the JS implementation instead of native modules

'use strict';

// Mock the native module to always return null
function requireWithFriendlyError() {
  return null;
}

// Export a mock native module
const native = null;

// Export the mock
module.exports = native;`;

  // Backup the original file
  const backupPath = `${nativeJsPath}.backup`;
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(nativeJsPath, backupPath);
    console.log(`Original file backed up to ${backupPath}`);
  }

  // Write the patched content
  fs.writeFileSync(nativeJsPath, patchedContent);
  console.log("File patched successfully!");
} else {
  console.error(`File not found: ${nativeJsPath}`);
  console.log("Make sure you have installed the dependencies with npm install");
}
