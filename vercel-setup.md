# Vercel Setup

For complete instructions on running and deploying across environments, see **DEPLOY_AND_RUN.md**.

## CLI Installation & Auth

```bash
yarn global add vercel
vercel login
```

## Link & Configure

```bash
vercel link
yarn setup:vercel-env
```

## Local Development & Functions

```bash
npx vercel dev
```

## Deploy to Vercel

```bash
yarn deploy:development
yarn deploy:preview
yarn deploy:production
```
