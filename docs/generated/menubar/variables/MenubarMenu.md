[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [menubar](../README.md) / MenubarMenu

# Variable: MenubarMenu()

> `const` **MenubarMenu**: (`props`) => `Element` = `MenubarPrimitive.Menu`

Defined in: [src/components/ui/menubar.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/menubar.tsx#L7)

## Parameters

### props

`ScopedProps`\<`MenubarMenuProps`\>

## Returns

`Element`
