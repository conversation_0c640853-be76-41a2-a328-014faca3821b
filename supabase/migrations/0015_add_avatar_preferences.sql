-- Migration: Add Avatar Preferences to User Profiles
-- File: supabase/migrations/0015_add_avatar_preferences.sql

-- Add avatar_preferences column to store user's avatar settings
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS avatar_preferences JSONB DEFAULT '{
  "source": "gravatar",
  "useGravatar": true
}'::jsonb;

-- Add comment for documentation
COMMENT ON COLUMN public.user_profiles.avatar_preferences IS 'User avatar preferences including source (gravatar/custom/default), custom URL, and Gravatar usage setting';

-- Create an index on avatar preferences for queries
CREATE INDEX IF NOT EXISTS idx_user_profiles_avatar_preferences 
ON public.user_profiles USING GIN (avatar_preferences);

-- Add check constraint to ensure valid avatar preferences structure
ALTER TABLE public.user_profiles 
ADD CONSTRAINT check_avatar_preferences_valid 
CHECK (
  avatar_preferences IS NULL OR (
    avatar_preferences ? 'source' AND
    avatar_preferences ? 'useGravatar' AND
    avatar_preferences->>'source' IN ('gravatar', 'custom', 'default') AND
    (avatar_preferences->>'useGravatar')::boolean IN (true, false)
  )
);

-- Update existing users to have default avatar preferences
UPDATE public.user_profiles 
SET avatar_preferences = '{
  "source": "gravatar",
  "useGravatar": true
}'::jsonb
WHERE avatar_preferences IS NULL; 