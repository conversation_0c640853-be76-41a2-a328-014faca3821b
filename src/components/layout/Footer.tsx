import React from "react";
import { Link } from "react-router-dom";
import { Book } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { CONTACT_INFO } from "@/config/contact";
import GoogleAd from "@/components/ads/GoogleAd";

const Footer = () => {
  const { isAuthenticated } = useAuth();

  return (
    <footer className="bg-literary-navy text-white py-6 px-4 border-t border-literary-gold/20">
      <div className="container mx-auto">
        <GoogleAd
          placement="footer"
          slot={import.meta.env.VITE_ADSENSE_SLOT_FOOTER}
          className="mx-auto my-4"
        />
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0 flex items-center gap-2 group">
            <Book
              size={24}
              className="text-literary-gold group-hover:rotate-6 transition-transform"
            />
            <h2 className="text-xl font-serif font-bold text-literary-gold/90 group-hover:text-literary-gold transition-colors">
              Word by Word Story
            </h2>
          </div>
          <div className="flex flex-col md:flex-row gap-6">
            {[
              { path: "/", label: "Home" },
              { path: "/gallery", label: "Gallery" },
              { path: "/pricing", label: "Pricing" },
              ...(isAuthenticated
                ? [{ path: "/dashboard", label: "Dashboard" }]
                : [{ path: "/register", label: "Register" }]),
            ].map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className="text-white/80 hover:text-literary-gold transition-colors relative group"
              >
                <span className="relative">
                  {item.label}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-literary-gold transition-all duration-300 group-hover:w-full"></span>
                </span>
              </Link>
            ))}
          </div>
        </div>
        <div className="mt-4 text-center text-sm text-gray-400">
          <p>Contact Support: {CONTACT_INFO.email}</p>
          <p>Call us: {CONTACT_INFO.formattedPhoneDisplay}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
