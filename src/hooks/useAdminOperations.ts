import { useState, useCallback, useEffect } from 'react';import { useAuth } from '@/contexts/auth';import { useToast } from '@/hooks/use-toast';
import { 
  adminService,
  AdminTokenGrant,
  AdminSession,
  AdminAction,
  TokenGrantResult,
  BulkGrantResult,
  UserSearchResult
} from '@/services/adminService';

export interface UseAdminOperationsReturn {
  // State
  isLoadingOperation: boolean;
  searchResults: UserSearchResult[];
  tokenGrantHistory: AdminTokenGrant[];
  adminSessions: AdminSession[];
  adminActions: AdminAction[];
  currentAdminSession: string | null;

  // Operations
  searchUsers: (searchTerm: string, includeTokenBalance?: boolean) => Promise<UserSearchResult[]>;
  grantTokens: (targetUserId: string, amount: number, reason: string) => Promise<TokenGrantResult>;
  bulkGrantTokens: (targetUserIds: string[], amount: number, reason: string) => Promise<BulkGrantResult>;
  getUserTokenBalance: (userId: string) => Promise<number | null>;

  // Session management
  startAdminSession: () => Promise<boolean>;
  endAdminSession: () => Promise<boolean>;

  // History and audit
  loadTokenGrantHistory: (options?: any) => Promise<void>;
  loadAdminSessions: () => Promise<void>;
  loadAdminActions: () => Promise<void>;
  exportAdminData: (dataType: 'token_grants' | 'admin_actions' | 'admin_sessions', fromDate?: string, toDate?: string) => Promise<any[]>;

  // Utilities
  clearSearch: () => void;
  refreshData: () => Promise<void>;
}

export const useAdminOperations = (): UseAdminOperationsReturn => {  const { user, isAdmin } = useAuth();  const { toast } = useToast();  // State  const [isLoadingOperation, setIsLoadingOperation] = useState(false);
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [tokenGrantHistory, setTokenGrantHistory] = useState<AdminTokenGrant[]>([]);
  const [adminSessions, setAdminSessions] = useState<AdminSession[]>([]);
  const [adminActions, setAdminActions] = useState<AdminAction[]>([]);
    const [currentAdminSession, setCurrentAdminSession] = useState<string | null>(null);  // Check admin privileges before operations  const checkAdminAccess = useCallback(() => {    if (!user?.id || !isAdmin()) {      toast({        title: 'Access Denied',        description: 'Admin privileges required for this operation.',        variant: 'destructive',      });      return false;    }    return true;  }, [user?.id, isAdmin, toast]);

  // Load token grant history
  const loadTokenGrantHistory = useCallback(async (options: any = {}) => {
    if (!checkAdminAccess()) return;

    try {
      const history = await adminService.getTokenGrantHistory(user!.id, options);
      setTokenGrantHistory(history);
    } catch (error) {
      console.error('Error loading token grant history:', error);
      toast({
        title: 'Load Failed',
        description: 'Failed to load token grant history',
        variant: 'destructive',
      });
    }
  }, [checkAdminAccess, user, toast]);

  // Search users
  const searchUsers = useCallback(async (
    searchTerm: string,
    includeTokenBalance: boolean = true
  ): Promise<UserSearchResult[]> => {
    if (!checkAdminAccess()) return [];

    setIsLoadingOperation(true);
    try {
      const results = await adminService.searchUsers(searchTerm, 20, includeTokenBalance);
      setSearchResults(results);
      
      // Log the search action
      await adminService.logAdminAction(
        user!.id,
        'user_search',
        undefined,
        { search_term: searchTerm, results_count: results.length },
        currentAdminSession || undefined
      );

      return results;
    } catch (error) {
      console.error('Error searching users:', error);
      toast({
        title: 'Search Failed',
        description: 'Failed to search users. Please try again.',
        variant: 'destructive',
      });
      return [];
    } finally {
      setIsLoadingOperation(false);
    }
  }, [checkAdminAccess, user, currentAdminSession, toast]);

  // Grant tokens to a user
  const grantTokens = useCallback(async (
    targetUserId: string,
    amount: number,
    reason: string
  ): Promise<TokenGrantResult> => {
    if (!checkAdminAccess()) {
      return { success: false, errorMessage: 'Admin access required' };
    }

    setIsLoadingOperation(true);
    try {
      const result = await adminService.grantTokens(
        user!.id,
        targetUserId,
        amount,
        reason,
        'manual',
        undefined,
        currentAdminSession || undefined
      );

      if (result.success) {
        toast({
          title: 'Tokens Granted',
          description: `Successfully granted ${amount} tokens. New balance: ${result.newBalance}`,
        });
        
        // Refresh data
        await loadTokenGrantHistory();
      } else {
        toast({
          title: 'Grant Failed',
          description: result.errorMessage || 'Failed to grant tokens',
          variant: 'destructive',
        });
      }

      return result;
    } catch (error) {
      console.error('Error granting tokens:', error);
      const errorMessage = 'Failed to grant tokens. Please try again.';
      toast({
        title: 'Grant Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      return { success: false, errorMessage };
    } finally {
      setIsLoadingOperation(false);
    }
  }, [checkAdminAccess, user, currentAdminSession, toast, loadTokenGrantHistory]);

  // Bulk grant tokens
  const bulkGrantTokens = useCallback(async (
    targetUserIds: string[],
    amount: number,
    reason: string
  ): Promise<BulkGrantResult> => {
    if (!checkAdminAccess()) {
      throw new Error('Admin access required');
    }

    setIsLoadingOperation(true);
    try {
      const result = await adminService.bulkGrantTokens(
        user!.id,
        targetUserIds,
        amount,
        reason,
        currentAdminSession || undefined
      );

      toast({
        title: 'Bulk Grant Complete',
        description: `${result.successfulGrants}/${result.totalGranted} grants successful${result.failedGrants > 0 ? `, ${result.failedGrants} failed` : ''}`,
        variant: result.failedGrants > 0 ? 'destructive' : 'default',
      });

      // Refresh data
      await loadTokenGrantHistory();

      return result;
    } catch (error) {
      console.error('Error bulk granting tokens:', error);
      toast({
        title: 'Bulk Grant Failed',
        description: 'Failed to perform bulk token grant. Please try again.',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoadingOperation(false);
    }
  }, [checkAdminAccess, user, currentAdminSession, toast, loadTokenGrantHistory]);

  // Get user token balance
  const getUserTokenBalance = useCallback(async (userId: string): Promise<number | null> => {
    if (!checkAdminAccess()) return null;

    try {
      return await adminService.getUserTokenBalance(userId);
    } catch (error) {
      console.error('Error getting user token balance:', error);
      return null;
    }
  }, [checkAdminAccess]);

  // Start admin session
  const startAdminSession = useCallback(async (): Promise<boolean> => {
    if (!checkAdminAccess()) return false;

    try {
      const result = await adminService.startAdminSession(
        user!.id,
        undefined, // IP will be detected server-side if needed
        navigator.userAgent
      );

      if (result.success && result.sessionId) {
        setCurrentAdminSession(result.sessionId);
        toast({
          title: 'Admin Session Started',
          description: 'Your admin session is now active.',
        });
        return true;
      } else {
        toast({
          title: 'Session Failed',
          description: result.error || 'Failed to start admin session',
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('Error starting admin session:', error);
      toast({
        title: 'Session Failed',
        description: 'Failed to start admin session',
        variant: 'destructive',
      });
      return false;
    }
  }, [checkAdminAccess, user, toast]);

  // End admin session
  const endAdminSession = useCallback(async (): Promise<boolean> => {
    if (!currentAdminSession) return true;

    try {
      const success = await adminService.endAdminSession(currentAdminSession);
      if (success) {
        setCurrentAdminSession(null);
        toast({
          title: 'Session Ended',
          description: 'Admin session has been ended.',
        });
      }
      return success;
    } catch (error) {
      console.error('Error ending admin session:', error);
      return false;
    }
  }, [currentAdminSession, toast]);

  // Load admin sessions
  const loadAdminSessions = useCallback(async () => {
    if (!checkAdminAccess()) return;

    try {
      const sessions = await adminService.getAdminSessions(user!.id);
      setAdminSessions(sessions);
    } catch (error) {
      console.error('Error loading admin sessions:', error);
      toast({
        title: 'Load Failed',
        description: 'Failed to load admin sessions',
        variant: 'destructive',
      });
    }
  }, [checkAdminAccess, user, toast]);

  // Load admin actions
  const loadAdminActions = useCallback(async () => {
    if (!checkAdminAccess()) return;

    try {
      const actions = await adminService.getAdminActions(user!.id);
      setAdminActions(actions);
    } catch (error) {
      console.error('Error loading admin actions:', error);
      toast({
        title: 'Load Failed',
        description: 'Failed to load admin actions',
        variant: 'destructive',
      });
    }
  }, [checkAdminAccess, user, toast]);

  // Export admin data
  const exportAdminData = useCallback(async (
    dataType: 'token_grants' | 'admin_actions' | 'admin_sessions',
    fromDate?: string,
    toDate?: string
  ): Promise<any[]> => {
    if (!checkAdminAccess()) return [];

    try {
      const data = await adminService.exportAdminData(user!.id, dataType, fromDate, toDate);
      toast({
        title: 'Export Complete',
        description: `Exported ${data.length} records`,
      });
      return data;
    } catch (error) {
      console.error('Error exporting admin data:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export admin data',
        variant: 'destructive',
      });
      return [];
    }
  }, [checkAdminAccess, user, toast]);

  // Clear search results
  const clearSearch = useCallback(() => {
    setSearchResults([]);
  }, []);

  // Refresh all data
  const refreshData = useCallback(async () => {
    if (!checkAdminAccess()) return;

    await Promise.all([
      loadTokenGrantHistory(),
      loadAdminSessions(),
      loadAdminActions(),
    ]);
  }, [checkAdminAccess, loadTokenGrantHistory, loadAdminSessions, loadAdminActions]);

    // Auto-start admin session when hook is used  useEffect(() => {    if (user?.id && isAdmin() && !currentAdminSession) {      startAdminSession();    }  }, [user?.id, isAdmin, currentAdminSession, startAdminSession]);

  // Auto-end session when user logs out or loses admin privileges
  useEffect(() => {
    return () => {
      if (currentAdminSession) {
        endAdminSession();
      }
    };
  }, []);

  return {
    // State
    isLoadingOperation,
    searchResults,
    tokenGrantHistory,
    adminSessions,
    adminActions,
    currentAdminSession,

    // Operations
    searchUsers,
    grantTokens,
    bulkGrantTokens,
    getUserTokenBalance,

    // Session management
    startAdminSession,
    endAdminSession,

    // History and audit
    loadTokenGrantHistory,
    loadAdminSessions,
    loadAdminActions,
    exportAdminData,

    // Utilities
    clearSearch,
    refreshData,
  };
}; 