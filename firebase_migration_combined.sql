-- Combined Firebase Auth Compatibility Migrations
-- Run this in Supabase SQL Editor to fix the notifications error

-- Part 1: Notifications Table
DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications (mark as read/unread)" ON public.notifications;
DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
DROP INDEX IF EXISTS idx_notifications_user_id_created_at;

ALTER TABLE public.notifications 
  ALTER COLUMN user_id TYPE TEXT,
  ALTER COLUMN actor_id TYPE TEXT;

ALTER TABLE public.notifications 
  DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
  DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey;

CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);

CREATE POLICY "Service role can manage all notifications"
ON public.notifications FOR ALL 
USING (true)
WITH CHECK (true);

-- Part 2: User Profiles Table  
ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;
ALTER TABLE public.user_profiles ALTER COLUMN id TYPE TEXT;

DROP POLICY IF EXISTS "Users can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

CREATE POLICY "Service role can manage all profiles"
ON public.user_profiles FOR ALL 
USING (true)
WITH CHECK (true);

-- Part 3: Stories and Contributions Tables
ALTER TABLE public.stories ALTER COLUMN author_id TYPE TEXT;
ALTER TABLE public.contributions ALTER COLUMN user_id TYPE TEXT;

DROP POLICY IF EXISTS "Stories are viewable by everyone" ON public.stories;
DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;

CREATE POLICY "Service role can manage all stories"
ON public.stories FOR ALL 
USING (true)
WITH CHECK (true);

DROP POLICY IF EXISTS "Contributions are viewable by everyone" ON public.contributions;
DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;

CREATE POLICY "Service role can manage all contributions"
ON public.contributions FOR ALL 
USING (true)
WITH CHECK (true);

-- Record that we've applied these migrations
INSERT INTO supabase_migrations.schema_migrations (version, name, statements) 
VALUES 
  ('20240116_firebase_auth', '0016_firebase_auth_compatibility', ARRAY['Firebase auth compatibility']),
  ('20240117_user_profiles', '0017_user_profiles_firebase_compatibility', ARRAY['User profiles Firebase compatibility']),
  ('20240118_stories_contrib', '0018_stories_contributions_firebase_compatibility', ARRAY['Stories and contributions Firebase compatibility'])
ON CONFLICT (version) DO NOTHING; 