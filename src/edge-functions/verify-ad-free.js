// This would be deployed as a Supabase Edge Function
// File: supabase/functions/verify-ad-free/index.js

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// Helper logging function for enhanced debugging
const logStep = (step, details) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : "";
  console.log(`[VERIFY-AD-FREE] ${step}${detailsStr}`);
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");
    logStep("Stripe key verified");

    // Create a Supabase client with the service role key for admin operations
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    );

    // Authenticate the user from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } =
      await supabaseClient.auth.getUser(token);
    if (userError)
      throw new Error(`Authentication error: ${userError.message}`);

    const user = userData.user;
    if (!user?.id) throw new Error("User ID not available");
    logStep("User authenticated", { userId: user.id });

    // Get the session ID from the request
    const { sessionId } = await req.json();
    if (!sessionId) throw new Error("No session ID provided");
    logStep("Session ID received", { sessionId });

    // Initialize Stripe
    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });

    // Check the session status
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    logStep("Session retrieved", {
      status: session.payment_status,
      customerId: session.customer,
      metadata: session.metadata,
    });

    if (session.payment_status !== "paid") {
      return new Response(
        JSON.stringify({ success: false, message: "Payment not completed" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }

    // Verify that this session was for ad-free feature
    if (session.metadata?.feature !== "ad-free") {
      return new Response(
        JSON.stringify({
          success: false,
          message: "This was not an ad-free purchase",
        }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }

    // Verify user ID matches session metadata
    if (session.metadata?.userId !== user.id) {
      logStep("User ID mismatch", {
        sessionUserId: session.metadata?.userId,
        currentUserId: user.id,
      });
      return new Response(
        JSON.stringify({ success: false, message: "User mismatch" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" } },
      );
    }

    // Update user preferences to mark them as ad-free
    const { error: updateError } = await supabaseClient
      .from("user_preferences")
      .upsert(
        {
          user_id: user.id,
          ad_free: true,
          updated_at: new Date().toISOString(),
        },
        { onConflict: "user_id" },
      );

    if (updateError) {
      throw new Error(
        `Failed to update user preferences: ${updateError.message}`,
      );
    }

    logStep("User marked as ad-free", { userId: user.id });

    return new Response(
      JSON.stringify({
        success: true,
        message: "Ad-free status verified and activated",
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } },
    );
  } catch (error) {
    logStep("ERROR", { message: error.message });
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      },
    );
  }
});
