// CommonJS build script to work around Rollup issues
const { execSync } = require("child_process");

// Set environment variable to skip Rollup native modules
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = "true";

try {
  console.log("Starting development server...");
  execSync("npx vite", {
    stdio: "inherit",
    env: {
      ...process.env,
      ROLLUP_SKIP_NODEJS_BUNDLE: "true",
    },
  });
} catch (error) {
  console.error("Error starting development server:", error);
  process.exit(1);
}
