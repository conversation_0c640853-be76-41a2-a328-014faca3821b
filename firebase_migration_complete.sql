-- Firebase Auth Compatibility Migration - Complete Version
-- This version drops ALL policies on the affected tables

DO $$
DECLARE
    r RECORD;
BEGIN
    -- Step 1: Drop ALL policies on affected tables (using dynamic SQL to catch all)
    FOR r IN 
        SELECT policyname, tablename 
        FROM pg_policies 
        WHERE tablename IN ('notifications', 'user_profiles', 'stories', 'contributions')
        AND schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', r.policyname, r.tablename);
        RAISE NOTICE 'Dropped policy % on table %', r.policyname, r.tablename;
    END LOOP;
    
    -- Step 2: Drop triggers and functions
    DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
    DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
    
    -- Step 3: Drop all foreign key constraints
    ALTER TABLE public.notifications 
        DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_story_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_contribution_id_fkey;
    
    ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
    ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;
    
    -- Step 4: Drop indexes
    DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
    DROP INDEX IF EXISTS idx_notifications_user_id_created_at;
    
    -- Step 5: NOW we can safely alter column types
    ALTER TABLE public.notifications 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT,
        ALTER COLUMN actor_id TYPE TEXT USING actor_id::TEXT;
    
    ALTER TABLE public.user_profiles 
        ALTER COLUMN id TYPE TEXT USING id::TEXT;
    
    ALTER TABLE public.stories 
        ALTER COLUMN author_id TYPE TEXT USING author_id::TEXT;
    
    ALTER TABLE public.contributions 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;
    
    -- Step 6: Recreate indexes
    CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
    CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);
    
    -- Step 7: Create new service role policies
    CREATE POLICY "Service role can manage all notifications"
    ON public.notifications FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all profiles"
    ON public.user_profiles FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all stories"
    ON public.stories FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all contributions"
    ON public.contributions FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    RAISE NOTICE 'Firebase Auth compatibility migration completed successfully!';
END $$; 