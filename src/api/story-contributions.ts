import { NextFunction, Request, Response } from "express";
import CachedDatabaseAdapter from "../lib/cachedDbAdapter";
import { ContributionMode, RealTimeService, RealTimeEventType, RealTimeEvent } from "@/services/realTimeService";
import { StoryStatus } from "@prisma/client";

// Get the RealTimeService instance
const realTimeService = RealTimeService.getInstance();

/**
 * Create a new story
 */
export const createStory = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { title, description, contributionMode, wordsPerContribution, userId, username } = req.body;
    
    if (!title || !userId) {
      return res.status(400).json({ error: "Title and userId are required" });
    }
    
    const storyData = {
      title,
      description,
      created_by: userId,
      contribution_mode: contributionMode || ContributionMode.WORD,
      words_per_contribution: wordsPerContribution,
      participants: [userId],
      current_turn: userId, // Initially, creator's turn
    };
    
    const result = await CachedDatabaseAdapter.createStory(storyData);
    
    if (result.error) {
      return res.status(500).json({ error: result.error.message });
    }
    
    // Register the creator as the first participant in the real-time service
    const storyId = result.data.id;
    realTimeService.registerUserSession(storyId, userId, username);
    
    res.status(201).json(result.data);
  } catch (error) {
    next(error);
  }
};

/**
 * Get story state with all contributions
 */
export const getStoryState = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    
    if (!storyId) {
      return res.status(400).json({ error: "Story ID is required" });
    }
    
    const result = await CachedDatabaseAdapter.getStoryWithContributions(storyId);
    
    if (result.error) {
      return res.status(404).json({ error: result.error.message });
    }
    
    // Get active user sessions from real-time service
    try {
      const activeSessions = await realTimeService.getActiveUserSessions(storyId);
      // Add active sessions to the response
      result.data.activeSessions = activeSessions;
    } catch (sessionError) {
      console.error("Error getting active sessions:", sessionError);
      // Continue even if we can't get active sessions
    }
    
    res.status(200).json(result.data);
  } catch (error) {
    next(error);
  }
};

/**
 * Submit a new contribution to a story
 */
export const submitContribution = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    const { content, userId, type, username } = req.body;
    
    if (!storyId || !content || !userId) {
      return res.status(400).json({ error: "Story ID, content, and userId are required" });
    }
    
    // Get story to check if it's user's turn and determine position
    const storyResult = await CachedDatabaseAdapter.getStory(storyId);
    
    if (storyResult.error) {
      return res.status(404).json({ error: "Story not found" });
    }
    
    const story = storyResult.data;
    
    // Check if it's the user's turn
    if (!story || story.current_turn !== userId) {
      return res.status(403).json({ error: "It's not your turn to contribute or story data is missing" });
    }
    
    // Get the latest contribution to determine position
    const latestContributionResult = await CachedDatabaseAdapter.getLatestContribution(storyId);

    if (latestContributionResult.error && latestContributionResult.error.code !== 'PGRST116') {
      console.error('Error fetching latest contribution:', latestContributionResult.error);
    }

    const latestContributionData = latestContributionResult.data;
    const position = latestContributionData && typeof latestContributionData.position === 'number' ? latestContributionData.position + 1 : 0;
    
    // Create the contribution
    const contributionData = {
      story_id: storyId,
      user_id: userId,
      content,
      position,
      type: type || story.contribution_mode,
    };
    
    const result = await CachedDatabaseAdapter.createContribution(contributionData);
    
    if (result.error) {
      return res.status(500).json({ error: result.error.message });
    }
    
    // Update story with new word count and next turn
    const wordCount = content.trim().split(/\s+/).length;
    const participants = story.participants;
    
    // Find next participant's turn
    if (!Array.isArray(participants) || participants.length === 0) {
      console.error('Participants array is missing or empty for story:', storyId);
    }

    let nextTurn = story.current_turn;
    if (Array.isArray(participants) && participants.length > 0) {
        const currentIndex = participants.findIndex(p => p.id === userId);
        if (currentIndex !== -1) {
            const nextIndex = (currentIndex + 1) % participants.length;
            nextTurn = participants[nextIndex].id;
        } else {
            console.error('Current contributor not found in participants list:', userId, storyId);
        }
    }
    
    // Ensure story.word_count is a number before adding to it
    const currentWordCount = typeof story.word_count === 'number' ? story.word_count : 0;

    await CachedDatabaseAdapter.updateStory(storyId, {
      word_count: currentWordCount + wordCount,
      current_turn: nextTurn,
      updated_at: new Date(),
    });
    
    // Broadcast the contribution to all connected clients via real-time service
    realTimeService.broadcastContribution(storyId, {
      contributionId: result.data.id,
      content,
      userId,
      username,
      position,
      type: type || story.contribution_mode,
      nextTurn,
      wordCount: currentWordCount + wordCount,
    });
    
    // Return the contribution with updated story info
    res.status(201).json({
      contribution: result.data,
      nextTurn,
      wordCount: currentWordCount + wordCount,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Join an existing story as a participant
 */
export const joinStory = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    const { userId, username } = req.body;
    
    if (!storyId || !userId) {
      return res.status(400).json({ error: "Story ID and userId are required" });
    }
    
    const result = await CachedDatabaseAdapter.addParticipantToStory(storyId, userId);
    
    if (result.error) {
      return res.status(500).json({ error: result.error.message });
    }
    
    // Register user session in real-time service
    realTimeService.registerUserSession(storyId, userId, username);
    
    res.status(200).json(result.data);
  } catch (error) {
    next(error);
  }
};

/**
 * Update user session status (typing, online)
 */
export const updateUserSession = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    const { userId, isTyping, isOnline } = req.body;
    
    if (!storyId || !userId) {
      return res.status(400).json({ error: "Story ID and userId are required" });
    }
    
    const sessionData = {
      user_id: userId,
      story_id: storyId,
      is_typing: isTyping !== undefined ? isTyping : undefined,
      is_online: isOnline !== undefined ? isOnline : undefined,
      last_active: new Date(),
    };
    
    // Update session in database
    const result = await CachedDatabaseAdapter.updateUserSession(sessionData);
    
    if (result.error) {
      return res.status(500).json({ error: result.error.message });
    }
    
    // Update real-time status
    realTimeService.updateUserStatus(storyId, userId, isTyping, isOnline);
    
    res.status(200).json(result.data);
  } catch (error) {
    next(error);
  }
};

/**
 * Get all active user sessions for a story
 */
export const getStoryUserSessions = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    
    if (!storyId) {
      return res.status(400).json({ error: "Story ID is required" });
    }
    
    // Get sessions from database
    const dbResult = await CachedDatabaseAdapter.getStoryUserSessions(storyId);
    
    if (dbResult.error) {
      return res.status(500).json({ error: dbResult.error.message });
    }
    
    // Get real-time active sessions
    try {
      const realtimeSessions = await realTimeService.getActiveUserSessions(storyId);
      
      // Merge database sessions with real-time status
      const sessions = dbResult.data.map(dbSession => {
        const realtimeSession = realtimeSessions.find(rs => rs.userId === dbSession.user_id);
        return {
          ...dbSession,
          is_typing: realtimeSession?.isTyping || false,
          is_online: realtimeSession?.isOnline || false,
        };
      });
      
      res.status(200).json(sessions);
    } catch (realtimeError) {
      console.error("Error getting real-time sessions:", realtimeError);
      // Fall back to database sessions if real-time fails
      res.status(200).json(dbResult.data);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Mark a story as complete
 */
export const completeStory = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    const { userId } = req.body;
    
    if (!storyId || !userId) {
      return res.status(400).json({ error: "Story ID and user ID are required" });
    }
    
    // Get the story to verify ownership
    const storyResult = await CachedDatabaseAdapter.getStory(storyId);
    
    if (storyResult.error) {
      return res.status(404).json({ error: "Story not found" });
    }
    
    const story = storyResult.data;
    
    // Only the story creator can complete the story
    if (story.created_by !== userId) {
      return res.status(403).json({ error: "Only the story creator can complete the story" });
    }
    
    // Update the story status to COMPLETED
    const updateResult = await CachedDatabaseAdapter.updateStory(storyId, {
      status: StoryStatus.COMPLETED,
    });
    
    if (updateResult.error) {
      return res.status(500).json({ error: updateResult.error.message });
    }
    
    // Broadcast the story completion event
    const event: RealTimeEvent = {
      type: RealTimeEventType.STORY_COMPLETED,
      timestamp: Date.now(),
      storyId,
      data: {
        completedAt: new Date().toISOString(),
        completedBy: userId,
      },
    };
    
    // realTimeService.broadcastEvent(storyId, event); // Commented out: RealTimeService not implemented / method is private
    
    res.status(200).json({ success: true, story: updateResult.data });
  } catch (error) {
    next(error);
  }
};

/**
 * Archive a story
 */
export const archiveStory = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { storyId } = req.params;
    const { userId } = req.body;
    
    if (!storyId || !userId) {
      return res.status(400).json({ error: "Story ID and user ID are required" });
    }
    
    // Get the story to verify ownership
    const storyResult = await CachedDatabaseAdapter.getStory(storyId);
    
    if (storyResult.error) {
      return res.status(404).json({ error: "Story not found" });
    }
    
    const story = storyResult.data;
    
    // Only the story creator can archive the story
    if (story.created_by !== userId) {
      return res.status(403).json({ error: "Only the story creator can archive the story" });
    }
    
    // Update the story status to ARCHIVED
    const updateResult = await CachedDatabaseAdapter.updateStory(storyId, {
      status: StoryStatus.ARCHIVED,
    });
    
    if (updateResult.error) {
      return res.status(500).json({ error: updateResult.error.message });
    }
    
    // Broadcast the story archival event
    const event: RealTimeEvent = {
      type: RealTimeEventType.STORY_ARCHIVED,
      timestamp: Date.now(),
      storyId,
      data: {
        archivedAt: new Date().toISOString(),
        archivedBy: userId,
      },
    };
    
    // realTimeService.broadcastEvent(storyId, event); // Commented out: RealTimeService not implemented / method is private
    
    // Clean up real-time sessions for this story
    realTimeService.cleanupStorySessions(storyId);
    
    res.status(200).json({ success: true, story: updateResult.data });
  } catch (error) {
    next(error);
  }
};

/**
 * Subscribe to real-time updates for a story
 * This endpoint will be used by the client to establish a WebSocket connection
 */
export const subscribeToStoryUpdates = (
  req: Request,
  res: Response
) => {
  const { storyId } = req.params;
  
  if (!storyId) {
    return res.status(400).json({ error: "Story ID is required" });
  }
  
  // Set headers for SSE (Server-Sent Events)
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  // Function to send events to the client
  const sendEvent = (event: any) => {
    res.write(`data: ${JSON.stringify(event)}\n\n`);
  };
  
  // Subscribe to real-time updates
  const unsubscribe = realTimeService.subscribeToStory(storyId, sendEvent);
  
  // Handle client disconnect
  req.on('close', () => {
    unsubscribe();
  });
};
