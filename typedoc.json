{"entryPoints": ["src/components/ui"], "entryPointStrategy": "expand", "out": "docs/generated", "name": "Word by Word Story - UI Components", "theme": "default", "includeVersion": true, "excludeExternals": true, "excludeNotDocumented": false, "excludePrivate": true, "excludeProtected": true, "excludeInternal": true, "searchInComments": true, "treatWarningsAsErrors": false, "skipErrorChecking": true, "plugin": ["typedoc-plugin-markdown"], "readme": "README.md", "hideGenerator": false, "json": "docs/generated/documentation.json", "pretty": true, "exclude": ["**/node_modules/**", "**/dist/**", "**/build/**", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/cypress/**", "**/*.stories.tsx"]}