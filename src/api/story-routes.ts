import express from "express";
import {
  createStory,
  getStoryState,
  submitContribution,
  joinStory,
  updateUserSession,
  getStoryUserSessions,
  subscribeToStoryUpdates,
  completeStory,
  archiveStory,
} from "./story-contributions";

const router = express.Router();

// Story routes
router.post("/stories", createStory);
router.get("/stories/:storyId/contributions", getStoryState);
router.post("/stories/:storyId/contributions", submitContribution);
router.post("/stories/:storyId/participants", joinStory);
router.post("/stories/:storyId/sessions", updateUserSession);
router.get("/stories/:storyId/sessions", getStoryUserSessions);

// Story management routes
router.post("/stories/:storyId/complete", completeStory);
router.post("/stories/:storyId/archive", archiveStory);

// Real-time updates route (Server-Sent Events)
router.get("/stories/:storyId/subscribe", subscribeToStoryUpdates);

export default router;
