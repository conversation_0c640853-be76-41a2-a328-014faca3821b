export const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://word-by-word-story.vercel.app', // Replace with your actual production domain
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
};

// For development, you may want to also allow localhost
// const allowedOrigins = ['https://word-by-word-story.vercel.app', 'http://localhost:5173'];
// const origin = req.headers.get("Origin") || "";
// if (allowedOrigins.includes(origin)) {
//   corsHeaders['Access-Control-Allow-Origin'] = origin;
// }
