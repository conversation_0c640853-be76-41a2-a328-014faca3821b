import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import { toast } from "@/hooks/use-toast";
import {
  UserPlus,
  MoreHorizontal,
  Search,
  ChevronDown,
  CheckCircle,
  XCircle,
  Shield,
  User,
  Crown,
  Mail,
  Calendar,
  Award,
} from "lucide-react";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { BadgeList, BadgeTier } from "@/components/ui/achievement-badge";

interface UserType {
  id: string;
  username: string;
  email: string;
  profilePicture: string;
  createdAt: string;
  tier: "free" | "wordsmith" | "storyteller" | "authors-guild";
  role: "user" | "admin";
  status: string;
  storiesCreated: number;
  lastLogin: string;
  credits?: number;
  achievements?: {
    storiesCreated: number;
    storiesParticipated: number;
    wordsContributed: number;
    votesReceived: number;
    badges: BadgeTier[];
  };
}

const mockUsers: UserType[] = [
  {
    id: "1",
    username: "pat",
    email: "<EMAIL>",
    profilePicture: "/placeholder.svg",
    createdAt: "2023-01-15",
    tier: "authors-guild",
    role: "admin",
    status: "active",
    storiesCreated: 15,
    lastLogin: "2023-05-10",
    credits: 50,
    achievements: {
      storiesCreated: 25,
      storiesParticipated: 30,
      wordsContributed: 1200,
      votesReceived: 150,
      badges: ["wordsmith", "storyteller", "authors-guild", "master"],
    },
  },
  {
    id: "2",
    username: "johndoe",
    email: "<EMAIL>",
    profilePicture: "/placeholder.svg",
    createdAt: "2023-02-20",
    tier: "free",
    role: "user",
    status: "active",
    storiesCreated: 5,
    lastLogin: "2023-05-08",
    credits: 10,
    achievements: {
      storiesCreated: 5,
      storiesParticipated: 7,
      wordsContributed: 120,
      votesReceived: 15,
      badges: ["novice", "wordsmith"],
    },
  },
  {
    id: "3",
    username: "janedoe",
    email: "<EMAIL>",
    profilePicture: "/placeholder.svg",
    createdAt: "2023-03-10",
    tier: "wordsmith",
    role: "user",
    status: "inactive",
    storiesCreated: 3,
    lastLogin: "2023-04-15",
    credits: 5,
    achievements: {
      storiesCreated: 3,
      storiesParticipated: 4,
      wordsContributed: 80,
      votesReceived: 8,
      badges: ["novice"],
    },
  },
  {
    id: "4",
    username: "admin2",
    email: "<EMAIL>",
    profilePicture: "/placeholder.svg",
    createdAt: "2023-01-05",
    tier: "authors-guild",
    role: "admin",
    status: "active",
    storiesCreated: 12,
    lastLogin: "2023-05-09",
    credits: 25,
    achievements: {
      storiesCreated: 12,
      storiesParticipated: 15,
      wordsContributed: 500,
      votesReceived: 60,
      badges: ["novice", "wordsmith", "storyteller"],
    },
  },
];

const userFormSchema = z.object({
  username: z.string().min(3, {
    message: "Username must be at least 3 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  role: z.enum(["user", "admin"]),
  tier: z.enum(["free", "wordsmith", "storyteller", "authors-guild"]),
  credits: z.number().min(0).optional(),
  storiesCreated: z.number().min(0).optional(),
  storiesParticipated: z.number().min(0).optional(),
  wordsContributed: z.number().min(0).optional(),
  votesReceived: z.number().min(0).optional(),
});

type UserFormData = z.infer<typeof userFormSchema>;

export const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserType[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState("all");
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<UserType | null>(null);
  const [isManageBadgesOpen, setIsManageBadgesOpen] = useState(false);

  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      username: "",
      email: "",
      role: "user",
      tier: "free",
      credits: 10,
      storiesCreated: 0,
      storiesParticipated: 0,
      wordsContributed: 0,
      votesReceived: 0,
    },
  });

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchTerm === "" ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterRole === "all" || user.role === filterRole;

    return matchesSearch && matchesRole;
  });

  const onSubmit = (values: UserFormData) => {
    if (editingUser) {
      const updatedUser = { ...editingUser, ...values };

      if (updatedUser.achievements) {
        if (values.storiesCreated !== undefined) {
          updatedUser.achievements.storiesCreated = values.storiesCreated;
        }
        if (values.storiesParticipated !== undefined) {
          updatedUser.achievements.storiesParticipated =
            values.storiesParticipated;
        }
        if (values.wordsContributed !== undefined) {
          updatedUser.achievements.wordsContributed = values.wordsContributed;
        }
        if (values.votesReceived !== undefined) {
          updatedUser.achievements.votesReceived = values.votesReceived;
        }

        const badges: BadgeTier[] = [];

        if (updatedUser.achievements.storiesCreated >= 1) {
          badges.push("novice");
        }

        if (
          updatedUser.achievements.storiesCreated >= 5 ||
          updatedUser.achievements.wordsContributed >= 100
        ) {
          badges.push("wordsmith");
        }

        if (
          updatedUser.achievements.storiesCreated >= 10 ||
          updatedUser.achievements.wordsContributed >= 500
        ) {
          badges.push("storyteller");
        }

        if (
          updatedUser.achievements.storiesCreated >= 20 ||
          updatedUser.achievements.wordsContributed >= 1000
        ) {
          badges.push("authors-guild");
        }

        if (updatedUser.achievements.storiesParticipated >= 5) {
          badges.push("apprentice");
        }

        if (updatedUser.achievements.votesReceived >= 50) {
          badges.push("master");
        }

        updatedUser.achievements.badges = [...new Set(badges)];
      }

      const updatedUsers = users.map((user) =>
        user.id === editingUser.id ? updatedUser : user,
      );
      setUsers(updatedUsers);
      toast({
        title: "User updated",
        description: `${values.username} has been successfully updated.`,
      });
    } else {
      const newUserAchievements = {
        storiesCreated: values.storiesCreated || 0,
        storiesParticipated: values.storiesParticipated || 0,
        wordsContributed: values.wordsContributed || 0,
        votesReceived: values.votesReceived || 0,
        badges: [] as BadgeTier[],
      };

      if (newUserAchievements.storiesCreated >= 1) {
        newUserAchievements.badges.push("novice");
      }
      if (
        newUserAchievements.storiesCreated >= 5 ||
        newUserAchievements.wordsContributed >= 100
      ) {
        newUserAchievements.badges.push("wordsmith");
      }

      const newUser: UserType = {
        id: Math.random().toString(36).substring(2, 9),
        username: values.username,
        email: values.email,
        tier: values.tier,
        role: values.role,
        profilePicture: `/placeholder.svg`,
        createdAt: new Date().toISOString().split("T")[0],
        status: "active",
        storiesCreated: values.storiesCreated || 0,
        lastLogin: new Date().toISOString().split("T")[0],
        credits: values.credits || 10,
        achievements: newUserAchievements,
      };

      setUsers([...users, newUser]);
      toast({
        title: "User added",
        description: `${values.username} has been successfully added.`,
      });
    }

    setIsAddUserOpen(false);
    setEditingUser(null);
    form.reset();
  };

  const handleEditUser = (user: UserType) => {
    setEditingUser(user);
    form.reset({
      username: user.username,
      email: user.email,
      role: user.role,
      tier: user.tier,
      credits: user.credits || 0,
      storiesCreated: user.achievements?.storiesCreated || 0,
      storiesParticipated: user.achievements?.storiesParticipated || 0,
      wordsContributed: user.achievements?.wordsContributed || 0,
      votesReceived: user.achievements?.votesReceived || 0,
    });
    setIsAddUserOpen(true);
  };

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter((user) => user.id !== userId));
    toast({
      title: "User removed",
      description: "The user has been successfully removed.",
    });
  };

  const handleOpenChange = (open: boolean) => {
    setIsAddUserOpen(open);
    if (!open) {
      setEditingUser(null);
      form.reset();
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage users and permissions</CardDescription>
            </div>
            <Dialog open={isAddUserOpen} onOpenChange={handleOpenChange}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>
                    {editingUser ? "Edit User" : "Add New User"}
                  </DialogTitle>
                  <DialogDescription>
                    {editingUser
                      ? "Edit user details and permissions."
                      : "Add a new user to the system."}
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <Tabs defaultValue="basic">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="basic">Basic Info</TabsTrigger>
                        <TabsTrigger value="achievements">
                          Achievements
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="basic" className="space-y-4 pt-2">
                        <FormField
                          control={form.control}
                          name="username"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Username</FormLabel>
                              <FormControl>
                                <Input placeholder="johndoe" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="<EMAIL>"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="role"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Role</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select role" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="user">User</SelectItem>
                                  <SelectItem value="admin">Admin</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="tier"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Subscription Tier</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select tier" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="free">Free</SelectItem>
                                  <SelectItem value="wordsmith">
                                    Wordsmith
                                  </SelectItem>
                                  <SelectItem value="storyteller">
                                    Storyteller
                                  </SelectItem>
                                  <SelectItem value="authors-guild">
                                    Authors Guild
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="credits"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Credits</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      parseInt(e.target.value) || 0,
                                    )
                                  }
                                  value={field.value || 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TabsContent>

                      <TabsContent
                        value="achievements"
                        className="space-y-4 pt-2"
                      >
                        <FormField
                          control={form.control}
                          name="storiesCreated"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Stories Created</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      parseInt(e.target.value) || 0,
                                    )
                                  }
                                  value={field.value || 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="storiesParticipated"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Stories Participated</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      parseInt(e.target.value) || 0,
                                    )
                                  }
                                  value={field.value || 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="wordsContributed"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Words Contributed</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      parseInt(e.target.value) || 0,
                                    )
                                  }
                                  value={field.value || 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="votesReceived"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Votes Received</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      parseInt(e.target.value) || 0,
                                    )
                                  }
                                  value={field.value || 0}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {editingUser && editingUser.achievements && (
                          <div className="pt-2">
                            <p className="text-sm font-medium mb-2">
                              Current Badges
                            </p>
                            {editingUser.achievements.badges &&
                            editingUser.achievements.badges.length > 0 ? (
                              <BadgeList
                                badges={editingUser.achievements.badges}
                                size="md"
                                showLabels={true}
                              />
                            ) : (
                              <p className="text-sm text-muted-foreground">
                                No badges earned yet
                              </p>
                            )}
                            <p className="text-xs text-muted-foreground mt-2">
                              Badges will be automatically calculated based on
                              achievements
                            </p>
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>

                    <DialogFooter>
                      <Button type="submit">
                        {editingUser ? "Save Changes" : "Add User"}
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4 space-x-2">
            <div className="flex items-center relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-[300px]"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-auto">
                  Filter by Role
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by role</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setFilterRole("all")}>
                  All Roles
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterRole("admin")}>
                  <Shield className="mr-2 h-4 w-4" /> Admin
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setFilterRole("user")}>
                  <User className="mr-2 h-4 w-4" /> User
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Badges</TableHead>
                  <TableHead>Credits</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <img
                            src={user.profilePicture}
                            alt={user.username}
                            className="h-8 w-8 rounded-full"
                          />
                          <div>
                            <p className="font-medium">{user.username}</p>
                            <p className="text-sm text-muted-foreground flex items-center">
                              <Mail className="mr-1 h-3 w-3" /> {user.email}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {user.role === "admin" ? (
                            <Shield className="h-4 w-4 text-red-500" />
                          ) : (
                            <User className="h-4 w-4 text-gray-500" />
                          )}
                          <span className="capitalize">{user.role}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {user.status === "active" ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span>Active</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 text-gray-400" />
                              <span>Inactive</span>
                            </>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.achievements &&
                        user.achievements.badges &&
                        user.achievements.badges.length > 0 ? (
                          <BadgeList
                            badges={user.achievements.badges}
                            size="sm"
                          />
                        ) : (
                          <span className="text-xs text-gray-500">
                            No badges
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Crown className="h-4 w-4 text-literary-gold mr-1" />
                          <span>{user.credits || 0}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={() => handleEditUser(user)}
                            >
                              Edit User
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteUser(user.id)}
                              disabled={user.email === "<EMAIL>"}
                              className={
                                user.email === "<EMAIL>"
                                  ? "text-gray-400"
                                  : "text-red-500"
                              }
                            >
                              {user.email === "<EMAIL>"
                                ? "Cannot Delete Admin"
                                : "Delete"}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No users found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {filteredUsers.length} of {users.length} users
          </div>
        </CardFooter>
      </Card>
    </>
  );
};
