import { supabase } from './auth';
import { ApiKeyService, ApiTier } from './apiKeyService';
import { getSubscriptionStatus } from './subscription';

/**
 * Integration service that connects our API key system 
 * to the existing Stripe billing infrastructure
 */
export class StripeApiIntegration {
  
  // Map Stripe subscription status to API tier
  static mapSubscriptionToTier(subscriptionStatus: string | null, isAdFree?: boolean): ApiTier {
    if (!subscriptionStatus) {
      return ApiTier.FREE;
    }
    
    switch (subscriptionStatus) {
      case 'active':
      case 'trialing':
        return isAdFree ? ApiTier.PREMIUM : ApiTier.FREE;
      case 'past_due':
      case 'unpaid':
        // Allow grace period for premium users
        return ApiTier.PREMIUM;
      case 'canceled':
      case 'incomplete':
      case 'incomplete_expired':
      default:
        return ApiTier.FREE;
    }
  }
  
  // Update API key tier based on current subscription
  static async syncApiKeyWithSubscription(userId: string, apiKeyId: string): Promise<{
    success: boolean;
    newTier?: ApiTier;
    error?: string;
  }> {
    try {
      // Get current subscription status from existing Stripe integration
      const { isSubscribed, status } = await getSubscriptionStatus();
      
      // Get user profile to check ad-free status
      const { data: userProfile, error: profileError } = await supabase
        .from('user_profiles')
        .select('is_ad_free, tier')
        .eq('id', userId)
        .single();
      
      if (profileError) {
        console.error('❌ Error fetching user profile:', profileError);
        return { success: false, error: 'Failed to fetch user profile' };
      }
      
      // Determine the appropriate API tier
      const newTier = this.mapSubscriptionToTier(status, userProfile?.is_ad_free);
      
      // Update the API key tier in cache
      const apiKey = await ApiKeyService.validateApiKey(apiKeyId);
      if (apiKey.valid && apiKey.apiKey) {
        const updatedKey = {
          ...apiKey.apiKey,
          tier: newTier,
          billingStatus: isSubscribed ? 'active' as const : 'cancelled' as const,
          updatedAt: new Date()
        };
        
        // Update in cache
        await import('./cacheService').then(({ cacheService }) => 
          cacheService.set(`api_key:${apiKey.apiKey!.id}`, updatedKey, 3600)
        );
        
        console.log(`✅ Updated API key ${apiKeyId} tier to ${newTier} based on subscription status: ${status}`);
        
        return { success: true, newTier };
      }
      
      return { success: false, error: 'API key not found' };
      
    } catch (error) {
      console.error('❌ Error syncing API key with subscription:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
  
  // Sync all API keys for a user with their subscription
  static async syncAllUserApiKeys(userId: string): Promise<{
    success: boolean;
    updatedKeys: number;
    error?: string;
  }> {
    try {
      // TODO: Get all API keys for user from database
      // For now, this is a placeholder since we need database integration
      
      console.log(`🔄 Syncing all API keys for user ${userId}`);
      
      // This would be implemented once we have database storage
      // const userApiKeys = await DatabaseAdapter.getUserApiKeys(userId);
      // let updatedCount = 0;
      // 
      // for (const apiKey of userApiKeys) {
      //   const result = await this.syncApiKeyWithSubscription(userId, apiKey.id);
      //   if (result.success) updatedCount++;
      // }
      
      return { success: true, updatedKeys: 0 };
      
    } catch (error) {
      console.error('❌ Error syncing user API keys:', error);
      return { success: false, updatedKeys: 0, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
  
  // Handle Stripe subscription change webhook
  static async handleSubscriptionChange(
    stripeCustomerId: string,
    subscriptionStatus: string,
    subscriptionId: string
  ): Promise<void> {
    try {
      // Find user by Stripe customer ID using existing subscription table
      const { data: subscription, error: subError } = await supabase
        .from('subscriptions')
        .select('user_id')
        .eq('stripe_customer_id', stripeCustomerId)
        .single();
      
      if (subError || !subscription) {
        console.error('❌ User not found for Stripe customer:', stripeCustomerId);
        return;
      }
      
      // Update user's ad-free status based on subscription
      const isAdFree = subscriptionStatus === 'active' || subscriptionStatus === 'trialing';
      
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          is_ad_free: isAdFree,
          tier: isAdFree ? 'premium' : 'free'
        })
        .eq('id', subscription.user_id);
      
      if (profileError) {
        console.error('❌ Error updating user profile:', profileError);
      }
      
      // Sync all API keys for this user
      await this.syncAllUserApiKeys(subscription.user_id);
      
      console.log(`✅ Processed subscription change for user ${subscription.user_id}: ${subscriptionStatus}`);
      
    } catch (error) {
      console.error('❌ Error handling subscription change:', error);
    }
  }
  
  // Create API key with automatic tier assignment based on subscription
  static async createApiKeyWithSubscriptionTier(data: {
    userId: string;
    name: string;
    description?: string;
    allowedOrigins?: string[];
    allowedIPs?: string[];
    expiresAt?: Date;
  }): Promise<{ apiKey: any; plainKey: string } | null> {
    try {
      // Get current subscription status
      const { isSubscribed, status } = await getSubscriptionStatus();
      
      // Get user profile
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('is_ad_free, stripe_customer_id')
        .eq('id', data.userId)
        .single();
      
      // Determine tier based on subscription
      const tier = this.mapSubscriptionToTier(status, userProfile?.is_ad_free);
      
      // Get subscription ID for API key linking
      let subscriptionId: string | undefined;
      if (userProfile?.stripe_customer_id) {
        const { data: sub } = await supabase
          .from('subscriptions')
          .select('stripe_subscription_id')
          .eq('stripe_customer_id', userProfile.stripe_customer_id)
          .eq('status', 'active')
          .single();
        
        subscriptionId = sub?.stripe_subscription_id || undefined;
      }
      
      // Create API key with appropriate tier
      const result = await ApiKeyService.createApiKey({
        ...data,
        tier,
        subscriptionId
      });
      
      if (result) {
        console.log(`✅ Created API key for user ${data.userId} with ${tier} tier (subscription: ${status})`);
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ Error creating API key with subscription tier:', error);
      return null;
    }
  }
  
  // Get billing summary for API usage
  static async getBillingSummary(userId: string, month: number, year: number): Promise<{
    subscriptionFee: number;
    apiUsageCost: number;
    totalCost: number;
    requestCount: number;
    cacheHitRate: number;
    tier: ApiTier;
  } | null> {
    try {
      // Get subscription info
      const { isSubscribed, status } = await getSubscriptionStatus();
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('is_ad_free')
        .eq('id', userId)
        .single();
      
      const tier = this.mapSubscriptionToTier(status, userProfile?.is_ad_free);
      
      // Base subscription fee
      let subscriptionFee = 0;
      if (tier === ApiTier.PREMIUM) {
        subscriptionFee = 49.99; // $49.99/month
      }
      
      // TODO: Calculate API usage costs from stored usage data
      // This would aggregate usage records for the specified month/year
      const apiUsageCost = 0; // Placeholder
      const requestCount = 0; // Placeholder
      const cacheHitRate = 0; // Placeholder
      
      return {
        subscriptionFee,
        apiUsageCost,
        totalCost: subscriptionFee + apiUsageCost,
        requestCount,
        cacheHitRate,
        tier
      };
      
    } catch (error) {
      console.error('❌ Error generating billing summary:', error);
      return null;
    }
  }
  
  // Webhook endpoint for enhanced Stripe integration
  static async enhancedWebhookHandler(event: any): Promise<void> {
    try {
      switch (event.type) {
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
        case 'customer.subscription.created':
          const subscription = event.data.object;
          await this.handleSubscriptionChange(
            subscription.customer,
            subscription.status,
            subscription.id
          );
          break;
          
        case 'checkout.session.completed':
          const session = event.data.object;
          if (session.customer && session.subscription) {
            await this.handleSubscriptionChange(
              session.customer,
              'active', // New subscription is active
              session.subscription
            );
          }
          break;
          
        default:
          console.log(`ℹ️ Unhandled webhook event: ${event.type}`);
      }
    } catch (error) {
      console.error('❌ Error in enhanced webhook handler:', error);
    }
  }
}

export default StripeApiIntegration; 