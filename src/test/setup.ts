import '@testing-library/jest-dom';
import { beforeAll, vi } from 'vitest';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // Deprecated
    removeListener: vi.fn(), // Deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock window.location
Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    href: 'http://localhost:5173',
    origin: 'http://localhost:5173',
    protocol: 'http:',
    host: 'localhost:5173',
    hostname: 'localhost',
    port: '5173',
    pathname: '/',
    search: '',
    hash: '',
  },
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock fetch if not available
if (!global.fetch) {
  global.fetch = vi.fn();
}

// Mock PostHog for tests
vi.mock('../lib/posthog', () => ({
  posthog: {
    capture: vi.fn(),
    identify: vi.fn(),
    reset: vi.fn(),
    isFeatureEnabled: vi.fn().mockReturnValue(false),
    getFeatureFlag: vi.fn().mockReturnValue(false),
  },
  analytics: {
    track: vi.fn(),
    identify: vi.fn(),
    reset: vi.fn(),
    setUserProperties: vi.fn(),
    pageView: vi.fn(),
    isFeatureEnabled: vi.fn().mockReturnValue(false),
    getFeatureFlag: vi.fn().mockReturnValue(false),
  },
  trackStoryEvent: {
    storyCreated: vi.fn(),
    storyContribution: vi.fn(),
    storyCompleted: vi.fn(),
    storyViewed: vi.fn(),
    storyVoted: vi.fn(),
  },
  trackUserEvent: {
    userRegistered: vi.fn(),
    userLoggedIn: vi.fn(),
    userLoggedOut: vi.fn(),
  },
  trackSubscriptionEvent: {
    subscriptionStarted: vi.fn(),
    subscriptionCancelled: vi.fn(),
    paymentSuccessful: vi.fn(),
    paymentFailed: vi.fn(),
  },
  trackEngagementEvent: {
    featureUsed: vi.fn(),
    timeSpent: vi.fn(),
    searchPerformed: vi.fn(),
    errorEncountered: vi.fn(),
  },
}));

// Supabase mock
vi.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null }),
      getSession: vi.fn().mockResolvedValue({ 
        data: { session: null }, 
        error: null 
      }),
      signUp: vi.fn().mockResolvedValue({
        data: { user: null, session: null },
        error: null
      }),
      signInWithPassword: vi.fn().mockResolvedValue({
        data: { user: null, session: null },
        error: null
      }),
      signOut: vi.fn().mockResolvedValue({
        error: null
      }),
      resetPasswordForEmail: vi.fn().mockResolvedValue({
        error: null
      }),
      onAuthStateChange: vi.fn().mockImplementation((callback) => {
        // Call callback immediately with null session for testing
        if (callback) {
          setTimeout(() => callback('SIGNED_OUT', null), 0);
        }
        return {
          data: { subscription: { unsubscribe: vi.fn() } }
        };
      }),
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    }),
    functions: {
      invoke: vi.fn().mockResolvedValue({ data: null, error: null }),
    },
    rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
    channel: vi.fn(() => ({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    })),
  },
}));