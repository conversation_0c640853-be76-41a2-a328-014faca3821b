import React, { useState, useEffect } from 'react';
import { supabase } from '../services/auth';
import { ApiTier, TIER_LIMITS } from '../services/apiKeyService';

interface ApiMetrics {
  usage: {
    totalRequests: number;
    requestsToday: number;
    requestsThisMonth: number;
    avgResponseTime: number;
    cacheHitRate: number;
    topEndpoints: Array<{
      endpoint: string;
      requests: number;
      avgResponseTime: number;
      cost: number;
    }>;
  };
  billing: {
    tier: ApiTier;
    monthlyFee: number;
    usageCost: number;
    totalCost: number;
    estimatedSavings: number;
    nextBillDate: string;
  };
  performance: {
    uptime: number;
    errorRate: number;
    latencyP95: number;
    throughput: number;
  };
  revenue?: {
    mrr: number; // Monthly Recurring Revenue
    arr: number; // Annual Recurring Revenue
    churnRate: number;
    upgradeRate: number;
  };
}

interface ApiKey {
  id: string;
  name: string;
  tier: ApiTier;
  isActive: boolean;
  totalRequests: number;
  monthlyRequests: number;
  lastUsedAt?: string;
}

export const ApiAnalyticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<ApiMetrics | null>(null);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'24h' | '7d' | '30d'>('30d');

  useEffect(() => {
    fetchAnalytics();
  }, [selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Fetch user's API keys
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get JWT token for API calls
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) throw new Error('No access token');

      const headers = {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      };

      // Fetch real analytics data from our API
      const [overviewResponse, endpointsResponse, apiKeysResponse] = await Promise.all([
        fetch(`/api/analytics/overview?period=${selectedPeriod}`, { headers }),
        fetch(`/api/analytics/endpoints?period=${selectedPeriod}&limit=10`, { headers }),
        fetch('/api/keys', { headers }) // Get user's API keys
      ]);

      if (!overviewResponse.ok) {
        throw new Error(`Analytics API error: ${overviewResponse.status}`);
      }

      const overviewData = await overviewResponse.json();
      const endpointsData = await endpointsResponse.json();
      const apiKeysData = await apiKeysResponse.json();

      // Transform the data to match our interface
      const realMetrics: ApiMetrics = {
        usage: {
          totalRequests: overviewData.data.usage.totalRequests,
          requestsToday: overviewData.data.usage.requestsToday,
          requestsThisMonth: overviewData.data.usage.requestsThisMonth,
          avgResponseTime: overviewData.data.usage.avgResponseTime,
          cacheHitRate: overviewData.data.usage.cacheHitRate,
          topEndpoints: endpointsData.success ? endpointsData.data.endpoints.map((ep: any) => ({
            endpoint: ep.endpoint,
            requests: ep.requests,
            avgResponseTime: ep.avgResponseTime,
            cost: ep.cost
          })) : []
        },
        billing: {
          tier: overviewData.data.costs.monthlyFee > 0 ? 
                (overviewData.data.costs.monthlyFee >= 199 ? ApiTier.ENTERPRISE : ApiTier.PREMIUM) : 
                ApiTier.FREE,
          monthlyFee: overviewData.data.costs.monthlyFee,
          usageCost: overviewData.data.costs.usageCost,
          totalCost: overviewData.data.costs.totalCost,
          estimatedSavings: overviewData.data.costs.estimatedSavings,
          nextBillDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // Next month
        },
        performance: {
          uptime: overviewData.data.usage.uptime,
          errorRate: overviewData.data.usage.errorRate,
          latencyP95: overviewData.data.performance.latencyP95,
          throughput: overviewData.data.performance.throughput
        }
      };

      // Transform API keys data
      const realApiKeys: ApiKey[] = apiKeysData.apiKeys?.map((key: any) => ({
        id: key.id,
        name: key.name,
        tier: key.tier,
        isActive: key.isActive,
        totalRequests: key.totalRequests,
        monthlyRequests: key.monthlyRequests,
        lastUsedAt: key.lastUsedAt
      })) || [];

      setMetrics(realMetrics);
      setApiKeys(realApiKeys);
      setError(null);
      
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
      
      // Fallback to empty data instead of mock data
      setMetrics({
        usage: {
          totalRequests: 0,
          requestsToday: 0,
          requestsThisMonth: 0,
          avgResponseTime: 0,
          cacheHitRate: 0,
          topEndpoints: []
        },
        billing: {
          tier: ApiTier.FREE,
          monthlyFee: 0,
          usageCost: 0,
          totalCost: 0,
          estimatedSavings: 0,
          nextBillDate: new Date().toISOString().split('T')[0]
        },
        performance: {
          uptime: 100,
          errorRate: 0,
          latencyP95: 0,
          throughput: 0
        }
      });
      setApiKeys([]);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getTierBadgeColor = (tier: ApiTier) => {
    switch (tier) {
      case ApiTier.FREE:
        return 'bg-gray-100 text-gray-800';
      case ApiTier.PREMIUM:
        return 'bg-purple-100 text-purple-800';
      case ApiTier.ENTERPRISE:
        return 'bg-gold-100 text-gold-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">❌ Error Loading Analytics</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={fetchAnalytics}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!metrics) return null;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">API Analytics Dashboard</h1>
        <p className="text-gray-600 mt-2">Monitor your API usage, costs, and revenue in real-time</p>
        
        {/* Period Selector */}
        <div className="mt-4 flex space-x-2">
          {(['24h', '7d', '30d'] as const).map(period => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-lg text-sm font-medium ${
                selectedPeriod === period
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {period === '24h' ? 'Last 24 Hours' : period === '7d' ? 'Last 7 Days' : 'Last 30 Days'}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="text-2xl">📊</div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Requests</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(metrics.usage.totalRequests)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="text-2xl">⚡</div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Avg Response Time</p>
              <p className="text-2xl font-semibold text-gray-900">
                {metrics.usage.avgResponseTime}ms
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="text-2xl">🔥</div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Cache Hit Rate</p>
              <p className="text-2xl font-semibold text-green-600">
                {formatPercentage(metrics.usage.cacheHitRate)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="text-2xl">💰</div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Monthly Cost</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatCurrency(metrics.billing.totalCost)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cost Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">💳 Cost Breakdown</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Subscription Fee ({metrics.billing.tier})</span>
                <span className="font-semibold">{formatCurrency(metrics.billing.monthlyFee)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">API Usage Cost</span>
                <span className="font-semibold">{formatCurrency(metrics.billing.usageCost)}</span>
              </div>
              <div className="flex justify-between items-center text-green-600">
                <span>Cache Savings</span>
                <span className="font-semibold">-{formatCurrency(metrics.billing.estimatedSavings)}</span>
              </div>
              <div className="border-t pt-2">
                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Total Cost</span>
                  <span>{formatCurrency(metrics.billing.totalCost)}</span>
                </div>
              </div>
            </div>
            
            {metrics.billing.tier === ApiTier.FREE && (
              <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-900 mb-2">🚀 Upgrade to Premium API</h4>
                <p className="text-purple-700 text-sm mb-3">
                  Get 100x more requests, priority caching, and analytics for just $49.99/month
                </p>
                <button className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700">
                  Upgrade Now
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">📈 Performance Metrics</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Uptime</span>
                <span className="font-semibold text-green-600">{formatPercentage(metrics.performance.uptime)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Error Rate</span>
                <span className="font-semibold">{formatPercentage(metrics.performance.errorRate)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">95th Percentile Latency</span>
                <span className="font-semibold">{metrics.performance.latencyP95}ms</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Throughput</span>
                <span className="font-semibold">{metrics.performance.throughput} req/s</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Endpoints */}
      <div className="bg-white rounded-lg shadow mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">🔥 Top API Endpoints</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Endpoint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Response Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cost
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {metrics.usage.topEndpoints.map((endpoint, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {endpoint.endpoint}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatNumber(endpoint.requests)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {endpoint.avgResponseTime}ms
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatCurrency(endpoint.cost)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* API Keys Management */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">🔑 API Keys</h3>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700">
              Create New Key
            </button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tier
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Monthly Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Used
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {apiKeys.map((apiKey) => (
                <tr key={apiKey.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {apiKey.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTierBadgeColor(apiKey.tier)}`}>
                      {apiKey.tier.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      apiKey.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {apiKey.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatNumber(apiKey.monthlyRequests)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {apiKey.lastUsedAt ? new Date(apiKey.lastUsedAt).toLocaleDateString() : 'Never'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-purple-600 hover:text-purple-900 mr-3">
                      View
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      Rotate
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ApiAnalyticsDashboard; 