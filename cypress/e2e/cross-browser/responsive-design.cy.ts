import { 
  testOnAllMobileDevices,
  testOnAllTabletDevices,
  testOnAllDesktopSizes,
  simulateTouch,
  isMobile,
  isTablet,
  isDesktop
} from '../../support/browsers/browser-utils'

describe('Responsive Design Testing', () => {
  beforeEach(() => {
    cy.mockSupabaseAuth()
  })

  // Mobile device testing
  testOnAllMobileDevices((viewport) => {
    describe('Mobile Layout Tests', () => {
      it('should display mobile navigation correctly', () => {
        cy.visitAndWait('/')
        
        // Mobile menu toggle should be visible
        cy.get('[data-testid=\"mobile-menu-toggle\"]').should('be.visible')
        
        // Desktop navigation should be hidden
        cy.get('[data-testid=\"desktop-nav\"]').should('not.be.visible')
        
        // Test mobile menu interaction
        cy.get('[data-testid=\"mobile-menu-toggle\"]').click()
        cy.get('[data-testid=\"mobile-menu\"]').should('be.visible')
        
        // Test navigation links
        cy.get('[data-testid=\"mobile-nav-gallery\"]').should('be.visible')
        cy.get('[data-testid=\"mobile-nav-create\"]').should('be.visible')
      })

      it('should handle story gallery on mobile', () => {
        cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
        
        cy.visitAndWait('/gallery')
        cy.wait('@getStories')
        
        // Stories should stack vertically on mobile
        cy.get('[data-testid=\"story-grid\"]').should('have.class', 'grid-cols-1')
        
        // Story cards should be properly sized for mobile
        cy.get('[data-testid=\"story-card\"]').first().then(($card) => {
          expect($card.width()).to.be.lessThan(viewport.width)
          expect($card.width()).to.be.greaterThan(viewport.width * 0.8)
        })
        
        // Touch interactions should work
        simulateTouch('[data-testid=\"story-card\"]', 'tap')
      })

      it('should handle forms on mobile', () => {
        cy.visitAndWait('/create-story')
        
        // Form inputs should be properly sized
        cy.get('[data-testid=\"story-title-input\"]').should('be.visible')
        cy.get('[data-testid=\"story-description-input\"]').should('be.visible')
        
        // Virtual keyboard should not break layout
        cy.get('[data-testid=\"story-title-input\"]').focus()
        cy.get('[data-testid=\"story-title-input\"]').type('Mobile Test Story')
        
        // Form should remain usable
        cy.get('[data-testid=\"create-story-button\"]').should('be.visible')
      })

      it('should handle touch gestures', () => {
        cy.visitAndWait('/gallery')
        
        // Test swipe gestures if implemented
        simulateTouch('[data-testid=\"story-card\"]', 'swipe')
        
        // Test pinch gestures if implemented
        simulateTouch('[data-testid=\"story-grid\"]', 'pinch')
        
        // Test scroll behavior
        cy.scrollTo('bottom', { duration: 1000 })
        cy.scrollTo('top', { duration: 1000 })
      })

      it('should display readable text sizes', () => {
        cy.visitAndWait('/')
        
        // Main heading should be large enough
        cy.get('h1').should('have.css', 'font-size').then((fontSize) => {
          const size = parseInt(fontSize)
          expect(size).to.be.greaterThan(24) // At least 24px
        })
        
        // Body text should be at least 16px
        cy.get('body').should('have.css', 'font-size').then((fontSize) => {
          const size = parseInt(fontSize)
          expect(size).to.be.greaterThan(15)
        })
        
        // Buttons should be large enough for touch
        cy.get('[data-testid=\"nav-gallery\"]').then(($btn) => {
          expect($btn.height()).to.be.greaterThan(44) // iOS touch target minimum
        })
      })

      it('should handle modals and overlays properly', () => {
        cy.visitAndWait('/gallery')
        
        // If modals exist, they should fill the screen appropriately
        cy.get('body').type('{esc}') // Close any open modals
        
        // Test modal opening if available
        if (Cypress.$('[data-testid=\"open-modal-button\"]').length > 0) {
          cy.get('[data-testid=\"open-modal-button\"]').click()
          cy.get('[data-testid=\"modal\"]').should('be.visible')
          
          // Modal should not extend beyond viewport
          cy.get('[data-testid=\"modal\"]').then(($modal) => {
            expect($modal.width()).to.be.lessThan(viewport.width)
          })
        }
      })
    })
  })

  // Tablet device testing
  testOnAllTabletDevices((viewport) => {
    describe('Tablet Layout Tests', () => {
      it('should display tablet-optimized navigation', () => {
        cy.visitAndWait('/')
        
        // Navigation should adapt to tablet size
        cy.get('[data-testid=\"nav-home\"]').should('be.visible')
        cy.get('[data-testid=\"nav-gallery\"]').should('be.visible')
        
        // Check if using mobile or desktop nav pattern
        cy.get('body').then(($body) => {
          if ($body.find('[data-testid=\"mobile-menu-toggle\"]').is(':visible')) {
            // Using mobile pattern
            cy.get('[data-testid=\"mobile-menu-toggle\"]').should('be.visible')
          } else {
            // Using desktop pattern
            cy.get('[data-testid=\"desktop-nav\"]').should('be.visible')
          }
        })
      })

      it('should display optimal grid layouts', () => {
        cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
        
        cy.visitAndWait('/gallery')
        cy.wait('@getStories')
        
        // Should show 2-3 columns on tablet
        cy.get('[data-testid=\"story-grid\"]').should('satisfy', ($grid) => {
          return $grid.hasClass('grid-cols-2') || $grid.hasClass('grid-cols-3')
        })
        
        // Cards should be appropriately sized
        cy.get('[data-testid=\"story-card\"]').first().then(($card) => {
          const cardWidth = $card.width()
          expect(cardWidth).to.be.greaterThan(200)
          expect(cardWidth).to.be.lessThan(viewport.width / 2 + 50)
        })
      })

      it('should handle touch and mouse interactions', () => {
        cy.visitAndWait('/gallery')
        
        // Should work with both touch and mouse
        cy.get('[data-testid=\"story-card\"]').first().click()
        cy.url().should('include', '/story/')
        
        cy.go('back')
        
        // Test touch gestures
        simulateTouch('[data-testid=\"story-card\"]', 'tap')
      })

      it('should optimize form layouts for tablets', () => {
        cy.visitAndWait('/create-story')
        
        // Forms should use available space efficiently
        cy.get('[data-testid=\"story-form\"]').should('be.visible')
        
        // Input fields should be appropriately sized
        cy.get('[data-testid=\"story-title-input\"]').then(($input) => {
          expect($input.width()).to.be.greaterThan(300)
          expect($input.width()).to.be.lessThan(viewport.width * 0.9)
        })
      })
    })
  })

  // Desktop testing
  testOnAllDesktopSizes((viewport) => {
    describe('Desktop Layout Tests', () => {
      it('should display full desktop navigation', () => {
        cy.visitAndWait('/')
        
        // Desktop navigation should be visible
        cy.get('[data-testid=\"desktop-nav\"]').should('be.visible')
        
        // Mobile menu should be hidden
        cy.get('[data-testid=\"mobile-menu-toggle\"]').should('not.be.visible')
        
        // All navigation items should be accessible
        cy.get('[data-testid=\"nav-home\"]').should('be.visible')
        cy.get('[data-testid=\"nav-gallery\"]').should('be.visible')
        cy.get('[data-testid=\"nav-create\"]').should('be.visible')
        cy.get('[data-testid=\"nav-pricing\"]').should('be.visible')
      })

      it('should utilize available screen space efficiently', () => {
        cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
        
        cy.visitAndWait('/gallery')
        cy.wait('@getStories')
        
        // Should show multiple columns based on screen size
        if (viewport.width >= 1920) {
          // Large desktop should show more columns
          cy.get('[data-testid=\"story-grid\"]').should('satisfy', ($grid) => {
            return $grid.hasClass('grid-cols-4') || 
                   $grid.hasClass('grid-cols-5') || 
                   $grid.hasClass('grid-cols-6')
          })
        } else if (viewport.width >= 1366) {
          // Standard desktop
          cy.get('[data-testid=\"story-grid\"]').should('satisfy', ($grid) => {
            return $grid.hasClass('grid-cols-3') || $grid.hasClass('grid-cols-4')
          })
        }
      })

      it('should handle hover states correctly', () => {
        cy.visitAndWait('/gallery')
        
        // Test hover effects on interactive elements
        cy.get('[data-testid=\"story-card\"]').first().trigger('mouseover')
        
        // Hover effects should be visible (if implemented)
        cy.get('[data-testid=\"story-card\"]').first().should('be.visible')
        
        // Test button hovers
        cy.get('[data-testid=\"nav-gallery\"]').trigger('mouseover')
        cy.get('[data-testid=\"nav-gallery\"]').trigger('mouseout')
      })

      it('should support keyboard navigation', () => {
        cy.visitAndWait('/')
        
        // Test tab navigation
        cy.get('body').tab()
        cy.focused().should('be.visible')
        
        // Continue tabbing through interactive elements
        cy.tab().tab().tab()
        
        // Test enter key activation
        cy.focused().type('{enter}')
      })

      it('should handle large content areas appropriately', () => {
        cy.visitAndWait('/dashboard')
        
        // Content should not stretch too wide on large screens
        cy.get('[data-testid=\"main-content\"]').then(($content) => {
          if (viewport.width > 1920) {
            // Very large screens should constrain content width
            expect($content.width()).to.be.lessThan(viewport.width * 0.9)
          }
        })
        
        // Sidebar should be appropriately sized
        if (Cypress.$('[data-testid=\"sidebar\"]').length > 0) {
          cy.get('[data-testid=\"sidebar\"]').should('be.visible')
          cy.get('[data-testid=\"sidebar\"]').then(($sidebar) => {
            expect($sidebar.width()).to.be.greaterThan(200)
            expect($sidebar.width()).to.be.lessThan(400)
          })
        }
      })
    })
  })

  // Cross-viewport tests
  describe('Viewport Transition Tests', () => {
    it('should handle viewport size changes gracefully', () => {
      cy.visitAndWait('/gallery')
      
      // Start with desktop
      cy.viewport(1920, 1080)
      cy.get('[data-testid=\"desktop-nav\"]').should('be.visible')
      
      // Resize to tablet
      cy.viewport(768, 1024)
      cy.wait(500) // Allow time for responsive changes
      
      // Check that layout adapts
      cy.get('[data-testid=\"story-grid\"]').should('be.visible')
      
      // Resize to mobile
      cy.viewport(375, 667)
      cy.wait(500)
      
      // Mobile navigation should appear
      cy.get('[data-testid=\"mobile-menu-toggle\"]').should('be.visible')
    })

    it('should maintain functionality across viewport changes', () => {
      cy.visitAndWait('/')
      
      // Test functionality at different sizes
      const viewports = [
        { width: 375, height: 667 },   // Mobile
        { width: 768, height: 1024 },  // Tablet
        { width: 1920, height: 1080 }  // Desktop
      ]
      
      viewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height)
        cy.wait(300)
        
        // Navigation should work at all sizes
        cy.get('[data-testid=\"nav-gallery\"]').click()
        cy.url().should('include', '/gallery')
        
        cy.get('[data-testid=\"nav-home\"]').click()
        cy.url().should('eq', Cypress.config().baseUrl + '/')
      })
    })
  })

  // Accessibility across devices
  describe('Responsive Accessibility', () => {
    it('should maintain accessibility on mobile', () => {
      cy.viewport(375, 667)
      cy.visitAndWait('/')
      
      // Check focus management on mobile
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Check touch targets are large enough
      cy.get('[data-testid=\"mobile-menu-toggle\"]').then(($btn) => {
        expect($btn.width()).to.be.greaterThan(44)
        expect($btn.height()).to.be.greaterThan(44)
      })
    })

    it('should maintain accessibility on desktop', () => {
      cy.viewport(1920, 1080)
      cy.visitAndWait('/')
      
      // Keyboard navigation should work
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Check that all interactive elements are accessible
      cy.get('[data-testid=\"nav-gallery\"]').should('be.visible')
      cy.get('[data-testid=\"nav-create\"]').should('be.visible')
    })
  })
})