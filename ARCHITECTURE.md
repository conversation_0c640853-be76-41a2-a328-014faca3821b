# Word-by-Word Story: Technical Architecture

This document outlines the technical architecture of the Word-by-Word Story application, detailing the system's components, data flow, and design decisions.

## System Overview

Word-by-Word Story is a collaborative storytelling platform built with modern web technologies. The application enables users to create and participate in stories where each contributor adds one word (or more, depending on the story settings) at a time.

![Architecture Overview](/placeholder.svg)

## Technology Stack

### Frontend

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS with Shadcn UI components
- **State Management**: React Context API + React Query
- **Routing**: React Router v6
- **Form Handling**: React Hook Form with Zod validation

### Backend & Data Storage

- **Backend Services**:
  - Supabase for database and authentication
  - Firebase for real-time features
- **Database**: PostgreSQL (via Supabase)
- **ORM**: Prisma

### Deployment & Infrastructure

- **Hosting**: Vercel
- **CI/CD**: Vercel deployment pipelines
- **Edge Functions**: Vercel Edge Functions for server-side logic

## Application Architecture

### Core Components

1. **Authentication System**

   - Managed through `AuthContext` and `AuthProvider`
   - Supports registration, login, and session management
   - User tiers and permissions

2. **Story Management**

   - `StoryContext` and `StoryProvider` handle story state
   - CRUD operations for stories
   - Turn management and contribution tracking

3. **Real-time Collaboration**

   - Typing indicators
   - Turn notifications
   - User presence

4. **Subscription & Payment**
   - Tier-based features
   - Ad-free subscription option

### Data Models

#### User

```typescript
interface User {
  id: string;
  username?: string;
  email: string;
  profilePicture?: string;
  credits?: number;
  tier?:
    | "free"
    | "microtier"
    | "wordsmith"
    | "storyteller"
    | "authors-guild"
    | "premium"
    | "pro";
}
```

#### Story

```typescript
interface Story {
  id: string;
  title: string;
  description: string;
  createdBy: User;
  createdAt: string;
  updatedAt: string;
  status: string;
  participants: User[];
  contributions: Contribution[];
  likes: number;
  views: number;
  tags: string[];
  isPublished: boolean;
  votes: number;
  words: Word[];
  wordCount: number;
  typingInfo?: TypingInfo;
  lastNudged?: LastNudged;
  currentTurn: string;
  contributionMode: StoryContributionMode;
  wordsPerContribution?: number;
}
```

#### Word

```typescript
interface Word {
  id: string;
  word: string;
  userId: string;
  createdAt: string;
}
```

### Directory Structure

The application follows a feature-based organization:

```
src/
├── api/                # API endpoints and service wrappers
├── components/         # Reusable UI components
│   ├── ui/             # Basic UI components (buttons, inputs, etc.)
│   ├── home/           # Homepage-specific components
│   ├── story/          # Story-related components
│   └── layout/         # Layout components
├── contexts/           # React contexts for state management
├── edge-functions/     # Vercel Edge Functions
├── hooks/              # Custom React hooks
├── integrations/       # Third-party service integrations
├── lib/                # Utility functions and libraries
├── pages/              # Page components
├── routes/             # Routing configuration
├── services/           # Service layer for API calls
└── types/              # TypeScript type definitions
```

## State Management

The application uses React Context API for global state management, with several key contexts:

1. **AuthContext**: Manages user authentication state
2. **StoryContext**: Handles story data and operations
3. **AdsContext**: Controls advertisement display
4. **OnboardingContext**: Manages user onboarding flow

For server state and data fetching, React Query is used to manage caching, background updates, and optimistic UI updates.

## Authentication Flow

1. User registers or logs in
2. Authentication credentials are verified with Supabase
3. JWT token is stored for session management
4. User information is loaded into AuthContext
5. Protected routes become accessible

## Story Creation & Contribution Flow

1. User creates a new story with title, description, and contribution settings
2. Creator adds the first word
3. Other participants are invited or join from the gallery
4. Each participant takes turns adding words based on the story's contribution mode
5. Real-time updates notify users when it's their turn
6. Story can be completed and published to the gallery

## Deployment Architecture

The application is deployed on Vercel with the following environments:

- **Development**: For ongoing development and testing
- **Preview**: For staging and pre-production validation
- **Production**: Live environment for end users

Each environment has its own configuration and environment variables.

## Performance Optimizations

1. **Code Splitting**: Routes are lazy-loaded
2. **Memoization**: React.memo and useMemo for expensive components
3. **Virtualization**: For long lists of stories or words
4. **Image Optimization**: For profile pictures and UI assets
5. **Caching Strategy**: Using React Query's caching capabilities

## Future Architectural Considerations

1. **Microservices**: Splitting backend functionality into dedicated services
2. **WebSockets**: Enhanced real-time collaboration features
3. **AI Integration**: More advanced AI-powered word suggestions
4. **Analytics**: Comprehensive usage tracking and insights
5. **Mobile App**: React Native version sharing the same backend

## Security Considerations

1. **Authentication**: Secure token-based authentication
2. **Data Validation**: Client and server-side validation
3. **Content Moderation**: For published stories
4. **Rate Limiting**: To prevent abuse
5. **GDPR Compliance**: User data handling and privacy

## Conclusion

The Word-by-Word Story application follows a modern React architecture with a focus on real-time collaboration, scalability, and user experience. The combination of React, TypeScript, and Tailwind CSS provides a robust foundation for the frontend, while Supabase and Firebase offer powerful backend capabilities without the need for a custom server implementation.
