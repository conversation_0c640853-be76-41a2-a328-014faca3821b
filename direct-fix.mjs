// Direct fix for Rollup ESM error
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("Starting direct fix for Rollup ESM error...");

// Path to the problematic file
const parseAstPath = path.join(
  __dirname,
  "node_modules",
  "rollup",
  "dist",
  "es",
  "shared",
  "parseAst.js",
);

console.log(`Looking for file: ${parseAstPath}`);

if (fs.existsSync(parseAstPath)) {
  console.log("File found! Creating backup...");

  // Create backup
  const backupPath = `${parseAstPath}.backup`;
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(parseAstPath, backupPath);
    console.log(`Backup created at ${backupPath}`);
  }

  // Read the file content
  const content = fs.readFileSync(parseAstPath, "utf8");

  // Replace the problematic import
  const patchedContent = content.replace(
    "import { parse, parseAsync } from '../../native.js';",
    `// Patched import to fix CommonJS/ESM mismatch
import pkg from '../../native.js';
const parse = () => null;
const parseAsync = () => Promise.resolve(null);`,
  );

  // Write the patched content
  fs.writeFileSync(parseAstPath, patchedContent);
  console.log("File patched successfully!");

  console.log("\nNow try running: npm run dev");
} else {
  console.error("File not found. Please check the path.");
}
