/**
 * Comprehensive Load Testing Scenario
 * Focus: Real-time messaging, token system, and collaborative features under load
 */

// Load testing configuration
const LOAD_TEST_CONFIG = {
  // Test scenarios with increasing load
  scenarios: [
    { name: "Light Load", users: 10, duration: 30, messagesPerUser: 5, tokenActions: 2 },
    { name: "Medium Load", users: 50, duration: 60, messagesPerUser: 10, tokenActions: 5 },
    { name: "Heavy Load", users: 100, duration: 120, messagesPerUser: 20, tokenActions: 10 },
    { name: "Stress Test", users: 200, duration: 180, messagesPerUser: 30, tokenActions: 15 },
  ],
  
  // Performance thresholds
  thresholds: {
    messageLatency: 500,      // ms - max time for message to appear
    tokenActionTime: 1000,    // ms - max time for token action to complete
    connectionTime: 2000,     // ms - max time to establish real-time connection
    memoryGrowth: 100,        // MB - max memory growth during test
    cpuUsage: 80,             // % - max CPU usage during peak load
  },

  // Real-time scenarios
  realTimeFeatures: [
    'message_sending',
    'typing_indicators', 
    'user_presence',
    'special_token_actions',
    'story_updates',
    'participant_management'
  ]
};

// Simulate user behavior patterns
class VirtualUser {
  constructor(id, scenario) {
    this.id = id;
    this.scenario = scenario;
    this.isActive = false;
    this.messagesSent = 0;
    this.tokenActionsUsed = 0;
    this.connectionLatency = 0;
    this.performanceLog = [];
  }

  // Start user session
  async startSession() {
    const startTime = performance.now();
    
    try {
      // Simulate connection establishment
      await this.simulateConnection();
      
      // Simulate authentication
      await this.simulateAuth();
      
      // Start collaborative activity
      await this.simulateCollaborativeActivity();
      
      this.isActive = true;
      this.connectionLatency = performance.now() - startTime;
      
      return { success: true, connectionTime: this.connectionLatency };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async simulateConnection() {
    // Simulate WebSocket connection setup
    const latency = Math.random() * 100 + 50; // 50-150ms
    await new Promise(resolve => setTimeout(resolve, latency));
    this.log('connection_established', latency);
  }

  async simulateAuth() {
    // Simulate authentication process
    const authTime = Math.random() * 200 + 100; // 100-300ms
    await new Promise(resolve => setTimeout(resolve, authTime));
    this.log('auth_completed', authTime);
  }

  async simulateCollaborativeActivity() {
    const activityDuration = this.scenario.duration * 1000; // Convert to ms
    const activityInterval = activityDuration / (this.scenario.messagesPerUser + this.scenario.tokenActions);
    
    let currentTime = 0;
    
    while (currentTime < activityDuration && this.isActive) {
      const action = this.chooseRandomAction();
      
      try {
        const actionResult = await this.performAction(action);
        this.log(action, actionResult.duration, actionResult.success);
      } catch (error) {
        this.log(action, 0, false, error.message);
      }
      
      // Wait for next action
      const waitTime = activityInterval + (Math.random() * 1000); // Add some randomness
      await new Promise(resolve => setTimeout(resolve, waitTime));
      currentTime += waitTime;
    }
  }

  chooseRandomAction() {
    const actions = ['send_message', 'start_typing', 'stop_typing', 'token_action', 'view_story'];
    
    // Weight actions based on realistic usage patterns
    const weights = {
      'send_message': 40,     // Most common action
      'start_typing': 20,     // Frequent
      'stop_typing': 20,      // Frequent 
      'token_action': 15,     // Less common (requires tokens)
      'view_story': 5         // Occasional
    };
    
    const totalWeight = Object.values(weights).reduce((a, b) => a + b, 0);
    let random = Math.random() * totalWeight;
    
    for (const [action, weight] of Object.entries(weights)) {
      random -= weight;
      if (random <= 0) return action;
    }
    
    return 'send_message'; // Fallback
  }

  async performAction(action) {
    const startTime = performance.now();
    
    switch (action) {
      case 'send_message':
        return await this.sendMessage();
      case 'start_typing':
        return await this.startTyping();
      case 'stop_typing':
        return await this.stopTyping();
      case 'token_action':
        return await this.performTokenAction();
      case 'view_story':
        return await this.viewStory();
      default:
        return { duration: 0, success: true };
    }
  }

  async sendMessage() {
    const startTime = performance.now();
    
    // Simulate message composition and sending
    const messageContent = this.generateMessage();
    const networkLatency = Math.random() * 200 + 50; // 50-250ms
    
    await new Promise(resolve => setTimeout(resolve, networkLatency));
    
    this.messagesSent++;
    const duration = performance.now() - startTime;
    
    return { 
      duration, 
      success: duration < LOAD_TEST_CONFIG.thresholds.messageLatency,
      messageLength: messageContent.length
    };
  }

  async performTokenAction() {
    const startTime = performance.now();
    
    // Simulate token validation and action processing
    const tokenValidationTime = Math.random() * 100 + 50; // 50-150ms
    const actionProcessingTime = Math.random() * 300 + 200; // 200-500ms
    
    await new Promise(resolve => setTimeout(resolve, tokenValidationTime + actionProcessingTime));
    
    this.tokenActionsUsed++;
    const duration = performance.now() - startTime;
    
    return {
      duration,
      success: duration < LOAD_TEST_CONFIG.thresholds.tokenActionTime,
      actionType: ['gotcha', 'reverse', 'golden'][Math.floor(Math.random() * 3)]
    };
  }

  async startTyping() {
    const latency = Math.random() * 50 + 10; // 10-60ms
    await new Promise(resolve => setTimeout(resolve, latency));
    return { duration: latency, success: true };
  }

  async stopTyping() {
    const latency = Math.random() * 50 + 10; // 10-60ms
    await new Promise(resolve => setTimeout(resolve, latency));
    return { duration: latency, success: true };
  }

  async viewStory() {
    const loadTime = Math.random() * 500 + 100; // 100-600ms
    await new Promise(resolve => setTimeout(resolve, loadTime));
    return { duration: loadTime, success: true };
  }

  generateMessage() {
    const words = ['once', 'upon', 'time', 'story', 'magical', 'adventure', 'hero', 'journey', 'treasure', 'dragon'];
    const wordCount = Math.floor(Math.random() * 3) + 1; // 1-3 words
    return Array.from({ length: wordCount }, () => words[Math.floor(Math.random() * words.length)]).join(' ');
  }

  log(action, duration, success = true, error = null) {
    this.performanceLog.push({
      timestamp: Date.now(),
      action,
      duration,
      success,
      error
    });
  }

  getStats() {
    return {
      id: this.id,
      messagesSent: this.messagesSent,
      tokenActionsUsed: this.tokenActionsUsed,
      connectionLatency: this.connectionLatency,
      totalActions: this.performanceLog.length,
      failedActions: this.performanceLog.filter(log => !log.success).length,
      averageActionTime: this.performanceLog.reduce((sum, log) => sum + log.duration, 0) / this.performanceLog.length
    };
  }
}

// Load test orchestrator
class LoadTestOrchestrator {
  constructor() {
    this.activeUsers = new Map();
    this.testResults = {
      scenarios: [],
      overallStats: {},
      performanceMetrics: {}
    };
  }

  async runLoadTest(scenario) {
    console.log(`🚀 Starting ${scenario.name} - ${scenario.users} users for ${scenario.duration}s`);
    console.log(`📊 Target: ${scenario.messagesPerUser} messages/user, ${scenario.tokenActions} token actions/user`);
    
    const startTime = performance.now();
    const users = [];

    // Create virtual users
    for (let i = 0; i < scenario.users; i++) {
      const user = new VirtualUser(`user-${i}`, scenario);
      users.push(user);
    }

    // Start all users concurrently (simulate real-world sudden load)
    const connectionPromises = users.map(user => user.startSession());
    const connectionResults = await Promise.allSettled(connectionPromises);

    // Monitor performance during test
    const performanceMonitor = this.startPerformanceMonitoring();

    // Wait for test duration
    await new Promise(resolve => setTimeout(resolve, scenario.duration * 1000));

    // Stop all users
    users.forEach(user => user.isActive = false);
    
    // Stop performance monitoring
    const performanceMetrics = this.stopPerformanceMonitoring(performanceMonitor);

    // Collect results
    const testDuration = performance.now() - startTime;
    const userStats = users.map(user => user.getStats());
    
    const results = {
      scenario: scenario.name,
      duration: testDuration,
      users: scenario.users,
      connectionSuccess: connectionResults.filter(r => r.status === 'fulfilled' && r.value.success).length,
      totalMessages: userStats.reduce((sum, stats) => sum + stats.messagesSent, 0),
      totalTokenActions: userStats.reduce((sum, stats) => sum + stats.tokenActionsUsed, 0),
      averageConnectionTime: userStats.reduce((sum, stats) => sum + stats.connectionLatency, 0) / userStats.length,
      failureRate: userStats.reduce((sum, stats) => sum + stats.failedActions, 0) / userStats.reduce((sum, stats) => sum + stats.totalActions, 0),
      performanceMetrics,
      thresholdViolations: this.checkThresholds(userStats, performanceMetrics)
    };

    this.testResults.scenarios.push(results);
    return results;
  }

  startPerformanceMonitoring() {
    const startMemory = process.memoryUsage();
    const startTime = performance.now();
    
    return {
      startMemory,
      startTime,
      memorySnapshots: [startMemory]
    };
  }

  stopPerformanceMonitoring(monitor) {
    const endTime = performance.now();
    const endMemory = process.memoryUsage();
    
    return {
      duration: endTime - monitor.startTime,
      memoryGrowth: (endMemory.heapUsed - monitor.startMemory.heapUsed) / 1024 / 1024, // MB
      peakMemory: Math.max(...monitor.memorySnapshots.map(m => m.heapUsed)) / 1024 / 1024, // MB
      finalMemory: endMemory.heapUsed / 1024 / 1024 // MB
    };
  }

  checkThresholds(userStats, performanceMetrics) {
    const violations = [];
    const thresholds = LOAD_TEST_CONFIG.thresholds;

    // Check message latency
    const avgMessageTime = userStats.reduce((sum, stats) => sum + stats.averageActionTime, 0) / userStats.length;
    if (avgMessageTime > thresholds.messageLatency) {
      violations.push(`Message latency: ${avgMessageTime.toFixed(2)}ms > ${thresholds.messageLatency}ms`);
    }

    // Check memory growth
    if (performanceMetrics.memoryGrowth > thresholds.memoryGrowth) {
      violations.push(`Memory growth: ${performanceMetrics.memoryGrowth.toFixed(2)}MB > ${thresholds.memoryGrowth}MB`);
    }

    return violations;
  }

  async runAllScenarios() {
    console.log('🧪 Starting Comprehensive Load Testing Suite');
    console.log('=' .repeat(60));

    for (const scenario of LOAD_TEST_CONFIG.scenarios) {
      try {
        const result = await this.runLoadTest(scenario);
        this.printScenarioResults(result);
        
        // Cool down period between scenarios
        console.log('⏸️ Cool down period...\n');
        await new Promise(resolve => setTimeout(resolve, 5000));
      } catch (error) {
        console.error(`❌ Scenario ${scenario.name} failed:`, error.message);
      }
    }

    this.printOverallResults();
  }

  printScenarioResults(result) {
    const status = result.thresholdViolations.length === 0 ? '✅ PASS' : '⚠️ WARNINGS';
    
    console.log(`\n${status} ${result.scenario}`);
    console.log(`├─ Users: ${result.users} (${result.connectionSuccess} connected successfully)`);
    console.log(`├─ Duration: ${(result.duration / 1000).toFixed(1)}s`);
    console.log(`├─ Messages: ${result.totalMessages} total`);
    console.log(`├─ Token Actions: ${result.totalTokenActions} total`);
    console.log(`├─ Avg Connection Time: ${result.averageConnectionTime.toFixed(2)}ms`);
    console.log(`├─ Failure Rate: ${(result.failureRate * 100).toFixed(2)}%`);
    console.log(`├─ Memory Growth: ${result.performanceMetrics.memoryGrowth.toFixed(2)}MB`);
    
    if (result.thresholdViolations.length > 0) {
      console.log(`└─ ⚠️ Threshold Violations:`);
      result.thresholdViolations.forEach(violation => {
        console.log(`   └─ ${violation}`);
      });
    } else {
      console.log(`└─ ✅ All thresholds met`);
    }
  }

  printOverallResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 LOAD TESTING SUMMARY');
    console.log('='.repeat(60));

    const totalTests = this.testResults.scenarios.length;
    const passedTests = this.testResults.scenarios.filter(s => s.thresholdViolations.length === 0).length;
    const maxUsers = Math.max(...this.testResults.scenarios.map(s => s.users));
    const totalMessages = this.testResults.scenarios.reduce((sum, s) => sum + s.totalMessages, 0);
    const totalTokenActions = this.testResults.scenarios.reduce((sum, s) => sum + s.totalTokenActions, 0);

    console.log(`Tests Run: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Success Rate: ${(passedTests / totalTests * 100).toFixed(1)}%`);
    console.log(`Max Concurrent Users: ${maxUsers}`);
    console.log(`Total Messages Processed: ${totalMessages}`);
    console.log(`Total Token Actions: ${totalTokenActions}`);

    // Performance assessment
    const performanceRating = this.calculatePerformanceRating();
    console.log(`\n🏆 Performance Rating: ${performanceRating}`);
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    this.generateRecommendations();
  }

  calculatePerformanceRating() {
    const totalTests = this.testResults.scenarios.length;
    const passedTests = this.testResults.scenarios.filter(s => s.thresholdViolations.length === 0).length;
    const successRate = passedTests / totalTests;

    if (successRate >= 1.0) return 'EXCELLENT 🏅';
    if (successRate >= 0.75) return 'GOOD 👍';
    if (successRate >= 0.5) return 'ACCEPTABLE ⚠️';
    return 'NEEDS IMPROVEMENT 🔧';
  }

  generateRecommendations() {
    const hasMemoryIssues = this.testResults.scenarios.some(s => 
      s.performanceMetrics.memoryGrowth > LOAD_TEST_CONFIG.thresholds.memoryGrowth
    );
    
    const hasLatencyIssues = this.testResults.scenarios.some(s => 
      s.averageConnectionTime > LOAD_TEST_CONFIG.thresholds.connectionTime
    );

    if (hasMemoryIssues) {
      console.log('🔧 Optimize memory usage - implement message cleanup and connection pooling');
    }
    
    if (hasLatencyIssues) {
      console.log('⚡ Improve connection speed - optimize WebSocket setup and authentication');
    }
    
    if (!hasMemoryIssues && !hasLatencyIssues) {
      console.log('✅ Performance is excellent - ready for production scale');
    }
    
    console.log('📈 Consider implementing auto-scaling for peak loads');
    console.log('📊 Monitor real-time metrics in production environment');
  }
}

// Export for use
export { LoadTestOrchestrator, VirtualUser, LOAD_TEST_CONFIG };

// Run load tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const orchestrator = new LoadTestOrchestrator();
  orchestrator.runAllScenarios()
    .then(() => {
      console.log('\n🎉 Load testing completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Load testing failed:', error);
      process.exit(1);
    });
} 