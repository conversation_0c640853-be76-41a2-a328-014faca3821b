# Firebase Auth Compatibility Migrations

## Important: Run these migrations to fix the notifications error

The application has been switched from Supabase Auth to Firebase Auth, but the database schema still expects Supabase UUID format for user IDs. Firebase uses alphanumeric strings like `bplINgCWBwNxoUg8L7EVewg87gU2` instead of UUIDs.

## How to Apply These Migrations

### Option 1: Using Supabase Dashboard (Recommended)

1. Go to your [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Navigate to **SQL Editor** in the left sidebar
4. Run each migration **in order**:
   - First: `0010_firebase_auth_compatibility.sql`
   - Second: `0011_user_profiles_firebase_compatibility.sql`
   - Third: `0012_stories_contributions_firebase_compatibility.sql`

### Option 2: Using Supabase CLI

If you have the Supabase CLI set up:

```bash
supabase db push
```

### Option 3: Run as a Single Script

Copy and paste this entire SQL script into the Supabase SQL Editor:

```sql
-- Combined Firebase Auth Compatibility Migrations

-- Part 1: Notifications Table
DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their own notifications (mark as read/unread)" ON public.notifications;
DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
DROP INDEX IF EXISTS idx_notifications_user_id_created_at;

ALTER TABLE public.notifications 
  ALTER COLUMN user_id TYPE TEXT,
  ALTER COLUMN actor_id TYPE TEXT;

ALTER TABLE public.notifications 
  DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
  DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey;

CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);

CREATE POLICY "Service role can manage all notifications"
ON public.notifications FOR ALL 
USING (true)
WITH CHECK (true);

-- Part 2: User Profiles Table  
ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;
ALTER TABLE public.user_profiles ALTER COLUMN id TYPE TEXT;

DROP POLICY IF EXISTS "Users can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

CREATE POLICY "Service role can manage all profiles"
ON public.user_profiles FOR ALL 
USING (true)
WITH CHECK (true);

-- Part 3: Stories and Contributions Tables
ALTER TABLE public.stories ALTER COLUMN author_id TYPE TEXT;
ALTER TABLE public.contributions ALTER COLUMN user_id TYPE TEXT;

DROP POLICY IF EXISTS "Stories are viewable by everyone" ON public.stories;
DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;

CREATE POLICY "Service role can manage all stories"
ON public.stories FOR ALL 
USING (true)
WITH CHECK (true);

DROP POLICY IF EXISTS "Contributions are viewable by everyone" ON public.contributions;
DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;

CREATE POLICY "Service role can manage all contributions"
ON public.contributions FOR ALL 
USING (true)
WITH CHECK (true);
```

## What These Migrations Do

1. **Change UUID columns to TEXT**: All user ID columns are changed from UUID to TEXT to accept Firebase user IDs
2. **Remove foreign key constraints**: Since Firebase user IDs don't exist in Supabase's auth.users table
3. **Update RLS policies**: Replace Supabase auth-based policies with service role policies (authorization is now handled in the application layer)

## After Running Migrations

1. Deploy your application changes to Vercel
2. The notifications should now work properly with Firebase authentication
3. The debug component should show "✅ Notifications table accessible"

## Important Notes

- These migrations are **irreversible** without a backup
- Make sure to test in a development environment first if possible
- The application now relies on the service role key for all database operations
- Authorization is handled at the application level using Firebase Auth 