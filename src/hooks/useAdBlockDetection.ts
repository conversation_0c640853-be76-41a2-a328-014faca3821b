import { useState, useEffect, useCallback } from 'react';
import { 
  detectAdBlocker, 
  storeAdBlockDetection, 
  getStoredAdBlockDetection, 
  shouldShowAdBlockMessage,
  type AdBlockDetectionResult 
} from '@/utils/adblock-detector';

interface UseAdBlockDetectionReturn {
  isDetecting: boolean;
  detectionResult: AdBlockDetectionResult | null;
  shouldShowModal: boolean;
  showModal: () => void;
  hideModal: () => void;
  redetect: () => Promise<void>;
}

/**
 * Hook to manage ad blocker detection state and modal visibility
 */
export const useAdBlockDetection = (): UseAdBlockDetectionReturn => {
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectionResult, setDetectionResult] = useState<AdBlockDetectionResult | null>(null);
  const [shouldShowModal, setShouldShowModal] = useState(false);

  // Initial detection when component mounts
  const performDetection = useCallback(async () => {
    if (typeof window === 'undefined') return;

    setIsDetecting(true);
    
    try {
      // Check if we have a recent stored result
      const storedResult = getStoredAdBlockDetection();
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      if (storedResult && storedResult.detectedAt > fiveMinutesAgo) {
        // Use stored result if it's recent (less than 5 minutes old)
        setDetectionResult(storedResult);
        
        // Show modal if ad blocker is detected and we should show the message
        if (storedResult.isBlocked && shouldShowAdBlockMessage()) {
          setShouldShowModal(true);
        }
      } else {
        // Perform fresh detection
        const result = await detectAdBlocker();
        setDetectionResult(result);
        storeAdBlockDetection(result);
        
        // Show modal if ad blocker is detected and we should show the message
        if (result.isBlocked && shouldShowAdBlockMessage()) {
          setShouldShowModal(true);
        }
      }
    } catch (error) {
      console.error('Ad blocker detection failed:', error);
      // On error, assume no ad blocker to avoid false positives
      const fallbackResult: AdBlockDetectionResult = {
        isBlocked: false,
        blockedServices: [],
        detectedAt: new Date(),
      };
      setDetectionResult(fallbackResult);
    } finally {
      setIsDetecting(false);
    }
  }, []);

  // Re-run detection
  const redetect = useCallback(async () => {
    setIsDetecting(true);
    
    try {
      const result = await detectAdBlocker();
      setDetectionResult(result);
      storeAdBlockDetection(result);
      
      // If ad blocker is no longer detected, hide modal
      if (!result.isBlocked) {
        setShouldShowModal(false);
      }
    } catch (error) {
      console.error('Re-detection failed:', error);
    } finally {
      setIsDetecting(false);
    }
  }, []);

  // Modal control functions
  const showModal = useCallback(() => {
    setShouldShowModal(true);
  }, []);

  const hideModal = useCallback(() => {
    setShouldShowModal(false);
  }, []);

  // Run initial detection
  useEffect(() => {
    // Small delay to ensure DOM is fully loaded
    const timer = setTimeout(() => {
      performDetection();
    }, 1000);

    return () => clearTimeout(timer);
  }, [performDetection]);

  // Log detection results for debugging
  useEffect(() => {
    if (detectionResult) {
      console.log('🛡️ Ad Blocker Detection Result:', {
        isBlocked: detectionResult.isBlocked,
        blockedServices: detectionResult.blockedServices,
        shouldShowModal,
        detectedAt: detectionResult.detectedAt,
      });
    }
  }, [detectionResult, shouldShowModal]);

  return {
    isDetecting,
    detectionResult,
    shouldShowModal,
    showModal,
    hideModal,
    redetect,
  };
};

export default useAdBlockDetection; 