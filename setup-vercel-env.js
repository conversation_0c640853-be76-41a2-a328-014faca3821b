// Script to set up environment variables in Vercel
const { execSync } = require("child_process");
const fs = require("fs");
const readline = require("readline");

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Function to prompt for environment
const promptForEnvironment = () => {
  return new Promise((resolve) => {
    rl.question(
      "Which environment do you want to set up? (production, preview, development): ",
      (answer) => {
        if (
          ["production", "preview", "development"].includes(
            answer.toLowerCase(),
          )
        ) {
          resolve(answer.toLowerCase());
        } else {
          console.log(
            "Invalid environment. Please choose production, preview, or development.",
          );
          resolve(promptForEnvironment());
        }
      },
    );
  });
};

// Function to read environment variables from .env file
const readEnvFile = (envFile) => {
  try {
    const envContent = fs.readFileSync(envFile, "utf8");
    const envVars = {};

    envContent.split("\n").forEach((line) => {
      // Skip comments and empty lines
      if (line.startsWith("#") || line.trim() === "") return;

      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        const key = match[1].trim();
        const value = match[2].trim();
        envVars[key] = value;
      }
    });

    return envVars;
  } catch (error) {
    console.error(`Error reading ${envFile}:`, error);
    return {};
  }
};

// Function to set environment variables in Vercel
const setVercelEnv = async (environment) => {
  const envFile = `.env.${environment}`;
  console.log(`Reading environment variables from ${envFile}...`);

  const envVars = readEnvFile(envFile);

  if (Object.keys(envVars).length === 0) {
    console.error(`No environment variables found in ${envFile}`);
    return;
  }

  console.log(
    `Setting up ${Object.keys(envVars).length} environment variables for ${environment}...`,
  );

  for (const [key, value] of Object.entries(envVars)) {
    try {
      console.log(`Setting ${key}...`);
      execSync(`vercel env add ${key} ${environment}`, { stdio: "inherit" });
      // Input the value (this will be prompted by Vercel CLI)
      // Note: This is a simplified approach and may not work for all cases
      // You might need to manually input the values when prompted
    } catch (error) {
      console.error(`Error setting ${key}:`, error);
    }
  }

  console.log(`Environment variables for ${environment} set up successfully!`);
};

// Main function
const main = async () => {
  try {
    console.log("Vercel Environment Variables Setup");
    console.log("=================================");

    const environment = await promptForEnvironment();
    await setVercelEnv(environment);

    rl.close();
  } catch (error) {
    console.error("Error:", error);
    rl.close();
    process.exit(1);
  }
};

// Run the main function
main();
