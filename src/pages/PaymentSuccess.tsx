import React, { useEffect, useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { useSubscription } from '../hooks/useSubscription';
import { trackSubscriptionEvent } from '../lib/posthog';

const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const { isSubscribed, subscriptionStatus, loading } = useSubscription();
  const [initialLoad, setInitialLoad] = useState(true);

  useEffect(() => {
    // Track successful payment completion
    if (sessionId && isSubscribed) {
      trackSubscriptionEvent.paymentSuccessful(1.99, 'USD', 'ad_free_monthly', {
        session_id: sessionId,
        subscription_status: subscriptionStatus,
        payment_timestamp: new Date().toISOString(),
      });
    }

    // Give the webhook a moment to process and update the subscription status
    const timer = setTimeout(() => {
      setInitialLoad(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, [sessionId, isSubscribed, subscriptionStatus]);

  if (loading || initialLoad) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing Your Subscription</h2>
          <p className="text-gray-600">Please wait while we confirm your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {isSubscribed ? (
          <>
            {/* Success State */}
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Ad-Free!</h1>
            <p className="text-gray-600 mb-6">
              Your subscription is now active. Enjoy an uninterrupted storytelling experience!
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-green-800">
                <strong>Status:</strong> {subscriptionStatus || 'Active'}
              </p>
              {sessionId && (
                <p className="text-xs text-green-700 mt-1">
                  Session: {sessionId.substring(0, 20)}...
                </p>
              )}
            </div>
          </>
        ) : (
          <>
            {/* Pending/Error State */}
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.864-.833-2.598 0L5.196 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Received</h1>
            <p className="text-gray-600 mb-6">
              We've received your payment and are setting up your subscription. This usually takes just a few moments.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <p className="text-sm text-yellow-800">
                If your subscription doesn't activate within a few minutes, please contact support.
              </p>
            </div>
          </>
        )}

        <div className="space-y-3">
          <Link
            to="/subscription"
            className="block w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-150"
          >
            View Subscription Details
          </Link>
          <Link
            to="/"
            className="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition duration-150"
          >
            Continue to Stories
          </Link>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Questions? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess;