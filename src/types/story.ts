// src/types/story.ts

export interface Author {
  id: string; // UUID
  username?: string | null;
  avatar_url?: string | null;
}

export interface Contribution {
  id: string; // UUID
  story_id: string; // UUID
  author: Author; // Embedded author information
  content: string;
  word_count: number;
  order: number;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
}

export interface Story {
  id: string; // UUID
  title: string;
  description?: string | null;
  author: Author; // Embedded author information
  status: 'in_progress' | 'completed' | 'abandoned';
  max_contributors: number;
  max_words_per_contribution: number;
  current_word_count: number;
  is_public: boolean;
  created_at: string; // ISO 8601 date string
  updated_at: string; // ISO 8601 date string
  completed_at?: string | null; // ISO 8601 date string
  last_contribution_at?: string | null; // ISO 8601 date string
  genre?: string | null;
  cover_image_url?: string | null;
  gotchaWord?: string | null; // Added gotchaWord field
  contributions: Contribution[]; // Array of contributions
  // Optional fields, potentially populated from helper functions or client-side state
  upvote_count?: number;
  downvote_count?: number;
  // currentUserVote?: 'upvote' | 'downvote' | null; // Example for tracking current user's vote
} 