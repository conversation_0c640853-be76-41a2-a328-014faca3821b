import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Story } from '@/types/story';
import { supabase } from '@/lib/supabase';
import StoryCard from './StoryCard'; // Assuming StoryCard is in the same directory
import { Loader2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

// Debounce utility function (can be moved to a utils file)
const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  const debounced = (...args: Parameters<F>) => {
    if (timeout !== null) {
      clearTimeout(timeout);
      timeout = null;
    }
    timeout = setTimeout(() => func(...args), waitFor);
  };

  return debounced as (...args: Parameters<F>) => void;
};

// Placeholder for available genres - this might come from a config or API later
const AVAILABLE_GENRES = ['all', 'Fiction', 'Fantasy', 'Sci-Fi', 'Mystery', 'Thriller', 'Romance', 'Historical'];
const STORY_STATUSES = ['all', 'in_progress', 'completed'];
const SORT_OPTIONS = [
  { value: 'created_at_desc', label: 'Newest' },
  { value: 'created_at_asc', label: 'Oldest' },
  { value: 'upvote_count_desc', label: 'Most Upvoted' },
];

const PAGE_LIMIT = 20; // Define page limit

const StoryGallery: React.FC = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState<boolean>(false); // True for initial load or when filters change
  const [loadingMore, setLoadingMore] = useState<boolean>(false); // True when fetching next page
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [genreFilter, setGenreFilter] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<string>('created_at_desc');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');

  const [page, setPage] = useState<number>(1); // Current page number (1-indexed)
  const [hasMore, setHasMore] = useState<boolean>(true); // Whether more stories can be loaded

  const loadMoreRef = useRef<HTMLDivElement | null>(null); // Ref for the loader element

  // Debounce search term update
  useEffect(() => {
    const handler = debounce((term: string) => {
      setDebouncedSearchTerm(term);
      setPage(1); // Reset page to 1 when search term changes
      setStories([]); // Clear existing stories for new search results
      setHasMore(true); // Assume there might be more results
    }, 500);
    handler(searchTerm);
    // Cleanup function to clear timeout if component unmounts or searchTerm changes quickly
    return () => {
      // This cleanup is a bit tricky with the current debounce structure.
      // A more robust debounce hook might handle this better.
    };
  }, [searchTerm]);

  // Effect for resetting page and stories when filters/sort order change
  useEffect(() => {
    setPage(1);
    setStories([]);
    setHasMore(true);
  }, [statusFilter, genreFilter, sortOrder]); // debouncedSearchTerm handled in its own effect

  const fetchStories = useCallback(async (isInitialLoad: boolean) => {
    if (isInitialLoad) {
      setLoading(true);
    } else {
      setLoadingMore(true);
    }
    setError(null);
    
    try {
      let p_sort_column = 'created_at';
      let p_sort_ascending = false;
      if (sortOrder === 'created_at_asc') p_sort_ascending = true;
      else if (sortOrder === 'created_at_desc') p_sort_ascending = false;
      else if (sortOrder === 'upvote_count_desc') p_sort_column = 'upvote_count';
      
      const rpcParams = {
        p_search_term: debouncedSearchTerm || '',
        p_status_filter: statusFilter,
        p_genre_filter: genreFilter,
        p_sort_column: p_sort_column,
        p_sort_ascending: p_sort_ascending,
        p_page_limit: PAGE_LIMIT,
        p_page_offset: (page - 1) * PAGE_LIMIT
      };

      const { data, error: fetchError } = await supabase.rpc('search_stories_with_content', rpcParams);

      if (fetchError) throw fetchError;

      const newStories = data?.map(s => ({...s, contributions: s.contributions || [] })) || [];
      
      setStories(prevStories => isInitialLoad ? (newStories as Story[]) : [...prevStories, ...(newStories as Story[])]);
      setHasMore(newStories.length === PAGE_LIMIT);
      if (newStories.length > 0 && !isInitialLoad) {
        setPage(prevPage => prevPage + 1);
      }

    } catch (err: any) {
      console.error("Error fetching stories:", err);
      setError(err.message || 'Failed to load stories');
      setHasMore(false); // Stop trying to load more if error
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      } else {
        setLoadingMore(false);
      }
    }
   
  }, [page, debouncedSearchTerm, statusFilter, genreFilter, sortOrder, supabase]); // page is a dependency now
  
  // Initial load and load when filters/search/sort change (page is reset)
  useEffect(() => {
    if (page === 1) { // Only fetch if page is 1 (initial load for current filters)
        fetchStories(true);
    }
  }, [page, fetchStories]); // fetchStories is memoized, page ensures it runs when page is reset to 1

  // Infinite scroll trigger function
  const loadMoreCallback = useCallback(() => {
    if (hasMore && !loading && !loadingMore) {
      // We don't set page here directly; fetchStories will increment it on success of non-initial load
      // but fetchStories depends on `page` state, so we need to ensure `page` is the *next* page to fetch.
      // This is tricky. Let's adjust `fetchStories` or how `page` is incremented.
      // For now, let's increment page here and let fetchStories use it.
      setPage(prevPage => prevPage + 1); 
      // And call fetchStories for non-initial load
      // fetchStories(false); // This will now use the incremented page from the line above. This might cause a double fetch if not careful.
      // Simpler: fetchStories depends on page. When page changes, the useEffect for page > 1 (below) will trigger.
    }
  }, [hasMore, loading, loadingMore]);

  // New useEffect to fetch more data when page changes (for pages > 1)
  useEffect(() => {
    if (page > 1 && hasMore && !loading && !loadingMore) {
        fetchStories(false); // Fetch for subsequent pages
    }
  }, [page, hasMore, loading, loadingMore, fetchStories]);

  useIntersectionObserver(loadMoreRef, {}, loadMoreCallback, [hasMore, loading, loadingMore]);

  // Filter controls are moved outside conditional rendering of gallery content
  const filterControls = (
    <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center justify-between flex-wrap">
      <div className="flex flex-wrap gap-x-4 gap-y-2 items-end">
        <div>
          <Label htmlFor="search-stories" className="text-sm font-medium">Search</Label>
          <Input
            id="search-stories"
            type="text"
            placeholder="Search title or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:w-[200px] mt-1"
          />
        </div>
        <div>
          <Label htmlFor="status-filter" className="text-sm font-medium mr-2">Status</Label>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger id="status-filter" className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              {STORY_STATUSES.map(status => (
                <SelectItem key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="genre-filter" className="text-sm font-medium mr-2">Genre</Label>
          <Select value={genreFilter} onValueChange={setGenreFilter}>
            <SelectTrigger id="genre-filter" className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by genre" />
            </SelectTrigger>
            <SelectContent>
              {AVAILABLE_GENRES.map(genre => (
                <SelectItem key={genre} value={genre}>
                  {genre.charAt(0).toUpperCase() + genre.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="sort-order" className="text-sm font-medium mr-2">Sort by</Label>
          <Select value={sortOrder} onValueChange={setSortOrder}>
            <SelectTrigger id="sort-order" className="w-full sm:w-[180px]">
              <SelectValue placeholder="Sort stories" />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );

  let content;
  if (loading && stories.length === 0) { // Show main loader only if initial load and no stories yet
    content = (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="h-10 w-10 animate-spin text-blue-500" />
        <p className="ml-3 text-lg">Loading stories...</p>
      </div>
    );
  } else if (error) {
    content = <p className="text-center text-red-500 py-10">Error loading stories: {error}</p>;
  } else if (stories.length === 0 && !loadingMore) { // Changed condition to !loadingMore
    content = <p className="text-center text-gray-500 py-10">No stories match your filters. Why not create one?</p>;
  } else {
    content = (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stories.map((story) => (
          <StoryCard key={story.id} story={story} />
        ))}
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {filterControls}
      {content}
      {/* Loader for infinite scroll */} 
      {loadingMore && (
        <div className="flex justify-center items-center py-6 mt-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="ml-2 text-md">Loading more stories...</p>
        </div>
      )}
      {/* Element to trigger infinite scroll */} 
      {!loading && !loadingMore && hasMore && (
          <div ref={loadMoreRef} style={{ height: '10px' }} />
      )}
      {!hasMore && stories.length > 0 && (
          <p className="text-center text-gray-400 py-6 mt-4">You've reached the end of the stories!</p>
      )}
    </div>
  );
};

export default StoryGallery; 