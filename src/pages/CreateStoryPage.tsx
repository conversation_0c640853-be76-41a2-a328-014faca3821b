import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ContributionMode } from '@/services/realTimeService';
import { useAuth } from '@/contexts/auth';

const CreateStoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [contributionMode, setContributionMode] = useState<ContributionMode>(ContributionMode.WORD);
  const [wordsPerContribution, setWordsPerContribution] = useState(3);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Title is required');
      return;
    }

    if (!isAuthenticated || !user) {
      setError('Please log in to create a story');
      navigate('/login');
      return;
    }
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      const response = await fetch('/api/v1/stories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          contributionMode,
          wordsPerContribution: contributionMode === ContributionMode.MULTI_WORD ? wordsPerContribution : undefined,
          userId: user.id,
          username: user.username,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create story');
      }
      
      const result = await response.json();
      
      // Navigate to the new story page
      navigate(`/stories/${result.id}`);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">Create a New Story</h1>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-3 text-sm text-red-700 bg-red-100 rounded-md">
              {error}
            </div>
          )}
          
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Title
            </label>
            <input
              id="title"
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter story title"
              required
            />
          </div>
          
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description (optional)
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter story description"
              rows={3}
            />
          </div>
          
          <div>
            <label htmlFor="contributionMode" className="block text-sm font-medium text-gray-700 mb-1">
              Contribution Mode
            </label>
            <select
              id="contributionMode"
              value={contributionMode}
              onChange={(e) => setContributionMode(e.target.value as ContributionMode)}
              className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={ContributionMode.WORD}>Word-by-Word</option>
              <option value={ContributionMode.MULTI_WORD}>Multi-Word</option>
              <option value={ContributionMode.SENTENCE}>Sentence</option>
              <option value={ContributionMode.PARAGRAPH}>Paragraph</option>
            </select>
            <p className="mt-1 text-sm text-gray-500">
              {contributionMode === ContributionMode.WORD && 'Each contributor adds a single word to the story.'}
              {contributionMode === ContributionMode.MULTI_WORD && 'Each contributor adds multiple words to the story.'}
              {contributionMode === ContributionMode.SENTENCE && 'Each contributor adds a complete sentence to the story.'}
              {contributionMode === ContributionMode.PARAGRAPH && 'Each contributor adds a paragraph to the story.'}
            </p>
          </div>
          
          {contributionMode === ContributionMode.MULTI_WORD && (
            <div>
              <label htmlFor="wordsPerContribution" className="block text-sm font-medium text-gray-700 mb-1">
                Words Per Contribution
              </label>
              <input
                id="wordsPerContribution"
                type="number"
                min={2}
                max={10}
                value={wordsPerContribution}
                onChange={(e) => setWordsPerContribution(parseInt(e.target.value))}
                className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="mt-1 text-sm text-gray-500">
                Maximum number of words each contributor can add per turn (2-10).
              </p>
            </div>
          )}
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting || !title.trim()}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSubmitting ? 'Creating...' : 'Create Story'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateStoryPage;
