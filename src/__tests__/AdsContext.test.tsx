import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import { AdsProvider, useAds, AdsContext } from '@/contexts/AdsContext';
import { useAuth } from '@/contexts/auth';

// Mock the useAuth hook
vi.mock('@/contexts/auth', () => ({
  useAuth: vi.fn(),
}));

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_ADSENSE_CLIENT_ID: 'ca-pub-test-client-id',
  },
}));

// Test component to access the context
const TestComponent: React.FC = () => {
  const {
    adSettings,
    setAdSettings,
    isAdFree,
    isAdFreeUser,
    toggleAdFreeStatus,
    shouldShowAd,
  } = useAds();

  return (
    <div>
      <div data-testid="show-ads">{adSettings.showAds.toString()}</div>
      <div data-testid="ad-density">{adSettings.adDensity}</div>
      <div data-testid="is-ad-free">{isAdFree.toString()}</div>
      <div data-testid="is-ad-free-user">{isAdFreeUser.toString()}</div>
      <div data-testid="should-show-sidebar">{shouldShowAd('sidebar').toString()}</div>
      <div data-testid="should-show-header">{shouldShowAd('header').toString()}</div>
      <button
        data-testid="toggle-ad-free"
        onClick={() => toggleAdFreeStatus()}
      >
        Toggle Ad Free
      </button>
      <button
        data-testid="set-low-density"
        onClick={() => setAdSettings({ showAds: true, adDensity: 'low' })}
      >
        Set Low Density
      </button>
    </div>
  );
};

// Wrapper component for testing
const TestWrapper: React.FC<{ children: React.ReactNode; route?: string }> = ({
  children,
  route = '/',
}) => (
  <MemoryRouter initialEntries={[route]}>
    <AdsProvider>{children}</AdsProvider>
  </MemoryRouter>
);

describe('AdsContext', () => {
  const mockUseAuth = useAuth as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    
    // Mock document.createElement and appendChild for AdSense script loading
    const mockScript = document.createElement('script');
    const mockAppendChild = vi.fn();
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockScript);
    vi.spyOn(document.head, 'appendChild').mockImplementation(mockAppendChild);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Default State', () => {
    it('should provide default ad settings for unauthenticated users', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        isAuthenticated: false,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      expect(screen.getByTestId('ad-density')).toHaveTextContent('medium');
      expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
      expect(screen.getByTestId('is-ad-free-user')).toHaveTextContent('false');
    });

    it('should load Google AdSense script on mount', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        isAuthenticated: false,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(document.createElement).toHaveBeenCalledWith('script');
      expect(document.head.appendChild).toHaveBeenCalled();
    });
  });

  describe('Ad-Free User Detection', () => {
    it('should detect ad-free users with wordsmith tier', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'wordsmith' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('true');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should detect ad-free users with storyteller tier', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'storyteller' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('true');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should detect ad-free users with authors-guild tier', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'authors-guild' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('true');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should show ads for free tier users', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });
    });

    it('should show ads for users without tier', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1' }, // No tier property
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });
    });
  });

  describe('Page-Based Ad Control', () => {
    it('should disable ads on login page', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper route="/login">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should disable ads on register page', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper route="/register">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should disable ads on subscription page', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper route="/subscription">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should disable ads on payment page', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper route="/payment">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });
    });

    it('should show ads on regular pages for free users', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper route="/stories">
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });
    });
  });

  describe('Ad Density Logic', () => {
    it('should show all ads for medium density', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('should-show-sidebar')).toHaveTextContent('true');
        expect(screen.getByTestId('should-show-header')).toHaveTextContent('true');
      });
    });

    it('should show only sidebar ads for low density', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Wait for initial render, then change to low density
      await waitFor(() => {
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });

      // Click to set low density
      screen.getByTestId('set-low-density').click();

      await waitFor(() => {
        expect(screen.getByTestId('should-show-sidebar')).toHaveTextContent('true');
        expect(screen.getByTestId('should-show-header')).toHaveTextContent('false');
      });
    });

    it('should not show any ads for ad-free users regardless of density', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'wordsmith' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('should-show-sidebar')).toHaveTextContent('false');
        expect(screen.getByTestId('should-show-header')).toHaveTextContent('false');
      });
    });
  });

  describe('Toggle Ad-Free Status', () => {
    it('should toggle ad-free status', async () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'free' },
        isAuthenticated: true,
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Initially should show ads
      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
      });

      // Toggle ad-free status
      screen.getByTestId('toggle-ad-free').click();

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('true');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('false');
      });

      // Toggle back
      screen.getByTestId('toggle-ad-free').click();

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });
    });
  });

  describe('Context Provider', () => {
    it('should provide all required context values', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        isAuthenticated: false,
      });

      const TestContextConsumer: React.FC = () => {
        const context = useAds();
        
        return (
          <div>
            <div data-testid="has-ad-settings">{typeof context.adSettings === 'object' ? 'true' : 'false'}</div>
            <div data-testid="has-set-ad-settings">{typeof context.setAdSettings === 'function' ? 'true' : 'false'}</div>
            <div data-testid="has-is-ad-free">{typeof context.isAdFree === 'boolean' ? 'true' : 'false'}</div>
            <div data-testid="has-is-ad-free-user">{typeof context.isAdFreeUser === 'boolean' ? 'true' : 'false'}</div>
            <div data-testid="has-toggle-ad-free">{typeof context.toggleAdFreeStatus === 'function' ? 'true' : 'false'}</div>
            <div data-testid="has-should-show-ad">{typeof context.shouldShowAd === 'function' ? 'true' : 'false'}</div>
          </div>
        );
      };

      render(
        <TestWrapper>
          <TestContextConsumer />
        </TestWrapper>
      );

      expect(screen.getByTestId('has-ad-settings')).toHaveTextContent('true');
      expect(screen.getByTestId('has-set-ad-settings')).toHaveTextContent('true');
      expect(screen.getByTestId('has-is-ad-free')).toHaveTextContent('true');
      expect(screen.getByTestId('has-is-ad-free-user')).toHaveTextContent('true');
      expect(screen.getByTestId('has-toggle-ad-free')).toHaveTextContent('true');
      expect(screen.getByTestId('has-should-show-ad')).toHaveTextContent('true');
    });
  });

  describe('Authentication State Changes', () => {
    it('should update ad settings when user logs out', async () => {
      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Initially authenticated with ad-free tier
      mockUseAuth.mockReturnValue({
        user: { id: '1', tier: 'wordsmith' },
        isAuthenticated: true,
      });

      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('true');
      });

      // User logs out
      mockUseAuth.mockReturnValue({
        user: null,
        isAuthenticated: false,
      });

      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('is-ad-free')).toHaveTextContent('false');
        expect(screen.getByTestId('show-ads')).toHaveTextContent('true');
      });
    });
  });
}); 