import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Loader2, Send, Users, Search, ArrowRight, Coins } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth';
import { useDebounce } from '@/hooks/use-debounce';

interface User {
  id: string;
  username: string;
  avatar_url?: string;
  email?: string;
}

interface TokenTransferDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance?: number;
  onTransferComplete?: (amount: number, recipient: string) => void;
}

export const TokenTransferDialog: React.FC<TokenTransferDialogProps> = ({
  isOpen,
  onClose,
  currentBalance = 0,
  onTransferComplete,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [amount, setAmount] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [recentRecipients, setRecentRecipients] = useState<User[]>([]);

  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Load recent recipients on mount
  useEffect(() => {
    const loadRecentRecipients = async () => {
      if (!user) return;

      try {
        const response = await fetch(`/api/v1/tokens/recent-recipients?userId=${user.id}`);
        if (response.ok) {
          const recipients = await response.json();
          setRecentRecipients(recipients.slice(0, 5)); // Show last 5 recipients
        }
      } catch (error) {
        console.error('Error loading recent recipients:', error);
      }
    };

    if (isOpen) {
      loadRecentRecipients();
    }
  }, [isOpen, user]);

  // Search for users
  useEffect(() => {
    const searchUsers = async () => {
      if (!debouncedSearchQuery.trim() || debouncedSearchQuery.length < 2) {
        setSearchResults([]);
        return;
      }

      setSearchLoading(true);
      try {
        const response = await fetch(
          `/api/v1/users/search?q=${encodeURIComponent(debouncedSearchQuery)}&limit=10`
        );
        
        if (response.ok) {
          const users = await response.json();
          // Filter out current user
          setSearchResults(users.filter((u: User) => u.id !== user?.id));
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        console.error('Error searching users:', error);
        setSearchResults([]);
      } finally {
        setSearchLoading(false);
      }
    };

    searchUsers();
  }, [debouncedSearchQuery, user?.id]);

  const handleUserSelect = (selectedUser: User) => {
    setSelectedUser(selectedUser);
    setSearchQuery('');
    setSearchResults([]);
    setError('');
  };

  const handleAmountChange = (value: string) => {
    const numValue = parseInt(value);
    if (value === '' || (numValue > 0 && numValue <= currentBalance)) {
      setAmount(value);
      setError('');
    } else if (numValue > currentBalance) {
      setError(`You only have ${currentBalance} tokens available`);
    } else if (numValue <= 0) {
      setError('Amount must be greater than 0');
    }
  };

  const handleTransfer = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to transfer tokens.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedUser) {
      setError('Please select a recipient');
      return;
    }

    if (!amount || parseInt(amount) <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    const transferAmount = parseInt(amount);
    if (transferAmount > currentBalance) {
      setError(`You only have ${currentBalance} tokens available`);
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/v1/tokens/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromUserId: user.id,
          toUserId: selectedUser.id,
          amount: transferAmount,
          message: message.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Transfer failed');
      }

      const result = await response.json();

      toast({
        title: 'Transfer Successful',
        description: `Successfully transferred ${transferAmount} tokens to ${selectedUser.username}`,
      });

      onTransferComplete?.(transferAmount, selectedUser.username);
      onClose();

      // Reset form
      setSelectedUser(null);
      setAmount('');
      setMessage('');
      setSearchQuery('');

    } catch (err) {
      console.error('Error transferring tokens:', err);
      setError(err instanceof Error ? err.message : 'Failed to transfer tokens');
      
      toast({
        title: 'Transfer Failed',
        description: 'Unable to complete the transfer. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getUserInitials = (username: string) => {
    return username.slice(0, 2).toUpperCase();
  };

  const clearSelection = () => {
    setSelectedUser(null);
    setSearchQuery('');
    setError('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="w-5 h-5 text-blue-500" />
            Transfer Tokens
          </DialogTitle>
          <DialogDescription>
            Send tokens to another user. Your current balance: <span className="font-semibold">{currentBalance} tokens</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recipient Selection */}
          <div>
            <Label className="text-base font-semibold">Select Recipient</Label>
            
            {selectedUser ? (
              <Card className="mt-2">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={selectedUser.avatar_url} />
                        <AvatarFallback>{getUserInitials(selectedUser.username)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{selectedUser.username}</div>
                        {selectedUser.email && (
                          <div className="text-sm text-muted-foreground">{selectedUser.email}</div>
                        )}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={clearSelection}>
                      Change
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="mt-2 space-y-4">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search by username or email..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                  {searchLoading && (
                    <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin" />
                  )}
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Search Results</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {searchResults.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer"
                          onClick={() => handleUserSelect(user)}
                        >
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={user.avatar_url} />
                            <AvatarFallback className="text-xs">{getUserInitials(user.username)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{user.username}</div>
                            {user.email && (
                              <div className="text-xs text-muted-foreground">{user.email}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {/* Recent Recipients */}
                {recentRecipients.length > 0 && !searchQuery && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        Recent Recipients
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      {recentRecipients.map((user) => (
                        <div
                          key={user.id}
                          className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted cursor-pointer"
                          onClick={() => handleUserSelect(user)}
                        >
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={user.avatar_url} />
                            <AvatarFallback className="text-xs">{getUserInitials(user.username)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{user.username}</div>
                          </div>
                          <Badge variant="secondary" className="text-xs">Recent</Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>

          {/* Amount Input */}
          <div>
            <Label htmlFor="amount" className="text-base font-semibold">Amount</Label>
            <div className="mt-2 relative">
              <Coins className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                id="amount"
                type="number"
                min="1"
                max={currentBalance}
                value={amount}
                onChange={(e) => handleAmountChange(e.target.value)}
                placeholder="Enter token amount"
                className="pl-10"
              />
            </div>
            <div className="mt-1 text-sm text-muted-foreground">
              Maximum: {currentBalance} tokens
            </div>
          </div>

          {/* Optional Message */}
          <div>
            <Label htmlFor="message" className="text-base font-semibold">Message (Optional)</Label>
            <Textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Add a note with your transfer..."
              className="mt-2"
              rows={3}
              maxLength={200}
            />
            <div className="mt-1 text-sm text-muted-foreground">
              {message.length}/200 characters
            </div>
          </div>

          {/* Transfer Summary */}
          {selectedUser && amount && parseInt(amount) > 0 && (
            <Card className="bg-muted/50">
              <CardContent className="pt-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span>Transfer to:</span>
                    <span className="font-medium">{selectedUser.username}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">{amount} tokens</span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>Remaining balance:</span>
                    <span>{currentBalance - parseInt(amount)} tokens</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleTransfer}
            disabled={
              loading ||
              !selectedUser ||
              !amount ||
              parseInt(amount) <= 0 ||
              parseInt(amount) > currentBalance
            }
            className="min-w-[120px]"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Transferring...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Transfer {amount} Tokens
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};