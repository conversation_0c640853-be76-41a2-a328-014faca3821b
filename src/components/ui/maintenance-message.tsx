import React from "react";
import { useAuth } from "@/contexts/auth";
import { <PERSON>ertCircle, WrenchIcon } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export const MaintenanceMessage: React.FC = () => {
  const { systemMaintenance } = useAuth();

  if (!systemMaintenance.enabled) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mx-auto max-w-3xl mt-4">
      <div className="flex items-start gap-2">
        <WrenchIcon className="h-5 w-5 mt-0.5" />
        <div>
          <AlertTitle className="text-lg font-semibold mb-1">
            System Maintenance
          </AlertTitle>
          <AlertDescription className="text-sm">
            {systemMaintenance.message ||
              "The system is currently under maintenance. Please try again later."}
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
};
