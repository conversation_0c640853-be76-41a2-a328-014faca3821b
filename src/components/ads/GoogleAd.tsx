// Declare global window interface augmentation
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

import React, { useEffect, useRef, useState } from "react";
import { useAds } from "@/contexts/AdsContext";

interface GoogleAdProps {
  placement: string;
  slot: string;
  format?: string;
  responsive?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const getAdSize = (placement: string) => {
  switch (placement) {
    case "header":
      return { width: "728px", height: "90px" };
    case "sidebar":
      return { width: "300px", height: "600px" };
    case "footer":
      return { width: "728px", height: "90px" };
    case "content":
      return { width: "300px", height: "250px" };
    default:
      return { width: "300px", height: "250px" };
  }
};

const GoogleAd: React.FC<GoogleAdProps> = ({
  placement,
  slot,
  format = "auto",
  responsive = true,
  className = "",
  style = {},
}) => {
  const { shouldShowAd } = useAds();
  const adRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    console.log('🔍 GoogleAd Debug:', {
      placement,
      slot,
      shouldShowAd: shouldShowAd(placement),
      hasError,
      isInitialized,
      clientId: import.meta.env.VITE_ADSENSE_CLIENT_ID
    });
    
    if (!shouldShowAd(placement) || !slot || hasError || isInitialized) return;

    // Wait for next frame to ensure DOM is ready
    requestAnimationFrame(() => {
      // Double check container exists and has size
      if (adRef.current && !isInitialized) {
        const rect = adRef.current.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          try {
            console.log('🚀 Initializing AdSense for placement:', placement);
        (window.adsbygoogle = window.adsbygoogle || []).push({});
            setIsInitialized(true);
      } catch (error) {
        console.error("AdSense error:", error);
        setHasError(true);
      }
    }
      }
    });
  }, [shouldShowAd, placement, slot, hasError, isInitialized]);

  if (!shouldShowAd(placement) || !slot || hasError) {
    return null;
  }

  const adSize = getAdSize(placement);

  return (
    <div 
      ref={adRef}
      className={`google-ad-container ${className}`}
      style={{ 
        minWidth: adSize.width,
        minHeight: adSize.height,
        ...style 
      }}
      data-testid="google-ad"
    >
        <ins
          className="adsbygoogle"
        style={{ 
          display: "block",
          width: adSize.width,
          height: adSize.height,
          ...style 
        }}
          data-ad-client={import.meta.env.VITE_ADSENSE_CLIENT_ID}
          data-ad-slot={slot}
          data-ad-format={format}
          data-full-width-responsive={responsive}
        />
    </div>
  );
};

export default GoogleAd;
