import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { <PERSON>, <PERSON>Header, CardFooter, CardTitle, CardDescription, CardContent } from './card';
import { Button } from './button';
import { Badge } from './badge';
import { User, Clock, Users } from 'lucide-react';

const meta: Meta<typeof Card> = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible card component for displaying content with header, body, and footer sections.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => (
    <Card className="w-96">
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>This is a card description that provides context about the content.</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          This is the main content area of the card. You can put any content here including text, images, forms, or other components.
        </p>
      </CardContent>
      <CardFooter>
        <Button>Action</Button>
      </CardFooter>
    </Card>
  ),
};

export const Simple: Story = {
  render: () => (
    <Card className="w-80">
      <CardContent className="p-6">
        <p>A simple card with just content and no header or footer.</p>
      </CardContent>
    </Card>
  ),
};

export const WithoutFooter: Story = {
  render: () => (
    <Card className="w-96">
      <CardHeader>
        <CardTitle>Story Progress</CardTitle>
        <CardDescription>Track your collaborative writing progress</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Words written</span>
            <span className="font-medium">247 / 500</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '49.4%' }}></div>
          </div>
        </div>
      </CardContent>
    </Card>
  ),
};

export const StoryCard: Story = {
  render: () => (
    <Card className="w-80 hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">The Mysterious Forest</CardTitle>
          <Badge variant="secondary">Active</Badge>
        </div>
        <CardDescription>
          A collaborative adventure story set in an enchanted forest where every contributor adds to the mystery.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <User className="h-4 w-4" />
            <span>Created by WriterBot</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>2 hours ago</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            <span>12 contributors</span>
          </div>
          <div className="text-sm">
            <strong>Latest:</strong> "The old oak tree creaked ominously as Sarah approached, its gnarled branches seeming to reach..."
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button className="flex-1">Join Story</Button>
        <Button variant="outline">View</Button>
      </CardFooter>
    </Card>
  ),
  parameters: {
    docs: {
      description: {
        story: 'A story card as it would appear in the gallery, showing story details and actions.',
      },
    },
  },
};

export const UserProfileCard: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
            JD
          </div>
          <div>
            <CardTitle>John Doe</CardTitle>
            <CardDescription>@johndoe</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Passionate storyteller who loves collaborative writing and creative adventures.
          </p>
          <div className="grid grid-cols-3 gap-4 text-center text-sm">
            <div>
              <div className="font-bold">23</div>
              <div className="text-muted-foreground">Stories</div>
            </div>
            <div>
              <div className="font-bold">156</div>
              <div className="text-muted-foreground">Contributions</div>
            </div>
            <div>
              <div className="font-bold">89</div>
              <div className="text-muted-foreground">Followers</div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full">Follow</Button>
      </CardFooter>
    </Card>
  ),
};

export const PricingCard: Story = {
  render: () => (
    <Card className="w-80 border-2 border-blue-200">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Pro Plan</CardTitle>
        <CardDescription>Perfect for active writers</CardDescription>
        <div className="text-3xl font-bold text-blue-600">$9<span className="text-sm text-muted-foreground">/month</span></div>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Unlimited story creation
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Priority support
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Advanced analytics
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Custom story templates
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            Export options
          </li>
        </ul>
      </CardContent>
      <CardFooter>
        <Button className="w-full" size="lg">Choose Pro</Button>
      </CardFooter>
    </Card>
  ),
};

export const NotificationCard: Story = {
  render: () => (
    <Card className="w-96 border-orange-200 bg-orange-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
          <div className="flex-1">
            <p className="text-sm font-medium">New contribution to "Space Adventure"</p>
            <p className="text-sm text-muted-foreground">Sarah added: "The spaceship's engines hummed to life as they prepared for the journey to Mars."</p>
            <p className="text-xs text-muted-foreground mt-1">2 minutes ago</p>
          </div>
        </div>
      </CardContent>
    </Card>
  ),
};

export const LoadingCard: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </div>
      </CardContent>
      <CardFooter>
        <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
      </CardFooter>
    </Card>
  ),
  parameters: {
    docs: {
      description: {
        story: 'A loading state card with skeleton placeholders.',
      },
    },
  },
};