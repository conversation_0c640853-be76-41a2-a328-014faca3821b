import { describe, it, expect } from 'vitest';
import { ContributionValidator } from '@/services/contributionValidator';
import { ContributionMode } from '@/services/realTimeService';

describe('ContributionValidator', () => {
  describe('Word Mode Validation', () => {
    it('should validate a single word correctly', () => {
      const result = ContributionValidator.validate('hello', ContributionMode.WORD);
      expect(result.isValid).toBe(true);
    });

    it('should reject multiple words in word mode', () => {
      const result = ContributionValidator.validate('hello world', ContributionMode.WORD);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('single word');
    });

    it('should reject empty content', () => {
      const result = ContributionValidator.validate('', ContributionMode.WORD);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('empty');
    });

    it('should reject content with only whitespace', () => {
      const result = ContributionValidator.validate('   ', ContributionMode.WORD);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('empty');
    });
  });

  describe('Multi-Word Mode Validation', () => {
    it('should validate multiple words within the limit', () => {
      const result = ContributionValidator.validate('hello beautiful world', ContributionMode.MULTI_WORD);
      expect(result.isValid).toBe(true);
    });

    it('should reject too many words', () => {
      const result = ContributionValidator.validate(
        'this is a very long sentence with too many words', 
        ContributionMode.MULTI_WORD
      );
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('maximum');
    });

    it('should respect custom word limit', () => {
      // Custom limit of 2 words
      const result = ContributionValidator.validate(
        'three words here', 
        ContributionMode.MULTI_WORD,
        2
      );
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('maximum of 2 words');
    });

    it('should tokenize multi-word content correctly', () => {
      const tokens = ContributionValidator.tokenizeMultiWord('hello beautiful world');
      expect(tokens).toEqual(['hello', 'beautiful', 'world']);
    });

    it('should handle extra whitespace in tokenization', () => {
      const tokens = ContributionValidator.tokenizeMultiWord('  hello   beautiful   world  ');
      expect(tokens).toEqual(['hello', 'beautiful', 'world']);
    });
  });

  describe('Sentence Mode Validation', () => {
    it('should validate a proper sentence', () => {
      const result = ContributionValidator.validate(
        'This is a complete sentence.',
        ContributionMode.SENTENCE
      );
      expect(result.isValid).toBe(true);
    });

    it('should reject a sentence without ending punctuation', () => {
      const result = ContributionValidator.validate(
        'This is not a complete sentence',
        ContributionMode.SENTENCE
      );
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('punctuation');
    });

    it('should validate sentences with different ending punctuation', () => {
      expect(ContributionValidator.validate(
        'Is this a question?',
        ContributionMode.SENTENCE
      ).isValid).toBe(true);

      expect(ContributionValidator.validate(
        'This is exciting!',
        ContributionMode.SENTENCE
      ).isValid).toBe(true);
    });
  });

  describe('Paragraph Mode Validation', () => {
    it('should validate a proper paragraph', () => {
      const result = ContributionValidator.validate(
        'This is a paragraph. It has multiple sentences. Each one ends with proper punctuation.',
        ContributionMode.PARAGRAPH
      );
      expect(result.isValid).toBe(true);
    });

    it('should reject content without any complete sentences', () => {
      const result = ContributionValidator.validate(
        'this is not a proper paragraph without any punctuation',
        ContributionMode.PARAGRAPH
      );
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('complete sentence');
    });
  });
});
