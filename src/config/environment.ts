/**
 * Environment configuration for the application
 * This allows us to switch between API calls and mock data based on the environment
 */

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV || process.env.NODE_ENV === 'development';

// Check if we're in the demo mode (for testing contribution modes)
const isDemo = window.location.pathname.includes('contribution-modes-demo');

export const config = {
  // Use API calls in production, but use mock data in the demo
  useRealApi: !isDemo && !isDevelopment,
  
  // API endpoints
  api: {
    baseUrl: isDevelopment ? 'http://localhost:3000/api/v1' : '/api/v1',
    endpoints: {
      contribute: (storyId: string) => `/stories/${storyId}/contribute`,
      story: (storyId: string) => `/stories/${storyId}`,
      sessions: (storyId: string) => `/stories/${storyId}/sessions`,
    }
  },
  
  // Firebase configuration (REMOVED)
  // firebase: {
  //   useRealFirebase: !isDemo && !isDevelopment,
  // }
};
