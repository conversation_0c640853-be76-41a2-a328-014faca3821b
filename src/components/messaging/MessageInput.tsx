import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  Plus, 
  Zap, 
  RotateCcw, 
  Crown, 
  Smile,
  Paperclip,
  Mic
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

export interface MessageInputProps {
  storyId: string;
  placeholder?: string;
  maxLength?: number;
  wordsPerContribution?: number;
  contributionMode?: 'word' | 'sentence' | 'paragraph';
  userTokens?: number;
  isTyping?: boolean;
  disabled?: boolean;
  onSendMessage: (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => Promise<void>;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  onInviteContributor?: () => void;
  className?: string;
}

const specialActions = [
  {
    type: 'gotcha' as const,
    icon: Zap,
    label: 'Gotcha Word',
    description: 'Add a surprising twist to the story',
    cost: 2,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 hover:bg-yellow-100 dark:bg-yellow-950 dark:hover:bg-yellow-900',
  },
  {
    type: 'reverse' as const,
    icon: RotateCcw,
    label: 'Reverse Move',
    description: 'Undo the last contribution and add your own',
    cost: 5,
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 hover:bg-purple-100 dark:bg-purple-950 dark:hover:bg-purple-900',
  },
  {
    type: 'golden' as const,
    icon: Crown,
    label: 'Golden Contribution',
    description: 'Highlight this as a premium contribution',
    cost: 3,
    color: 'text-amber-600 dark:text-amber-400',
    bgColor: 'bg-amber-50 hover:bg-amber-100 dark:bg-amber-950 dark:hover:bg-amber-900',
  },
];

export const MessageInput: React.FC<MessageInputProps> = ({
  storyId,
  placeholder = 'Add your contribution to the story...',
  maxLength = 500,
  wordsPerContribution = 1,
  contributionMode = 'word',
  userTokens = 0,
  isTyping = false,
  disabled = false,
  onSendMessage,
  onTypingStart,
  onTypingStop,
  onInviteContributor,
  className,
}) => {
  const [message, setMessage] = useState('');
  const [selectedSpecialType, setSelectedSpecialType] = useState<'gotcha' | 'reverse' | 'golden' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSpecialActions, setShowSpecialActions] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  }, [message]);

  // Handle typing indicators
  const handleInputChange = (value: string) => {
    setMessage(value);

    // Start typing indicator
    if (!isTyping && value.length > 0) {
      onTypingStart?.();
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing indicator after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      onTypingStop?.();
    }, 2000);
  };

  // Validate word count based on contribution mode
  const validateWordCount = (text: string): boolean => {
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    
    switch (contributionMode) {
      case 'word':
        return words.length <= wordsPerContribution;
      case 'sentence':
        return words.length <= 50; // Reasonable sentence limit
      case 'paragraph':
        return words.length <= 200; // Reasonable paragraph limit
      default:
        return true;
    }
  };

  const getWordCount = () => {
    return message.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const getPlaceholderText = () => {
    switch (contributionMode) {
      case 'word':
        return wordsPerContribution === 1 
          ? 'Add your word...' 
          : `Add your ${wordsPerContribution} words...`;
      case 'sentence':
        return 'Add your sentence to the story...';
      case 'paragraph':
        return 'Add your paragraph to the story...';
      default:
        return placeholder;
    }
  };

  const canSend = () => {
    const trimmedMessage = message.trim();
    const isValidLength = trimmedMessage.length > 0 && trimmedMessage.length <= maxLength;
    const isValidWordCount = validateWordCount(trimmedMessage);
    const hasEnoughTokens = selectedSpecialType 
      ? userTokens >= (specialActions.find(a => a.type === selectedSpecialType)?.cost || 0)
      : true;
    
    return isValidLength && isValidWordCount && hasEnoughTokens && !isLoading;
  };

  const handleSend = async () => {
    if (!canSend()) return;

    setIsLoading(true);
    onTypingStop?.();

    try {
      await onSendMessage(message.trim(), selectedSpecialType || undefined);
      setMessage('');
      setSelectedSpecialType(null);
      setShowSpecialActions(false);
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const selectedAction = selectedSpecialType 
    ? specialActions.find(a => a.type === selectedSpecialType)
    : null;

  return (
    <TooltipProvider>
      <div className={cn('border-t bg-background p-4', className)}>
        {/* Special Action Indicator */}
        {selectedAction && (
          <div className="mb-3 flex items-center gap-2">
            <div className={cn(
              'flex items-center gap-2 px-3 py-1.5 rounded-full text-sm border',
              selectedAction.bgColor,
              selectedAction.color
            )}>
              <selectedAction.icon className="w-4 h-4" />
              <span className="font-medium">{selectedAction.label}</span>
              <Badge variant="secondary" className="text-xs">
                {selectedAction.cost} tokens
              </Badge>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 ml-1"
                onClick={() => setSelectedSpecialType(null)}
              >
                ×
              </Button>
            </div>
          </div>
        )}

        <div className="flex items-end gap-2">
          {/* Plus Button for Invites */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 flex-shrink-0"
                onClick={onInviteContributor}
                aria-label="Invite contributors"
              >
                <Plus className="w-5 h-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Invite contributors</TooltipContent>
          </Tooltip>

          {/* Message Input */}
          <div className="flex-1 relative">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={getPlaceholderText()}
              maxLength={maxLength}
              disabled={disabled || isLoading}
              className="min-h-[44px] max-h-[120px] resize-none pr-12 rounded-2xl"
              rows={1}
            />
            
            {/* Word/Character Count */}
            <div className="absolute bottom-2 right-12 text-xs text-muted-foreground">
              {contributionMode === 'word' ? (
                <span className={cn(
                  getWordCount() > wordsPerContribution && 'text-destructive'
                )}>
                  {getWordCount()}/{wordsPerContribution}
                </span>
              ) : (
                <span className={cn(
                  message.length > maxLength * 0.9 && 'text-destructive'
                )}>
                  {message.length}/{maxLength}
                </span>
              )}
            </div>
          </div>

          {/* Special Actions */}
          <Popover open={showSpecialActions} onOpenChange={setShowSpecialActions}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 flex-shrink-0"
                disabled={disabled || isLoading}
              >
                <Zap className="w-5 h-5" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64 p-2" align="end">
              <div className="space-y-1">
                <div className="px-2 py-1 text-xs font-medium text-muted-foreground">
                  Special Actions ({userTokens} tokens available)
                </div>
                {specialActions.map((action) => {
                  const canAfford = userTokens >= action.cost;
                  const ActionIcon = action.icon;
                  
                  return (
                    <Button
                      key={action.type}
                      variant="ghost"
                      className={cn(
                        'w-full justify-start h-auto p-3 text-left',
                        !canAfford && 'opacity-50 cursor-not-allowed',
                        selectedSpecialType === action.type && action.bgColor
                      )}
                      disabled={!canAfford}
                      onClick={() => {
                        setSelectedSpecialType(action.type);
                        setShowSpecialActions(false);
                      }}
                    >
                      <div className="flex items-start gap-3 w-full">
                        <ActionIcon className={cn('w-5 h-5 mt-0.5', action.color)} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{action.label}</span>
                            <Badge variant="outline" className="text-xs">
                              {action.cost}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </PopoverContent>
          </Popover>

          {/* Send Button */}
          <Button
            onClick={handleSend}
            disabled={!canSend()}
            size="icon"
            className="h-10 w-10 flex-shrink-0 rounded-full"
            aria-label="Send"
          >
            <Send className="w-5 h-5" />
          </Button>
        </div>

        {/* Helper Text */}
        {!validateWordCount(message) && (
          <p className="text-xs text-destructive mt-2">
            {contributionMode === 'word' 
              ? `Please use only ${wordsPerContribution} word${wordsPerContribution !== 1 ? 's' : ''}`
              : `Too many words for a ${contributionMode}`
            }
          </p>
        )}
      </div>
    </TooltipProvider>
  );
};

export default MessageInput;