import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Sparkles, BookText, Users, Loader2 } from "lucide-react";
import { supabase } from "@/lib/supabase";

interface Step3DiscoverProps {
  onContinue: () => void;
}

interface FeaturedStory {
  id: string;
  title: string;
  contributorsCount: number;
  preview: string;
  upvote_count?: number;
  created_at?: string;
}

export const Step3Discover: React.FC<Step3DiscoverProps> = ({ onContinue }) => {
  const [featuredStories, setFeaturedStories] = useState<FeaturedStory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback demo stories for when database is empty
  const fallbackStories: FeaturedStory[] = [
    {
      id: "demo-1",
      title: "Lost in the Mist",
      contributorsCount: 23,
      preview:
        "The fog rolled in silently, swallowing the city whole. <PERSON> took a deep breath and stepped forward, knowing her choices tonight would shape her destiny forever...",
    },
    {
      id: "demo-2",
      title: "Beneath Silver Skies",
      contributorsCount: 17,
      preview:
        "The moon hung low, illuminating the campfire where legends were spun into life. Every eye sparkled with dreams, but only one dared to chase the wind that night...",
    },
    {
      id: "demo-3",
      title: "The Final Voyage",
      contributorsCount: 31,
      preview:
        "Captain Njoku gripped the wheel as waves battered the hull. “We chart a course for the unknown,” he declared, as the motley crew readied for adventure...",
    },
    {
      id: "demo-4",
      title: "Garden of Whispers",
      contributorsCount: 12,
      preview:
        "No one knew who planted the mysterious blossoms in the abandoned lot, but rumors spoke of secret messages in their petals, waiting to be discovered...",
    },
  ];

  useEffect(() => {
    const fetchFeaturedStories = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch stories with vote counts and contribution counts
        // We'll get public stories, ordered by votes (most popular first)
        const { data: stories, error: storiesError } = await supabase
          .from('stories')
          .select(`
            id,
            title,
            created_at,
            contributions(content, user_id),
            votes(vote_type)
          `)
          .eq('is_public', true)
          .eq('status', 'in_progress')
          .limit(4);

        if (storiesError) {
          console.error('Error fetching stories:', storiesError);
          throw storiesError;
        }

        if (stories && stories.length > 0) {
          // Process the stories to calculate vote counts and create previews
          const processedStories: FeaturedStory[] = stories.map(story => {
            // Count unique contributors
            const contributorsCount = new Set(
              story.contributions?.map((c: any) => c.user_id) || []
            ).size;

            // Count upvotes
            const upvoteCount = story.votes?.filter((v: any) => v.vote_type === 'upvote').length || 0;

            // Create preview from contributions
            const preview = story.contributions
              ?.slice(0, 15) // Take first 15 contributions
              ?.map((c: any) => c.content)
              ?.join(' ') || 'This story is just beginning...';

            return {
              id: story.id,
              title: story.title,
              contributorsCount,
              preview: preview.length > 150 ? preview.substring(0, 150) + '...' : preview,
              upvote_count: upvoteCount,
              created_at: story.created_at
            };
          });

          // Sort by vote count (most popular first)
          processedStories.sort((a, b) => (b.upvote_count || 0) - (a.upvote_count || 0));

          setFeaturedStories(processedStories);
        } else {
          // No real stories found, use fallback
          setFeaturedStories(fallbackStories);
        }
      } catch (err) {
        console.error('Error fetching featured stories:', err);
        setError('Failed to load featured stories');
        // Use fallback stories on error
        setFeaturedStories(fallbackStories);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedStories();
  }, []);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold mb-2">Discover Popular Stories</h2>
        <p className="text-muted-foreground">
          Get inspired by exploring what others in the community have created.
          <br />
          <span className="text-xs italic text-gray-400">
            (You can only join <b>public</b> stories – private stories aren’t
            open to everyone.)
          </span>
        </p>
      </div>

      {/* Removed Explore Stories button */}

      <div className="space-y-4">
        <h3 className="text-base font-medium flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-amber-500" />
          Featured Stories
          {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        </h3>

        {error && (
          <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
            {error} - Showing demo stories instead.
          </p>
        )}

        <div className="grid gap-3">
          {featuredStories.map((story) => (
            <Card key={story.id} className="overflow-hidden hover:shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="bg-primary/10 h-12 w-12 rounded-md flex items-center justify-center flex-shrink-0">
                    <BookText className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <h4 className="font-medium">{story.title}</h4>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {story.contributorsCount} contributors
                      {story.upvote_count !== undefined && story.upvote_count > 0 && (
                        <span className="ml-2 text-amber-600">
                          ⭐ {story.upvote_count} votes
                        </span>
                      )}
                    </p>
                    <p className="text-sm line-clamp-3">{story.preview}</p>
                    {story.id.startsWith('demo-') && (
                      <p className="text-xs text-gray-400 italic">Demo story</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
