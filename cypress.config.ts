const { defineConfig } = require('cypress')

module.exports = defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: false,
    screenshotOnRunFailure: true,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    setupNodeEvents(on, config) {
      // Browser-specific configurations
      on('before:browser:launch', (browser, launchOptions) => {
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          launchOptions.args.push('--disable-dev-shm-usage')
          launchOptions.args.push('--no-sandbox')
          launchOptions.args.push('--disable-gpu')
          launchOptions.args.push('--remote-debugging-port=9222')
        }

        if (browser.name === 'firefox') {
          launchOptions.preferences['dom.ipc.processCount'] = 8
          launchOptions.preferences['browser.tabs.remote.autostart'] = true
        }

        return launchOptions
      })

      // Task for accessibility testing
      on('task', {
        log(message) {
          console.log(message)
          return null
        },
        table(message) {
          console.table(message)
          return null
        }
      })
    },
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.ts',
    env: {
      // Test user credentials
      TEST_EMAIL: '<EMAIL>',
      TEST_PASSWORD: 'testpassword123',
      // Mock API endpoints
      SUPABASE_URL: 'https://mock-supabase.com',
      SUPABASE_ANON_KEY: 'mock-key',
      // Performance thresholds
      PERFORMANCE_THRESHOLD_PAGE_LOAD: 3000,
      PERFORMANCE_THRESHOLD_FIRST_PAINT: 1500,
      // Accessibility settings
      A11Y_OPTIONS: {
        runOnly: {
          type: 'tag',
          values: ['wcag2a', 'wcag2aa']
        }
      }
    }
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.ts',
  },
  // Browser-specific retries
  retries: {
    runMode: 2,
    openMode: 0
  }
})