// This script runs during Vercel build to set up the database
import { execSync } from "child_process";
import fs from "fs";

// Check if we're running in a Vercel environment
const isVercel = process.env.VERCEL === "1";

// Log the environment we're running in
console.log(`Running build in ${isVercel ? "Vercel" : "local"} environment`);
console.log(`Environment: ${process.env.VERCEL_ENV || "local"}`);

// Run Prisma migrations
try {
  console.log("Running Prisma migrations...");
  execSync("npx prisma migrate deploy", { stdio: "inherit" });
  console.log("Prisma migrations completed successfully");
} catch (error) {
  console.error("Error running Prisma migrations:", error);
  // Don't exit on error, try to continue with the build
  console.log("Continuing with build despite migration error...");
}

// Generate Prisma client
try {
  console.log("Generating Prisma client...");
  execSync("npx prisma generate", { stdio: "inherit" });
  console.log("Prisma client generated successfully");
} catch (error) {
  console.error("Error generating Prisma client:", error);
  process.exit(1);
}

// Run the build
try {
  console.log("Building the application...");
  execSync("npx vite build", { stdio: "inherit" });
  console.log("Build completed successfully");
} catch (error) {
  console.error("Error building the application:", error);
  process.exit(1);
}
