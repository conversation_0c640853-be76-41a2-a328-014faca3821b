// Script to fix Rollup module issues
const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("Starting Rollup fix script...");

// Step 1: Check Node.js version
const nodeVersion = process.version;
console.log(`Node.js version: ${nodeVersion}`);

// Step 2: Create .npmrc file to force Roll<PERSON> to use JS implementation
const npmrcContent = `rollup-plugin-node-polyfills:resolve=@rollup/plugin-node-polyfills
rollup:resolve=@rollup/rollup-js
`;

fs.writeFileSync(".npmrc", npmrcContent);
console.log("Created .npmrc file to force JS implementation of Rollup");

// Step 3: Modify package.json to use JS implementation
try {
  const packageJsonPath = path.join(process.cwd(), "package.json");
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

  // Add resolutions field to force JS implementation
  packageJson.resolutions = {
    ...packageJson.resolutions,
    rollup: "npm:@rollup/rollup-js@latest",
  };

  // Remove optionalDependencies if they exist
  if (
    packageJson.optionalDependencies &&
    packageJson.optionalDependencies["@rollup/rollup-win32-x64-msvc"]
  ) {
    delete packageJson.optionalDependencies["@rollup/rollup-win32-x64-msvc"];
    if (Object.keys(packageJson.optionalDependencies).length === 0) {
      delete packageJson.optionalDependencies;
    }
  }

  // Add direct dependency on rollup-js
  packageJson.dependencies = {
    ...packageJson.dependencies,
    "@rollup/rollup-js": "latest",
  };

  // Write updated package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log("Updated package.json to use JS implementation of Rollup");
} catch (error) {
  console.error("Error updating package.json:", error);
}

// Step 4: Clean install dependencies
try {
  console.log("Cleaning node_modules directory...");
  if (fs.existsSync("node_modules")) {
    if (process.platform === "win32") {
      execSync("rmdir /s /q node_modules", { stdio: "inherit" });
    } else {
      execSync("rm -rf node_modules", { stdio: "inherit" });
    }
  }

  console.log("Removing package-lock.json...");
  if (fs.existsSync("package-lock.json")) {
    fs.unlinkSync("package-lock.json");
  }

  console.log("Installing dependencies...");
  execSync("npm install", { stdio: "inherit" });
  console.log("Dependencies installed successfully");
} catch (error) {
  console.error("Error during dependency installation:", error);
}

// Step 5: Create a patched vite script
const viteScriptContent = `#!/usr/bin/env node

// Patched Vite script to use JS implementation of Rollup
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = 'true';
process.env.ROLLUP_NATIVE_DISABLE = 'true';

// Run the original vite CLI
require('vite/bin/vite.js');
`;

fs.writeFileSync("vite-patched.js", viteScriptContent);
fs.chmodSync("vite-patched.js", "755");
console.log("Created patched Vite script");

// Step 6: Update package.json scripts to use patched vite
try {
  const packageJsonPath = path.join(process.cwd(), "package.json");
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

  // Update scripts to use patched vite
  if (packageJson.scripts) {
    if (packageJson.scripts.dev) {
      packageJson.scripts.dev = "node vite-patched.js";
    }
    if (packageJson.scripts.build) {
      packageJson.scripts.build = "node vite-patched.js build";
    }
    if (packageJson.scripts["build:dev"]) {
      packageJson.scripts["build:dev"] =
        "node vite-patched.js build --mode development";
    }
    if (packageJson.scripts.preview) {
      packageJson.scripts.preview = "node vite-patched.js preview";
    }

    // Add a new script for running with the patched vite
    packageJson.scripts["dev:patched"] = "node vite-patched.js";
    packageJson.scripts["build:patched"] = "node vite-patched.js build";
  }

  // Write updated package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log("Updated package.json scripts to use patched Vite");
} catch (error) {
  console.error("Error updating package.json scripts:", error);
}

console.log("\nFix completed! You can now run the project with:");
console.log("npm run dev:patched");
console.log("or build with:");
console.log("npm run build:patched");
