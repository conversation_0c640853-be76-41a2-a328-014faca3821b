import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Plus, UserPlus, Send, Copy, Link, Mail } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';

export interface InviteButtonProps {
  storyId: string;
  storyTitle: string;
  onInvite?: (inviteData: InviteData) => Promise<void>;
  variant?: 'icon' | 'button' | 'compact';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export interface InviteData {
  type: 'email' | 'link' | 'username';
  recipients?: string[];
  message?: string;
  permissions?: 'contributor' | 'viewer';
  expiresAt?: Date;
}

export const InviteButton: React.FC<InviteButtonProps> = ({
  storyId,
  storyTitle,
  onInvite,
  variant = 'icon',
  size = 'md',
  disabled = false,
  className,
}) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('email');
  
  // Form state
  const [emailList, setEmailList] = useState('');
  const [usernameList, setUsernameList] = useState('');
  const [inviteMessage, setInviteMessage] = useState('');
  const [permissions, setPermissions] = useState<'contributor' | 'viewer'>('contributor');
  const [linkExpiry, setLinkExpiry] = useState('7d');

  // Parse email addresses from textarea
  const parseEmails = (text: string): string[] => {
    return text
      .split(/[,\n;]/)
      .map(email => email.trim())
      .filter(email => email.length > 0 && email.includes('@'));
  };

  // Parse usernames from textarea
  const parseUsernames = (text: string): string[] => {
    return text
      .split(/[,\n;]/)
      .map(username => username.trim().replace('@', ''))
      .filter(username => username.length > 0);
  };

  // Generate shareable link
  const generateShareableLink = async (): Promise<string> => {
    const baseUrl = window.location.origin;
    const inviteCode = `${storyId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    return `${baseUrl}/stories/${storyId}/join?invite=${inviteCode}&permissions=${permissions}`;
  };

  // Handle different invite types
  const handleInvite = async (type: 'email' | 'link' | 'username') => {
    setIsLoading(true);
    
    try {
      let recipients: string[] = [];
      
      if (type === 'email') {
        recipients = parseEmails(emailList);
        if (recipients.length === 0) {
          toast({
            title: 'No valid emails',
            description: 'Please enter at least one valid email address.',
            variant: 'destructive',
          });
          return;
        }
      } else if (type === 'username') {
        recipients = parseUsernames(usernameList);
        if (recipients.length === 0) {
          toast({
            title: 'No valid usernames',
            description: 'Please enter at least one valid username.',
            variant: 'destructive',
          });
          return;
        }
      }

      const inviteData: InviteData = {
        type,
        recipients: type === 'link' ? [] : recipients,
        message: inviteMessage.trim() || `Join me in writing "${storyTitle}" - a collaborative story!`,
        permissions,
        expiresAt: new Date(Date.now() + parseInt(linkExpiry.replace('d', '')) * 24 * 60 * 60 * 1000),
      };

      if (type === 'link') {
        // Generate and copy link
        const shareableLink = await generateShareableLink();
        await navigator.clipboard.writeText(shareableLink);
        
        toast({
          title: 'Link copied!',
          description: 'The invitation link has been copied to your clipboard.',
        });
      } else {
        // Send invites
        await onInvite?.(inviteData);
        
        toast({
          title: 'Invitations sent!',
          description: `Successfully sent ${recipients.length} invitation${recipients.length !== 1 ? 's' : ''}.`,
        });
      }

      // Reset form
      setEmailList('');
      setUsernameList('');
      setInviteMessage('');
      setIsOpen(false);
      
    } catch (error) {
      console.error('Failed to send invitations:', error);
      toast({
        title: 'Failed to send invitations',
        description: 'Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get button content based on variant
  const getButtonContent = () => {
    switch (variant) {
      case 'button':
        return (
          <>
            <UserPlus className="w-4 h-4 mr-2" />
            Invite Contributors
          </>
        );
      case 'compact':
        return (
          <>
            <Plus className="w-4 h-4 mr-1" />
            Invite
          </>
        );
      default:
        return <Plus className="w-4 h-4" />;
    }
  };

  // Get button size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-8 w-8';
      case 'lg':
        return 'h-12 w-12';
      default:
        return variant === 'icon' ? 'h-10 w-10' : 'h-10';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={variant === 'icon' ? 'outline' : 'default'}
          size={variant === 'icon' ? 'icon' : (size === 'md' ? 'default' : size)}
          disabled={disabled}
          className={cn(
            'transition-all duration-200',
            variant === 'icon' && [
              getSizeClasses(),
              'rounded-full shadow-lg hover:shadow-xl',
              'bg-primary text-primary-foreground hover:bg-primary/90',
              'border-primary/20 hover:border-primary/30',
              'hover:scale-105 active:scale-95'
            ],
            variant === 'compact' && 'px-3',
            className
          )}
        >
          {getButtonContent()}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Invite to "{storyTitle}"
          </DialogTitle>
          <DialogDescription>
            Invite others to collaborate on this story. Choose how you'd like to send invitations.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="email" className="flex items-center gap-1">
              <Mail className="w-4 h-4" />
              Email
            </TabsTrigger>
            <TabsTrigger value="username" className="flex items-center gap-1">
              <UserPlus className="w-4 h-4" />
              Username
            </TabsTrigger>
            <TabsTrigger value="link" className="flex items-center gap-1">
              <Link className="w-4 h-4" />
              Link
            </TabsTrigger>
          </TabsList>

          <div className="mt-4 space-y-4">
            {/* Permissions selector */}
            <div className="space-y-2">
              <Label>Invite as</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant={permissions === 'contributor' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPermissions('contributor')}
                  className="flex-1"
                >
                  Contributor
                </Button>
                <Button
                  type="button"
                  variant={permissions === 'viewer' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPermissions('viewer')}
                  className="flex-1"
                >
                  Viewer
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                {permissions === 'contributor' 
                  ? 'Can write and contribute to the story' 
                  : 'Can only read and follow the story'}
              </p>
            </div>

            <TabsContent value="email" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="emails">Email addresses</Label>
                <Textarea
                  id="emails"
                  placeholder="Enter email addresses separated by commas or new lines..."
                  value={emailList}
                  onChange={(e) => setEmailList(e.target.value)}
                  className="min-h-[80px] resize-none"
                />
                {emailList && (
                  <div className="flex flex-wrap gap-1">
                    {parseEmails(emailList).map((email, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {email}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="username" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="usernames">Usernames</Label>
                <Textarea
                  id="usernames"
                  placeholder="Enter usernames separated by commas or new lines..."
                  value={usernameList}
                  onChange={(e) => setUsernameList(e.target.value)}
                  className="min-h-[80px] resize-none"
                />
                {usernameList && (
                  <div className="flex flex-wrap gap-1">
                    {parseUsernames(usernameList).map((username, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        @{username}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="link" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="expiry">Link expires in</Label>
                <select
                  id="expiry"
                  value={linkExpiry}
                  onChange={(e) => setLinkExpiry(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-input bg-background rounded-md"
                >
                  <option value="1d">1 day</option>
                  <option value="3d">3 days</option>
                  <option value="7d">7 days</option>
                  <option value="30d">30 days</option>
                  <option value="365d">Never</option>
                </select>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Generate a shareable link that anyone can use to join the story. 
                  The link will be copied to your clipboard.
                </p>
              </div>
            </TabsContent>

            {/* Personal message */}
            {activeTab !== 'link' && (
              <div className="space-y-2">
                <Label htmlFor="message">Personal message (optional)</Label>
                <Textarea
                  id="message"
                  placeholder={`Join me in writing "${storyTitle}" - a collaborative story!`}
                  value={inviteMessage}
                  onChange={(e) => setInviteMessage(e.target.value)}
                  className="min-h-[60px] resize-none"
                />
              </div>
            )}
          </div>
        </Tabs>

        <DialogFooter className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={() => handleInvite(activeTab as 'email' | 'link' | 'username')}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                Sending...
              </>
            ) : (
              <>
                {activeTab === 'link' ? (
                  <>
                    <Copy className="w-4 h-4" />
                    Copy Link
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4" />
                    Send Invites
                  </>
                )}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InviteButton; 