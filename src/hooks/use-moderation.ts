/**
 * Moderation Hook
 * Provides easy access to moderation and strike system functionality
 */

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth';
import { useToast } from './use-toast';
import {
  submitContentReport,
  processReportWithStrike,
  issueStrikeToStory,
  getStoryModerationStatus,
  getPendingReports,
  removeStrike,
  getUserReportHistory,
  type ContentReport,
  type StoryStrike,
  type StoryModerationStatus
} from '@/services/strikeService';

export interface UseModerationOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface ModerationActions {
  // Content reporting
  reportContent: (
    contentType: 'story' | 'contribution' | 'comment',
    contentId: string,
    reason: string,
    details?: string
  ) => Promise<boolean>;
  
  // Strike management (admin only)
  issueStrike: (
    storyId: string,
    reason: string,
    details?: string,
    reportId?: string
  ) => Promise<boolean>;
  
  removeStrike: (strikeId: string, reason?: string) => Promise<boolean>;
  
  // Report processing (admin only)
  processReport: (reportId: string, issueStrike: boolean, notes?: string) => Promise<boolean>;
  
  // Data fetching
  getStoryStatus: (storyId: string) => Promise<StoryModerationStatus | null>;
  refreshPendingReports: () => Promise<void>;
  refreshUserReports: () => Promise<void>;
}

export interface ModerationData {
  // Current user's reports
  userReports: ContentReport[];
  
  // Admin data (only if user is admin)
  pendingReports: ContentReport[];
  
  // Loading states
  isLoading: boolean;
  isSubmitting: boolean;
  
  // Error state
  error: string | null;
}

export const useModeration = (options: UseModerationOptions = {}): ModerationActions & ModerationData => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { autoRefresh = false, refreshInterval = 30000 } = options;

  // State management
  const [userReports, setUserReports] = useState<ContentReport[]>([]);
  const [pendingReports, setPendingReports] = useState<ContentReport[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user is admin
  const isAdmin = user?.is_admin || false;

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && user) {
      const interval = setInterval(() => {
        refreshUserReports();
        if (isAdmin) {
          refreshPendingReports();
        }
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, user, isAdmin]);

  // Load initial data
  useEffect(() => {
    if (user) {
      refreshUserReports();
      if (isAdmin) {
        refreshPendingReports();
      }
    }
  }, [user, isAdmin]);

  // Content reporting
  const reportContent = async (
    contentType: 'story' | 'contribution' | 'comment',
    contentId: string,
    reason: string,
    details?: string
  ): Promise<boolean> => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to report content.',
        variant: 'destructive'
      });
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await submitContentReport(contentType, contentId, reason, details);
      
      if (result.success) {
        // Refresh user reports to show the new report
        await refreshUserReports();
        return true;
      } else {
        setError(result.error || 'Failed to submit report');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit report';
      setError(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Issue strike (admin only)
  const issueStrike = async (
    storyId: string,
    reason: string,
    details?: string,
    reportId?: string
  ): Promise<boolean> => {
    if (!isAdmin) {
      toast({
        title: 'Unauthorized',
        description: 'Only administrators can issue strikes.',
        variant: 'destructive'
      });
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await issueStrikeToStory(storyId, reason, details, reportId, false);
      
      if (result.success) {
        // Refresh pending reports to update UI
        await refreshPendingReports();
        return true;
      } else {
        setError(result.error || 'Failed to issue strike');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to issue strike';
      setError(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Remove strike (admin only)
  const removeStrikeAction = async (strikeId: string, reason?: string): Promise<boolean> => {
    if (!isAdmin) {
      toast({
        title: 'Unauthorized',
        description: 'Only administrators can remove strikes.',
        variant: 'destructive'
      });
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await removeStrike(strikeId, reason);
      
      if (result.success) {
        toast({
          title: 'Strike removed',
          description: 'The strike has been successfully removed.',
        });
        return true;
      } else {
        setError(result.error || 'Failed to remove strike');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove strike';
      setError(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Process report (admin only)
  const processReport = async (
    reportId: string,
    shouldIssueStrike: boolean,
    notes?: string
  ): Promise<boolean> => {
    if (!isAdmin) {
      toast({
        title: 'Unauthorized',
        description: 'Only administrators can process reports.',
        variant: 'destructive'
      });
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await processReportWithStrike(reportId, shouldIssueStrike, notes);
      
      if (result.success) {
        // Refresh pending reports to update UI
        await refreshPendingReports();
        
        toast({
          title: 'Report processed',
          description: shouldIssueStrike ? 'Report resolved with strike issued.' : 'Report dismissed.',
        });
        return true;
      } else {
        setError(result.error || 'Failed to process report');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process report';
      setError(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get story moderation status
  const getStoryStatus = async (storyId: string): Promise<StoryModerationStatus | null> => {
    try {
      return await getStoryModerationStatus(storyId);
    } catch (error) {
      console.error('Error getting story moderation status:', error);
      return null;
    }
  };

  // Refresh pending reports (admin only)
  const refreshPendingReports = async (): Promise<void> => {
    if (!isAdmin) return;

    setIsLoading(true);
    setError(null);

    try {
      const reports = await getPendingReports();
      setPendingReports(reports);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load pending reports';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh user reports
  const refreshUserReports = async (): Promise<void> => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const reports = await getUserReportHistory();
      setUserReports(reports);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load your reports';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    // Actions
    reportContent,
    issueStrike,
    removeStrike: removeStrikeAction,
    processReport,
    getStoryStatus,
    refreshPendingReports,
    refreshUserReports,

    // Data
    userReports,
    pendingReports,
    isLoading,
    isSubmitting,
    error
  };
};

export default useModeration;