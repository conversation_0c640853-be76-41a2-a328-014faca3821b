[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [input-otp](../README.md) / InputOTPSeparator

# Variable: InputOTPSeparator

> `const` **InputOTPSeparator**: `ForwardRefExoticComponent`\<`Omit`\<`DetailedHTMLProps`\<`HTMLAttributes`\<`HTMLDivElement`\>, `HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/input-otp.tsx:60](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/input-otp.tsx#L60)
