// Main messaging interface components
export { default as MessagingInterface } from './MessagingInterface';
export type { MessagingInterfaceProps, Story, Contribution, Participant } from './MessagingInterface';

// Individual components
export { default as MessageBubble } from './MessageBubble';
export type { MessageBubbleProps } from './MessageBubble';

export { default as StoryThread } from './StoryThread';
export type { StoryThreadProps } from './StoryThread';

export { default as MessageInput } from './MessageInput';
export type { MessageInputProps } from './MessageInput';

export { default as TypingIndicator } from './TypingIndicator';
export type { TypingIndicatorProps, TypingUser } from './TypingIndicator';

export { default as ParticipantsSidebar } from './ParticipantsSidebar';
export type { ParticipantsSidebarProps } from './ParticipantsSidebar';

export { default as StoryCompilationView } from './StoryCompilationView';
export type { StoryCompilationViewProps } from './StoryCompilationView';