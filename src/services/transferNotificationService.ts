import { supabase } from '@/lib/supabase';
import { TokenTransferUser } from './tokenService';

export interface TransferNotificationData {
  senderId: string;
  senderUsername: string;
  recipientId: string;
  recipientUsername: string;
  amount: number;
  transferId: string;
  transferMessage?: string;
  senderNewBalance?: number;
  recipientNewBalance?: number;
}

export interface FailedTransferNotificationData {
  senderId: string;
  senderUsername: string;
  recipientUsername: string;
  amount: number;
  error: string;
  transferMessage?: string;
}

/**
 * Service for handling transfer-related notifications
 */
export class TransferNotificationService {
  private static instance: TransferNotificationService;

  private constructor() {}

  public static getInstance(): TransferNotificationService {
    if (!TransferNotificationService.instance) {
      TransferNotificationService.instance = new TransferNotificationService();
    }
    return TransferNotificationService.instance;
  }

  /**
   * Send notification when a token transfer is successful
   */
  async notifyTransferSuccess(data: TransferNotificationData): Promise<void> {
    try {
      // Notify sender about successful transfer
      await this.createNotification({
        userId: data.senderId,
        type: 'transfer_sent',
        content: {
          message: `You successfully sent ${data.amount} tokens to @${data.recipientUsername}${data.transferMessage ? ` with message: "${data.transferMessage}"` : ''}.`,
          amount: data.amount,
          recipient_username: data.recipientUsername,
          transfer_id: data.transferId,
          new_balance: data.senderNewBalance,
          transfer_message: data.transferMessage,
        },
      });

      // Notify recipient about received tokens
      await this.createNotification({
        userId: data.recipientId,
        type: 'transfer_received',
        content: {
          message: `You received ${data.amount} tokens from @${data.senderUsername}${data.transferMessage ? ` with message: "${data.transferMessage}"` : ''}.`,
          amount: data.amount,
          sender_username: data.senderUsername,
          transfer_id: data.transferId,
          new_balance: data.recipientNewBalance,
          transfer_message: data.transferMessage,
        },
      });

    } catch (error) {
      console.error('Error sending transfer success notifications:', error);
      // Don't throw - notifications are not critical for transfer success
    }
  }

  /**
   * Send notification when a token transfer fails
   */
  async notifyTransferFailure(data: FailedTransferNotificationData): Promise<void> {
    try {
      // Only notify sender about failed transfer
      await this.createNotification({
        userId: data.senderId,
        type: 'transfer_failed',
        content: {
          message: `Failed to send ${data.amount} tokens to @${data.recipientUsername}. Reason: ${data.error}`,
          amount: data.amount,
          recipient_username: data.recipientUsername,
          error: data.error,
          transfer_message: data.transferMessage,
        },
      });

    } catch (error) {
      console.error('Error sending transfer failure notification:', error);
      // Don't throw - notifications are not critical
    }
  }

  /**
   * Send notification for transfer-related events (like daily limit reached)
   */
  async notifyTransferLimit(userId: string, limitType: 'daily' | 'single', remainingAmount?: number): Promise<void> {
    try {
      let message: string;
      
      if (limitType === 'daily') {
        message = remainingAmount 
          ? `You have ${remainingAmount} tokens remaining in your daily transfer limit.`
          : 'You have reached your daily transfer limit of 1000 tokens. Limit resets at midnight.';
      } else {
        message = 'Transfer failed: Single transfer cannot exceed 500 tokens.';
      }

      await this.createNotification({
        userId,
        type: 'transfer_limit',
        content: {
          message,
          limit_type: limitType,
          remaining_amount: remainingAmount,
        },
      });

    } catch (error) {
      console.error('Error sending transfer limit notification:', error);
    }
  }

  /**
   * Send notification when someone searches for the user (privacy feature)
   */
  async notifyUserSearch(userId: string, searchedByUsername: string): Promise<void> {
    try {
      await this.createNotification({
        userId,
        type: 'user_searched',
        content: {
          message: `@${searchedByUsername} searched for your username in the token transfer system.`,
          searched_by: searchedByUsername,
        },
      });

    } catch (error) {
      console.error('Error sending user search notification:', error);
    }
  }

  /**
   * Send weekly/monthly transfer summary notifications
   */
  async notifyTransferSummary(
    userId: string,
    period: 'weekly' | 'monthly',
    stats: {
      totalSent: number;
      totalReceived: number;
      transferCount: number;
      topRecipients: Array<{ username: string; amount: number }>;
      topSenders: Array<{ username: string; amount: number }>;
    }
  ): Promise<void> {
    try {
      const periodText = period === 'weekly' ? 'this week' : 'this month';
      const message = `Your token transfer summary for ${periodText}: ${stats.transferCount} transfers, ${stats.totalSent} sent, ${stats.totalReceived} received.`;

      await this.createNotification({
        userId,
        type: 'transfer_summary',
        content: {
          message,
          period,
          total_sent: stats.totalSent,
          total_received: stats.totalReceived,
          transfer_count: stats.transferCount,
          top_recipients: stats.topRecipients,
          top_senders: stats.topSenders,
        },
      });

    } catch (error) {
      console.error('Error sending transfer summary notification:', error);
    }
  }

  /**
   * Helper method to create a notification in the database
   */
  private async createNotification(notification: {
    userId: string;
    type: string;
    content: Record<string, any>;
  }): Promise<void> {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: notification.userId,
        type: notification.type,
        content: notification.content,
        is_read: false,
      });

    if (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get all transfer-related notifications for a user
   */
  async getTransferNotifications(
    userId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .in('type', ['transfer_sent', 'transfer_received', 'transfer_failed', 'transfer_limit', 'transfer_summary'])
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching transfer notifications:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching transfer notifications:', error);
      return [];
    }
  }

  /**
   * Mark transfer notifications as read for a user
   */
  async markTransferNotificationsAsRead(userId: string, transferId?: string): Promise<void> {
    try {
      let query = supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('user_id', userId)
        .in('type', ['transfer_sent', 'transfer_received', 'transfer_failed']);

      if (transferId) {
        query = query.eq('content->transfer_id', transferId);
      }

      const { error } = await query;

      if (error) {
        console.error('Error marking transfer notifications as read:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error marking transfer notifications as read:', error);
    }
  }
}

// Export singleton instance
export const transferNotificationService = TransferNotificationService.getInstance(); 