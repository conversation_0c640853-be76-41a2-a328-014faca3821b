[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [form](../README.md) / FormField

# Function: FormField()

> **FormField**\<`TFieldValues`, `TName`\>(`__namedParameters`): `Element`

Defined in: [src/components/ui/form.tsx:29](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/form.tsx#L29)

## Type Parameters

### TFieldValues

`TFieldValues` *extends* `FieldValues` = `FieldValues`

### TName

`TName` *extends* `string` = `FieldPath`\<`TFieldValues`\>

## Parameters

### \_\_namedParameters

`ControllerProps`\<`TFieldValues`, `TName`\>

## Returns

`Element`
