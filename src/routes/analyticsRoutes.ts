import { Router } from 'express';
import { Request, Response } from 'express';
import ApiAuthMiddleware from '../middleware/apiAuthMiddleware';
import UserAuthMiddleware from '../middleware/userAuthMiddleware';
import { APICacheMiddleware } from '../middleware/cacheMiddleware';
import { StripeApiIntegration } from '../services/stripeApiIntegration';
import { ApiKeyService } from '../services/apiKeyService';
import CostTrackingService from '../services/costTrackingService';
import AnalyticsService from '../services/analyticsService';
import { supabase } from '@/lib/supabase';

const router = Router();

/**
 * Analytics API endpoints for dashboard data
 */

// GET /api/analytics/overview - Get overview metrics
router.get('/overview',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  APICacheMiddleware.cache(), // Cache for 5 minutes
  async (req: Request, res: Response) => {
    try {
      const { period = '30d' } = req.query;
      const userId = req.userId!;
      
      // Get real analytics data from database
      const analytics = await AnalyticsService.getUserAnalytics(
        userId, 
        period as '24h' | '7d' | '30d'
      );

      if (!analytics) {
        return res.status(404).json({
          error: 'Analytics data not found',
          message: 'No analytics data available for this user'
        });
      }

      const overview = {
        usage: {
          totalRequests: analytics.usage.totalRequests,
          requestsToday: analytics.usage.requestsToday,
          requestsThisMonth: analytics.usage.requestsThisMonth,
          avgResponseTime: analytics.usage.avgResponseTime,
          cacheHitRate: analytics.usage.cacheHitRate,
          uptime: analytics.performance.uptime,
          errorRate: analytics.performance.errorRate
        },
        costs: {
          monthlyFee: analytics.billing.monthlyFee,
          usageCost: analytics.billing.usageCost,
          totalCost: analytics.billing.totalCost,
          estimatedSavings: analytics.billing.estimatedSavings
        },
        performance: {
          latencyP95: analytics.performance.latencyP95,
          throughput: analytics.performance.throughput,
          errorDetails: {
            '4xx': Math.round(analytics.performance.errorRate * 0.7 * 100) / 100, // Estimate 70% are 4xx
            '5xx': Math.round(analytics.performance.errorRate * 0.3 * 100) / 100  // Estimate 30% are 5xx
          }
        },
        period: period || '30d'
      };

      res.json({
        success: true,
        data: overview,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error fetching analytics overview:', error);
      res.status(500).json({
        error: 'Failed to fetch analytics overview',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/endpoints - Get endpoint performance data
router.get('/endpoints',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      const { limit = 10, sort = 'requests', period = '30d' } = req.query;
      const userId = req.userId!;

      // Get user's API keys
      const { data: apiKeys, error: keysError } = await supabase
        .from('api_keys')
        .select('id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (keysError) {
        throw keysError;
      }

      if (!apiKeys || apiKeys.length === 0) {
        return res.json({
          success: true,
          data: {
            endpoints: [],
            totalEndpoints: 0,
            period
          }
        });
      }

      const keyIds = apiKeys.map(key => key.id);
      const timeRange = AnalyticsService.getTimeRange(period as '24h' | '7d' | '30d');
      
      // Get real endpoint metrics from database
      const endpoints = await AnalyticsService.getEndpointMetrics(
        keyIds, 
        timeRange, 
        Number(limit)
      );

      res.json({
        success: true,
        data: {
          endpoints,
          totalEndpoints: endpoints.length,
          period
        }
      });

    } catch (error) {
      console.error('❌ Error fetching endpoint analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch endpoint analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/billing - Get billing analytics
router.get('/billing',
  ApiAuthMiddleware.requireApiKey(),
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      const { month, year } = req.query;
      
      // TODO: Get userId from API key
      const userId = 'user_123'; // Placeholder
      
      const currentMonth = month ? Number(month) : new Date().getMonth() + 1;
      const currentYear = year ? Number(year) : new Date().getFullYear();

      const billingSummary = await StripeApiIntegration.getBillingSummary(
        userId, 
        currentMonth, 
        currentYear
      );

      if (!billingSummary) {
        return res.status(404).json({
          error: 'Billing data not found',
          message: 'No billing information available for the specified period'
        });
      }

      res.json({
        success: true,
        data: {
          period: { month: currentMonth, year: currentYear },
          subscription: {
            tier: billingSummary.tier,
            monthlyFee: billingSummary.subscriptionFee,
            nextBillDate: '2024-12-24'
          },
          usage: {
            totalRequests: billingSummary.requestCount,
            cacheHitRate: billingSummary.cacheHitRate,
            usageCost: billingSummary.apiUsageCost
          },
          costs: {
            subscriptionFee: billingSummary.subscriptionFee,
            usageFee: billingSummary.apiUsageCost,
            totalCost: billingSummary.totalCost,
            savings: billingSummary.apiUsageCost * 0.9 // Cache savings
          },
          forecast: {
            nextMonthEstimate: billingSummary.totalCost * 1.05, // 5% growth estimate
            annualEstimate: billingSummary.totalCost * 12
          }
        }
      });

    } catch (error) {
      console.error('❌ Error fetching billing analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch billing analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/revenue - Revenue metrics (admin/business view)
router.get('/revenue',
  ApiAuthMiddleware.requireEnterprise(), // Restrict to enterprise tier
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      // TODO: Calculate actual revenue metrics from subscription data
      const mockRevenue = {
        mrr: 15497.01, // Monthly Recurring Revenue
        arr: 185964.12, // Annual Recurring Revenue
        customers: {
          total: 312,
          premium: 289,
          enterprise: 23,
          churnRate: 2.3,
          upgradeRate: 15.7
        },
        growth: {
          monthlyGrowth: 8.4,
          quarterlyGrowth: 23.1,
          yearlyGrowth: 145.7
        },
        costs: {
          totalInfrastructure: 2847.32,
          apiCosts: 1923.45,
          supportCosts: 892.18,
          margin: 78.4
        }
      };

      res.json({
        success: true,
        data: mockRevenue,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error fetching revenue analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch revenue analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

/**
 * 🚀 NEW: Cost Tracking and Profit Margin Optimization Endpoints
 */

// GET /api/analytics/costs - Get external service costs and optimization opportunities
router.get('/costs',
  ApiAuthMiddleware.requirePremium(), // Premium feature
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      const [serviceCosts, totalCosts, optimizations, alerts] = await Promise.all([
        CostTrackingService.getServiceCosts(),
        CostTrackingService.getTotalExternalCosts(),
        CostTrackingService.generateOptimizations(),
        CostTrackingService.checkCostAlerts()
      ]);

      res.json({
        success: true,
        data: {
          serviceCosts,
          totalCosts,
          optimizations,
          alerts,
          summary: {
            totalMonthlyCost: totalCosts.monthly,
            projectedCost: totalCosts.projected,
            optimizationPotential: optimizations.reduce((sum, opt) => sum + opt.savings, 0),
            costGrowth: ((totalCosts.projected - totalCosts.monthly) / totalCosts.monthly) * 100
          }
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error fetching cost analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch cost analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/margins - Get profit margin analysis
router.get('/margins',
  ApiAuthMiddleware.requirePremium(), // Premium feature
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      const marginAnalysis = await CostTrackingService.calculateProfitMargins();
      const costEfficiency = await CostTrackingService.getCostEfficiencyMetrics();

      res.json({
        success: true,
        data: {
          margins: marginAnalysis,
          efficiency: costEfficiency,
          insights: {
            profitability: marginAnalysis.margins.grossMargin > 70 ? 'excellent' : 
                          marginAnalysis.margins.grossMargin > 60 ? 'good' : 'concerning',
            revenuePerUser: marginAnalysis.revenue.totalMonthlyRevenue / 
                           (marginAnalysis.revenue.premiumSubscriptions / 49.99 + 
                            marginAnalysis.revenue.enterpriseSubscriptions / 199),
            costPerUser: marginAnalysis.costs.costPerUser,
            breakEvenAnalysis: {
              currentUsers: marginAnalysis.revenue.premiumSubscriptions / 49.99 + 
                           marginAnalysis.revenue.enterpriseSubscriptions / 199,
              breakEvenUsers: marginAnalysis.margins.breakEvenUsers,
              marginOfSafety: marginAnalysis.margins.breakEvenUsers > 
                             (marginAnalysis.revenue.premiumSubscriptions / 49.99 + 
                              marginAnalysis.revenue.enterpriseSubscriptions / 199)
            }
          }
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error fetching margin analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch margin analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/cost-report - Generate comprehensive monthly cost report
router.get('/cost-report',
  ApiAuthMiddleware.requireEnterprise(), // Enterprise feature
  APICacheMiddleware.cache(),
  async (req: Request, res: Response) => {
    try {
      const { month, year } = req.query;
      
      const currentMonth = month ? Number(month) : new Date().getMonth() + 1;
      const currentYear = year ? Number(year) : new Date().getFullYear();

      const costReport = await CostTrackingService.generateMonthlyCostReport(
        currentMonth, 
        currentYear
      );

      res.json({
        success: true,
        data: costReport,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error generating cost report:', error);
      res.status(500).json({
        error: 'Failed to generate cost report',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// POST /api/analytics/cost-optimization - Apply cost optimization recommendation
router.post('/cost-optimization',
  ApiAuthMiddleware.requireEnterprise(), // Enterprise feature
  async (req: Request, res: Response) => {
    try {
      const { optimizationId, action } = req.body;

      // TODO: Implement actual optimization actions
      // This would apply specific optimizations like caching strategies, etc.
      
      res.json({
        success: true,
        data: {
          optimizationId,
          action,
          status: 'applied',
          estimatedSavings: 140.25,
          implementationTime: '2-3 hours'
        },
        message: 'Cost optimization recommendation applied successfully'
      });

    } catch (error) {
      console.error('❌ Error applying cost optimization:', error);
      res.status(500).json({
        error: 'Failed to apply cost optimization',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// GET /api/analytics/health - Analytics service health check
router.get('/health',
  ApiAuthMiddleware.healthCheck()
);

/**
 * Real-time analytics endpoints
 */

// GET /api/analytics/realtime - Real-time metrics
router.get('/realtime',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  async (req: Request, res: Response) => {
    try {
      const userId = req.userId!;

      // Get user's API keys
      const { data: apiKeys, error: keysError } = await supabase
        .from('api_keys')
        .select('id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (keysError) {
        throw keysError;
      }

      const keyIds = apiKeys?.map(key => key.id) || [];
      
      // Get real-time metrics from database
      const realtimeMetrics = await AnalyticsService.getRealtimeMetrics(keyIds);

      res.json({
        success: true,
        data: realtimeMetrics
      });

    } catch (error) {
      console.error('❌ Error fetching real-time analytics:', error);
      res.status(500).json({
        error: 'Failed to fetch real-time analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

// POST /api/analytics/custom - Custom analytics query
router.post('/custom',
  ApiAuthMiddleware.requirePremium(), // Premium feature
  async (req: Request, res: Response) => {
    try {
      const { query, filters, groupBy, timeRange } = req.body;

      // TODO: Implement custom analytics queries
      // This would allow premium users to create custom reports
      
      res.json({
        success: true,
        data: {
          query,
          results: [],
          executionTime: '45ms',
          cached: false
        },
        message: 'Custom analytics query executed successfully'
      });

    } catch (error) {
      console.error('❌ Error executing custom analytics query:', error);
      res.status(500).json({
        error: 'Failed to execute custom query',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
);

export default router; 