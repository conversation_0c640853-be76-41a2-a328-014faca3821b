import React from 'react';
import { render, RenderOptions, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/auth/AuthContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { Toaster } from '@/components/ui/toaster';
import type { User } from '@supabase/supabase-js';

// Re-export testing library functions for easy access
export { render, screen, fireEvent, waitFor, act } from '@testing-library/react';

// Enhanced test environment configuration
export const testConfig = {
  queryClient: {
    defaultOptions: {
      queries: { 
        retry: false,
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      },
      mutations: { 
        retry: false,
      },
    },
  },
  testTimeout: 10000,
  waitForOptions: {
    timeout: 5000,
    interval: 100,
  },
};

// Mock user data for tests
export const mockTestUser: Partial<User> = {
  id: 'test-user-123',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Test User',
    avatar_url: 'https://example.com/avatar.jpg',
  },
  app_metadata: {},
  aud: 'authenticated',
  created_at: '2024-01-01T00:00:00Z',
};

// Mock story data for consistent testing
export const mockTestStory = {
  id: 'test-story-123',
  title: 'Test Story',
  description: 'A story for testing purposes',
  contribution_mode: 'word' as const,
  words_per_contribution: 1,
  max_contributions: 100,
  current_contribution_count: 3,
  creator_id: 'test-user-123',
  status: 'ACTIVE' as const,
};

// Mock contributions for testing
export const mockTestContributions = [
  {
    id: 'contrib-1',
    content: 'Once',
    author: { 
      id: 'test-user-123', 
      username: 'TestUser', 
      avatar_url: 'https://example.com/avatar1.jpg' 
    },
    created_at: '2024-01-01T10:00:00Z',
    position: 1,
    special_type: null,
    token_cost: 0,
  },
  {
    id: 'contrib-2',
    content: 'upon',
    author: { 
      id: 'test-user-456', 
      username: 'SecondUser', 
      avatar_url: 'https://example.com/avatar2.jpg' 
    },
    created_at: '2024-01-01T10:01:00Z',
    position: 2,
    special_type: null,
    token_cost: 0,
  },
  {
    id: 'contrib-3',
    content: 'time',
    author: { 
      id: 'test-user-123', 
      username: 'TestUser', 
      avatar_url: 'https://example.com/avatar1.jpg' 
    },
    created_at: '2024-01-01T10:02:00Z',
    position: 3,
    special_type: 'golden' as const,
    token_cost: 3,
  },
];

// Enhanced Test Wrapper with better error boundaries
interface TestWrapperProps {
  children: React.ReactNode;
  queryClient?: QueryClient;
  initialPath?: string;
}

export const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  queryClient,
  initialPath = '/'
}) => {
  const client = queryClient || new QueryClient(testConfig.queryClient);

  // Set up initial route if specified
  if (initialPath !== '/') {
    window.history.pushState({}, '', initialPath);
  }

  return (
    <BrowserRouter>
      <QueryClientProvider client={client}>
        <ThemeProvider>
          <AuthProvider>
            {children}
            <Toaster />
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

// Enhanced render function with TestWrapper
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  initialPath?: string;
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient, initialPath, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper 
        queryClient={queryClient} 
        initialPath={initialPath}
      >
        {children}
      </TestWrapper>
    ),
    ...renderOptions,
  });
}

// Test utility functions
export const createMockQueryClient = () => {
  return new QueryClient(testConfig.queryClient);
};

// Wait for async operations to complete
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 100));
};

// Clean up function for tests
export const cleanupTest = (queryClient: QueryClient) => {
  queryClient.clear();
  queryClient.getQueryCache().clear();
  queryClient.getMutationCache().clear();
};

// Mock Supabase functions for consistent testing
export const createMockSupabaseResponse = (data: any, error?: any) => ({
  data,
  error,
});

// Helper to mock auth state
export const mockAuthState = (user: Partial<User> | null) => {
  const mockSupabase = require('@/lib/supabase');
  mockSupabase.supabase.auth.getSession.mockResolvedValue({
    data: { session: user ? { user, ...mockTestSession } : null },
    error: null,
  });
};

const mockTestSession = {
  access_token: 'test-token',
  refresh_token: 'test-refresh',
  expires_in: 3600,
  token_type: 'bearer',
};

// Performance measurement helper
export const measureRenderTime = (componentName: string, renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  console.log(`${componentName} render time: ${end - start}ms`);
};

// Accessibility testing helper  
export const checkAccessibility = async (container: HTMLElement) => {
  // This could be enhanced with axe-core if needed
  const ariaLabels = container.querySelectorAll('[aria-label]');
  const altTexts = container.querySelectorAll('img[alt]');
  
  return {
    hasAriaLabels: ariaLabels.length > 0,
    hasAltTexts: altTexts.length > 0,
    ariaLabelCount: ariaLabels.length,
    altTextCount: altTexts.length,
  };
};