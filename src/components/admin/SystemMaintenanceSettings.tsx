import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { toast } from "@/hooks/use-toast";
import {
  ShieldAlert,
  WrenchIcon,
  Save,
  AlertTriangle,
  Users,
} from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface MaintenanceFormValues {
  enabled: boolean;
  message: string;
  allowAdmins: boolean;
  kickOutUsers: boolean; // Changed to match system maintenance state
}

export const SystemMaintenanceSettings: React.FC = () => {
  const { systemMaintenance, setSystemMaintenance } = useAuth();
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<MaintenanceFormValues>({
    defaultValues: {
      enabled: systemMaintenance.enabled,
      message: systemMaintenance.message,
      allowAdmins: systemMaintenance.allowAdmins,
      kickOutUsers: systemMaintenance.kickOutUsers, // Using the correct property name
    },
  });

  // Update form when systemMaintenance changes
  useEffect(() => {
    form.reset({
      enabled: systemMaintenance.enabled,
      message: systemMaintenance.message,
      allowAdmins: systemMaintenance.allowAdmins,
      kickOutUsers: systemMaintenance.kickOutUsers, // Using the correct property name
    });
  }, [systemMaintenance, form]);

  const onSubmit = async (data: MaintenanceFormValues) => {
    try {
      setIsSaving(true);

      // Save to context (which will save to Supabase)
      await setSystemMaintenance({
        enabled: data.enabled,
        message: data.message,
        allowAdmins: data.allowAdmins,
        kickOutUsers: data.kickOutUsers, // Using the correct property name
      });

      // Reset the kickOutUsers after applying
      form.setValue("kickOutUsers", false);

      toast({
        title: "Settings Updated",
        description: data.enabled
          ? "System maintenance mode has been activated"
          : "System maintenance mode has been deactivated",
      });
    } catch (error) {
      console.error("Error saving maintenance settings:", error);
      toast({
        title: "Error",
        description: "Failed to update maintenance settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <WrenchIcon className="mr-2 h-5 w-5" />
              System Maintenance Mode
            </CardTitle>
            <CardDescription>
              Configure system maintenance settings and notifications
            </CardDescription>
          </div>

          {form.watch("enabled") && (
            <div className="flex items-center gap-1 text-sm font-medium bg-red-100 text-red-800 px-3 py-1 rounded-md">
              <AlertTriangle className="h-4 w-4" />
              Maintenance Mode Active
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {form.watch("enabled") && (
          <Alert variant="destructive" className="mb-6">
            <AlertTitle className="flex items-center">
              <ShieldAlert className="h-4 w-4 mr-2" />
              Warning: Maintenance Mode is Active
            </AlertTitle>
            <AlertDescription>
              When maintenance mode is active, users cannot log in or access the
              system unless they are administrators (if allowed).
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Maintenance Mode
                    </FormLabel>
                    <FormDescription>
                      Enable maintenance mode to temporarily restrict access to
                      the system
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Maintenance Message</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="The system is currently under maintenance. Please try again later."
                      className="resize-none"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    This message will be displayed to users when they attempt to
                    access the system
                  </FormDescription>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="allowAdmins"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Allow Admins</FormLabel>
                      <FormDescription>
                        Allow administrators to access the system during
                        maintenance
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="kickOutUsers"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 border-orange-200 bg-orange-50">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        Force Logout Users
                      </FormLabel>
                      <FormDescription>
                        Force all non-admin users to log out immediately
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Settings
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
