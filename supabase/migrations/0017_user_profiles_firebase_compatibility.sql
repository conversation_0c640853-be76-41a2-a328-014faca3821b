-- Migration: Make user_profiles table compatible with Firebase Auth IDs

-- First, drop any foreign key constraints that reference this table
ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;

-- Change the id column from UUID to TEXT
ALTER TABLE public.user_profiles ALTER COLUMN id TYPE TEXT;

-- Update RLS policies to work without auth.uid()
DROP POLICY IF EXISTS "Users can view all profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;

-- Create new policies that allow service role access
CREATE POLICY "Service role can manage all profiles"
ON public.user_profiles FOR ALL 
USING (true)
WITH CHECK (true);

-- Note: Authorization will be handled at the application level using Firebase Auth 