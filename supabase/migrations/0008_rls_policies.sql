-- Enable RLS for all tables
ALTER TABLE public.stories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY; -- Commented out as payments table does not exist / not used

-- stories table RLS
CREATE POLICY "Allow public read access to public stories"
ON public.stories
FOR SELECT USING (is_public = true);

CREATE POLICY "Allow authenticated users to read their own stories"
ON public.stories
FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Allow authenticated users to create stories"
ON public.stories
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Allow author to update their stories"
ON public.stories
FOR UPDATE USING (auth.uid() = author_id)
WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Allow author to delete their stories"
ON public.stories
FOR DELETE USING (auth.uid() = author_id);

-- contributions table RLS
CREATE POLICY "Allow public read access to contributions of public stories"
ON public.contributions
FOR SELECT USING (
    EXISTS (
        SELECT 1
        FROM public.stories s
        WHERE s.id = contributions.story_id AND s.is_public = true
    )
);

CREATE POLICY "Allow authenticated users to read contributions of their stories"
ON public.contributions
FOR SELECT USING (
    EXISTS (
        SELECT 1
        FROM public.stories s
        WHERE s.id = contributions.story_id AND s.author_id = auth.uid()
    )
);

CREATE POLICY "Allow authenticated users to insert contributions to in-progress stories"
ON public.contributions
FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' AND
    EXISTS (
        SELECT 1
        FROM public.stories s
        WHERE s.id = contributions.story_id AND s.status = 'in_progress'
        -- Additional checks like max_contributors might be better in application logic or stored procedure
    ) AND
    contributions.user_id = auth.uid() -- Ensure the contributor is the authenticated user
);

-- contributions: NO UPDATE / DELETE policies for now, as per plan


-- votes table RLS
CREATE POLICY "Allow authenticated users to read all votes" -- For vote counts, etc.
ON public.votes
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to insert votes"
ON public.votes
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow user to update their own vote"
ON public.votes
FOR UPDATE USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow user to delete their own vote"
ON public.votes
FOR DELETE USING (auth.uid() = user_id);

-- subscriptions table RLS
CREATE POLICY "Allow user to see their own subscriptions"
ON public.subscriptions
FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Allow user to create their own subscriptions"
ON public.subscriptions
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Allow user to delete their own subscriptions"
ON public.subscriptions
FOR DELETE USING (auth.uid() = user_id);

-- payments table RLS (Commented out as payments table does not exist / not used)
-- CREATE POLICY "Allow user to see their own payment info"
-- ON public.payments
-- FOR SELECT USING (auth.uid() = user_id);
-- 
-- CREATE POLICY "Allow user to insert their own payment info (restricted use)"
-- ON public.payments
-- FOR INSERT WITH CHECK (auth.uid() = user_id);
-- 
-- CREATE POLICY "Allow user to update their own payment info (restricted use)"
-- ON public.payments
-- FOR UPDATE USING (auth.uid() = user_id)
-- WITH CHECK (auth.uid() = user_id);

-- payments: NO DELETE policy for users for now, as per plan. 