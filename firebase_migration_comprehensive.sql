-- Firebase Auth Compatibility Migration - Comprehensive Version
-- This finds and handles ALL tables that reference user IDs

DO $$
DECLARE
    r RECORD;
    col_record RECORD;
BEGIN
    -- Step 1: Find and drop ALL policies that might reference user-related columns
    RAISE NOTICE 'Step 1: Dropping all policies that might interfere...';
    
    -- Drop policies on any table that has user_id, author_id, actor_id, or id columns
    FOR r IN 
        SELECT DISTINCT pol.tablename, pol.policyname
        FROM pg_policies pol
        JOIN information_schema.columns col 
            ON col.table_name = pol.tablename 
            AND col.table_schema = 'public'
        WHERE col.column_name IN ('id', 'user_id', 'author_id', 'actor_id', 'reporter_id', 'moderator_id')
        AND pol.schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.%I', r.policyname, r.tablename);
        RAISE NOTICE 'Dropped policy % on table %', r.policyname, r.tablename;
    END LOOP;
    
    -- Step 2: Drop triggers and functions
    RAISE NOTICE 'Step 2: Dropping triggers and functions...';
    DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
    DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
    
    -- Step 3: Find and drop ALL foreign key constraints that reference user_profiles.id or auth.users
    RAISE NOTICE 'Step 3: Dropping foreign key constraints...';
    FOR r IN
        SELECT conname, conrelid::regclass AS table_name
        FROM pg_constraint
        WHERE contype = 'f'
        AND (confrelid = 'public.user_profiles'::regclass OR confrelid = 'auth.users'::regclass)
    LOOP
        EXECUTE format('ALTER TABLE %s DROP CONSTRAINT IF EXISTS %I', r.table_name, r.conname);
        RAISE NOTICE 'Dropped constraint % on table %', r.conname, r.table_name;
    END LOOP;
    
    -- Step 4: Drop specific known constraints
    ALTER TABLE public.notifications 
        DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_story_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_contribution_id_fkey;
    
    -- Step 5: Drop indexes on user-related columns
    RAISE NOTICE 'Step 4: Dropping indexes...';
    DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
    DROP INDEX IF EXISTS idx_notifications_user_id_created_at;
    
    -- Step 6: Alter column types for all tables with user references
    RAISE NOTICE 'Step 5: Altering column types...';
    
    -- Core tables we know about
    ALTER TABLE public.notifications 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT,
        ALTER COLUMN actor_id TYPE TEXT USING actor_id::TEXT;
    
    ALTER TABLE public.user_profiles 
        ALTER COLUMN id TYPE TEXT USING id::TEXT;
    
    ALTER TABLE public.stories 
        ALTER COLUMN author_id TYPE TEXT USING author_id::TEXT;
    
    ALTER TABLE public.contributions 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;
    
    -- Find and alter any other tables with user_id columns
    FOR col_record IN
        SELECT table_name, column_name
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND column_name IN ('user_id', 'author_id', 'reporter_id', 'moderator_id', 'actor_id')
        AND data_type = 'uuid'
        AND table_name NOT IN ('notifications', 'stories', 'contributions')
    LOOP
        EXECUTE format('ALTER TABLE public.%I ALTER COLUMN %I TYPE TEXT USING %I::TEXT', 
                      col_record.table_name, col_record.column_name, col_record.column_name);
        RAISE NOTICE 'Altered column % in table %', col_record.column_name, col_record.table_name;
    END LOOP;
    
    -- Step 7: Recreate indexes
    RAISE NOTICE 'Step 6: Recreating indexes...';
    CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
    CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);
    
    -- Step 8: Create simple service role policies for known tables
    RAISE NOTICE 'Step 7: Creating new policies...';
    
    -- Create policies for our main tables
    CREATE POLICY "Service role access" ON public.notifications FOR ALL USING (true) WITH CHECK (true);
    CREATE POLICY "Service role access" ON public.user_profiles FOR ALL USING (true) WITH CHECK (true);
    CREATE POLICY "Service role access" ON public.stories FOR ALL USING (true) WITH CHECK (true);
    CREATE POLICY "Service role access" ON public.contributions FOR ALL USING (true) WITH CHECK (true);
    
    -- Create policies for any other affected tables
    FOR r IN
        SELECT DISTINCT table_name
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND column_name IN ('user_id', 'author_id', 'reporter_id', 'moderator_id', 'actor_id')
        AND table_name NOT IN ('notifications', 'user_profiles', 'stories', 'contributions')
    LOOP
        EXECUTE format('CREATE POLICY "Service role access" ON public.%I FOR ALL USING (true) WITH CHECK (true)', r.table_name);
        RAISE NOTICE 'Created policy for table %', r.table_name;
    END LOOP;
    
    RAISE NOTICE 'Firebase Auth compatibility migration completed successfully!';
END $$; 