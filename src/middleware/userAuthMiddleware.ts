import { Request, Response, NextFunction } from 'express';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

// Extend Express Request to include authenticated user info
declare global {
  namespace Express {
    interface Request {
      user?: User;
      userId?: string;
    }
  }
}

export class UserAuthMiddleware {
  
  // Extract JWT token from request
  static extractToken(req: Request): string | null {
    // Check Authorization header: Bearer <jwt-token>
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    
    // Check cookie (for browser requests)
    const cookieToken = req.cookies?.['sb-access-token'];
    if (cookieToken) {
      return cookieToken;
    }
    
    return null;
  }
  
  // Validate JWT token and get user
  static async validateToken(token: string): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data, error } = await supabase.auth.getUser(token);
      
      if (error) {
        return { user: null, error: error.message };
      }
      
      if (!data.user) {
        return { user: null, error: 'Invalid token' };
      }
      
      return { user: data.user, error: null };
    } catch (error) {
      return { 
        user: null, 
        error: error instanceof Error ? error.message : 'Token validation failed' 
      };
    }
  }
  
  // Main authentication middleware
  static requireAuth(options: {
    optional?: boolean;
    adminOnly?: boolean;
  } = {}) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const token = UserAuthMiddleware.extractToken(req);
        
        // Handle missing token
        if (!token) {
          if (options.optional) {
            return next();
          }
          
          return res.status(401).json({
            error: 'Authentication required',
            message: 'Please provide a valid JWT token in the Authorization header',
            code: 'AUTH_TOKEN_MISSING'
          });
        }
        
        // Validate token
        const { user, error } = await UserAuthMiddleware.validateToken(token);
        
        if (error || !user) {
          return res.status(401).json({
            error: 'Invalid authentication token',
            message: error || 'Token validation failed',
            code: 'AUTH_TOKEN_INVALID'
          });
        }
        
        // Check admin requirements
        if (options.adminOnly) {
          const isAdmin = user.user_metadata?.role === 'admin' || 
                         user.app_metadata?.role === 'admin' ||
                         user.email === process.env.ADMIN_EMAIL;
          
          if (!isAdmin) {
            return res.status(403).json({
              error: 'Admin access required',
              message: 'This endpoint requires administrator privileges',
              code: 'ADMIN_REQUIRED'
            });
          }
        }
        
        // Attach user info to request
        req.user = user;
        req.userId = user.id;
        
        next();
        
      } catch (error) {
        console.error('❌ User authentication error:', error);
        
        res.status(500).json({
          error: 'Authentication service error',
          message: 'Please try again later',
          code: 'AUTH_SERVICE_ERROR'
        });
      }
    };
  }
  
  // Middleware to ensure user can only access their own resources
  static requireOwnership(resourceUserIdParam: string = 'userId') {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.user || !req.userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User authentication is required for this operation',
          code: 'AUTH_REQUIRED'
        });
      }
      
      // Get the resource user ID from params, body, or query
      const resourceUserId = req.params[resourceUserIdParam] || 
                            req.body[resourceUserIdParam] || 
                            req.query[resourceUserIdParam];
      
      // If no resource user ID specified, assume it's for the authenticated user
      if (!resourceUserId) {
        return next();
      }
      
      // Check if the authenticated user owns the resource
      if (req.userId !== resourceUserId) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'You can only access your own resources',
          code: 'OWNERSHIP_REQUIRED'
        });
      }
      
      next();
    };
  }
  
  // Middleware to validate API key ownership
  static async requireApiKeyOwnership(req: Request, res: Response, next: NextFunction) {
    try {
      if (!req.user || !req.userId) {
        return res.status(401).json({
          error: 'Authentication required',
          message: 'User authentication is required for API key operations',
          code: 'AUTH_REQUIRED'
        });
      }
      
      const keyId = req.params.keyId;
      
      if (!keyId) {
        return next(); // No specific key ID, proceed (for operations like creating new keys)
      }
      
      // Query the database to check if the API key belongs to the user
      const { data: apiKey, error } = await supabase
        .from('api_keys')
        .select('user_id')
        .eq('id', keyId)
        .single();
      
      if (error) {
        if (error.code === 'PGRST116') { // Not found
          return res.status(404).json({
            error: 'API key not found',
            message: 'The specified API key does not exist',
            code: 'API_KEY_NOT_FOUND'
          });
        }
        
        throw error;
      }
      
      if (apiKey.user_id !== req.userId) {
        return res.status(403).json({
          error: 'Access denied',
          message: 'You can only access your own API keys',
          code: 'API_KEY_OWNERSHIP_REQUIRED'
        });
      }
      
      next();
      
    } catch (error) {
      console.error('❌ API key ownership validation error:', error);
      
      res.status(500).json({
        error: 'Ownership validation error',
        message: 'Unable to validate API key ownership',
        code: 'OWNERSHIP_VALIDATION_ERROR'
      });
    }
  }
  
  // Convenience methods for common use cases
  static requireUser() {
    return UserAuthMiddleware.requireAuth({ optional: false });
  }
  
  static requireAdmin() {
    return UserAuthMiddleware.requireAuth({ adminOnly: true });
  }
  
  static optionalAuth() {
    return UserAuthMiddleware.requireAuth({ optional: true });
  }
  
  // Health check for user auth service
  static healthCheck() {
    return async (req: Request, res: Response) => {
      try {
        // Test Supabase connection
        const { data, error } = await supabase.auth.getSession();
        
        res.json({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          service: 'user-auth',
          features: {
            jwtValidation: true,
            supabaseConnection: !error,
            ownershipValidation: true,
            adminValidation: true
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          service: 'user-auth',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };
  }
}

export default UserAuthMiddleware; 