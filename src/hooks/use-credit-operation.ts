import { useState, useCallback } from "react";
import { useSubscription, CreditAction } from "@/hooks/use-subscription";
import { useToast } from "@/hooks/use-toast";

interface UseCreditOperationResult {
  handleCreditOperation: (
    action: CreditAction,
    quantity?: number,
  ) => Promise<boolean>;
  isUsingCredits: boolean;
}

export const useCreditOperation = (): UseCreditOperationResult => {
  const [isUsingCredits, setIsUsingCredits] = useState(false);
  const { useCredits, credits, CREDIT_COSTS } = useSubscription();
  const { toast } = useToast();

  const handleCreditOperation = useCallback(
    async (action: CreditAction, quantity = 1): Promise<boolean> => {
      if (isUsingCredits) return false;

      const cost = CREDIT_COSTS[action] * quantity;
      if (credits < cost) {
        toast({
          title: "Insufficient Credits",
          description: `You need ${cost} credits for this operation.`,
          variant: "destructive",
        });
        return false;
      }

      setIsUsingCredits(true);
      try {
        const success = await useCredits(action, quantity);
        if (!success) {
          toast({
            title: "Credit Usage Failed",
            description: "Failed to use credits for the operation.",
            variant: "destructive",
          });
          return false;
        }
        return true;
      } catch (error) {
        console.error("Error using credits:", error);
        toast({
          title: "Error",
          description: "Failed to use credits for the operation.",
          variant: "destructive",
        });
        return false;
      } finally {
        setIsUsingCredits(false);
      }
    },
    [isUsingCredits, useCredits, credits, CREDIT_COSTS, toast],
  );

  return {
    handleCreditOperation,
    isUsingCredits,
  };
};
