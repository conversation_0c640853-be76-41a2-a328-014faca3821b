[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [navigation-menu](../README.md) / NavigationMenuList

# Variable: NavigationMenuList

> `const` **NavigationMenuList**: `ForwardRefExoticComponent`\<`Omit`\<`NavigationMenuListProps` & `RefAttributes`\<`HTMLUListElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLUListElement`\>\>

Defined in: [src/components/ui/navigation-menu.tsx:26](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/navigation-menu.tsx#L26)
