import { toast } from "@/hooks/use-toast";
import { submitContentReport } from "./strikeService";

// List of words to filter (would be expanded in production)
const PROHIBITED_WORDS = [
  "badword1",
  "badword2",
  "badword3",
  // This would be a much more comprehensive list in production
];

/**
 * Check if content contains prohibited words
 */
export const containsProhibitedContent = (content: string): boolean => {
  const lowerContent = content.toLowerCase();

  return PROHIBITED_WORDS.some((word) =>
    lowerContent.includes(word.toLowerCase()),
  );
};

/**
 * Moderate content for inappropriate language
 * Returns the moderated content and a flag if content was modified
 */
export const moderateContent = (
  content: string,
): {
  moderated: string;
  wasModified: boolean;
  isRejected: boolean;
} => {
  // Check for prohibited content
  if (containsProhibitedContent(content)) {
    // For strict moderation, reject the content entirely
    return {
      moderated: content,
      wasModified: false,
      isRejected: true,
    };
  }

  // For a more lenient approach, we could replace prohibited words
  // This is left as a placeholder for future implementation

  return {
    moderated: content,
    wasModified: false,
    isRejected: false,
  };
};

/**
 * Report content for manual review - Updated to use new strike system
 */
export const reportContent = async (
  contentId: string,
  contentType: "story" | "contribution" | "comment",
  reason: string,
  reportedBy: string,
): Promise<boolean> => {
  try {
    // Use the new strike service for content reporting
    const result = await submitContentReport(contentType, contentId, reason);

    if (result.success) {
      toast({
        title: "Content reported",
        description: "Thank you for helping keep our platform safe. We'll review this content promptly.",
      });
      return true;
    } else {
      toast({
        title: "Failed to report content",
        description: result.error || "Please try again later.",
        variant: "destructive",
      });
      return false;
    }
  } catch (error) {
    console.error("Error reporting content:", error);

    toast({
      title: "Failed to report content",
      description: "Please try again later.",
      variant: "destructive",
    });

    return false;
  }
};

/**
 * Enhanced rate limiting with user tracking
 */
const userRateLimits = new Map<string, { count: number; lastReset: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_ACTIONS_PER_WINDOW = 10;

export const checkRateLimit = (userId: string): boolean => {
  const now = Date.now();
  const userLimit = userRateLimits.get(userId);

  if (!userLimit || now - userLimit.lastReset > RATE_LIMIT_WINDOW) {
    // Reset or initialize rate limit
    userRateLimits.set(userId, { count: 1, lastReset: now });
    return true;
  }

  if (userLimit.count >= MAX_ACTIONS_PER_WINDOW) {
    return false;
  }

  // Increment count
  userLimit.count++;
  return true;
};

/**
 * Get content moderation status
 */
export const getContentModerationStatus = async (
  contentType: "story" | "contribution" | "comment",
  contentId: string
): Promise<{
  hasReports: boolean;
  reportCount: number;
  isUnderReview: boolean;
}> => {
  try {
    // This would fetch from the content_reports table
    // For now, return default values
    return {
      hasReports: false,
      reportCount: 0,
      isUnderReview: false
    };
  } catch (error) {
    console.error("Error getting content moderation status:", error);
    return {
      hasReports: false,
      reportCount: 0,
      isUnderReview: false
    };
  }
};

/**
 * Clean up expired rate limits
 */
export const cleanupRateLimits = (): void => {
  const now = Date.now();
  for (const [userId, limit] of userRateLimits.entries()) {
    if (now - limit.lastReset > RATE_LIMIT_WINDOW) {
      userRateLimits.delete(userId);
    }
  }
};
