-- Add terms_accepted column to user_profiles table
-- This migration adds a boolean column to track whether users have accepted the terms and conditions

ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS terms_accepted BOOLEAN DEFAULT FALSE;

COMMENT ON COLUMN public.user_profiles.terms_accepted IS 'Indicates whether the user has accepted the terms and conditions';

-- Update existing users to have terms_accepted = false by default
-- This ensures that existing users will be prompted to accept terms on their next login
UPDATE public.user_profiles 
SET terms_accepted = FALSE 
WHERE terms_accepted IS NULL;
