describe('Navigation and Responsive Design', () => {
  beforeEach(() => {
    cy.mockSupabaseAuth()
  })

  describe('Desktop Navigation', () => {
    beforeEach(() => {
      cy.viewport(1280, 720)
    })

    it('should display main navigation links', () => {
      cy.visitAndWait('/')
      
      cy.get('[data-testid="nav-home"]').should('be.visible')
      cy.get('[data-testid="nav-gallery"]').should('be.visible')
      cy.get('[data-testid="nav-create"]').should('be.visible')
      cy.get('[data-testid="nav-pricing"]').should('be.visible')
    })

    it('should navigate between pages correctly', () => {
      cy.visitAndWait('/')
      
      cy.get('[data-testid="nav-gallery"]').click()
      cy.url().should('include', '/gallery')
      
      cy.get('[data-testid="nav-create"]').click()
      cy.url().should('include', '/create-story')
      
      cy.get('[data-testid="nav-pricing"]').click()
      cy.url().should('include', '/pricing')
      
      cy.get('[data-testid="nav-home"]').click()
      cy.url().should('eq', Cypress.config().baseUrl + '/')
    })

    it('should show user menu when authenticated', () => {
      cy.visitAndWait('/dashboard')
      
      cy.get('[data-testid="user-menu"]').should('be.visible')
      cy.get('[data-testid="user-menu"]').click()
      
      cy.get('[data-testid="nav-dashboard"]').should('be.visible')
      cy.get('[data-testid="nav-profile"]').should('be.visible')
      cy.get('[data-testid="logout-button"]').should('be.visible')
    })

    it('should highlight active navigation item', () => {
      cy.visitAndWait('/gallery')
      cy.get('[data-testid="nav-gallery"]').should('have.class', 'active')
      
      cy.visitAndWait('/create-story')
      cy.get('[data-testid="nav-create"]').should('have.class', 'active')
    })
  })

  describe('Mobile Navigation', () => {
    beforeEach(() => {
      cy.viewport(375, 667) // iPhone SE size
    })

    it('should show mobile menu toggle', () => {
      cy.visitAndWait('/')
      
      cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible')
      cy.get('[data-testid="desktop-nav"]').should('not.be.visible')
    })

    it('should open and close mobile menu', () => {
      cy.visitAndWait('/')
      
      // Menu should be closed initially
      cy.get('[data-testid="mobile-menu"]').should('not.be.visible')
      
      // Open menu
      cy.get('[data-testid="mobile-menu-toggle"]').click()
      cy.get('[data-testid="mobile-menu"]').should('be.visible')
      
      // Close menu
      cy.get('[data-testid="mobile-menu-close"]').click()
      cy.get('[data-testid="mobile-menu"]').should('not.be.visible')
    })

    it('should navigate from mobile menu', () => {
      cy.visitAndWait('/')
      
      cy.get('[data-testid="mobile-menu-toggle"]').click()
      cy.get('[data-testid="mobile-nav-gallery"]').click()
      
      cy.url().should('include', '/gallery')
      cy.get('[data-testid="mobile-menu"]').should('not.be.visible')
    })

    it('should be usable on touch devices', () => {
      cy.visitAndWait('/gallery')
      
      // Test touch interactions
      cy.get('[data-testid="story-card"]').first().trigger('touchstart')
      cy.get('[data-testid="story-card"]').first().trigger('touchend')
      cy.get('[data-testid="story-card"]').first().click()
      
      cy.url().should('include', '/story/')
    })
  })

  describe('Tablet Navigation', () => {
    beforeEach(() => {
      cy.viewport(768, 1024) // iPad size
    })

    it('should adapt layout for tablet size', () => {
      cy.visitAndWait('/gallery')
      
      cy.get('[data-testid="story-grid"]').should('be.visible')
      // Should show 2-3 columns on tablet
      cy.get('[data-testid="story-card"]').should('have.css', 'width').and('match', /\d+px/)
    })

    it('should show appropriate navigation for tablet', () => {
      cy.visitAndWait('/')
      
      // Tablet might show desktop nav or mobile nav depending on design
      cy.get('[data-testid="nav-home"]').should('be.visible')
    })
  })

  describe('Page Layouts and Components', () => {
    it('should have proper page titles', () => {
      const pages = [
        { path: '/', title: 'Word by Word Story' },
        { path: '/gallery', title: 'Story Gallery' },
        { path: '/pricing', title: 'Pricing' },
        { path: '/login', title: 'Sign In' },
        { path: '/register', title: 'Create Account' }
      ]

      pages.forEach(page => {
        cy.visitAndWait(page.path)
        cy.title().should('include', page.title)
      })
    })

    it('should load all critical CSS and fonts', () => {
      cy.visitAndWait('/')
      
      // Check that CSS is loaded
      cy.get('body').should('have.css', 'font-family')
      cy.get('body').should('have.css', 'margin')
      
      // Check for custom CSS classes
      cy.get('[class*="text-"]').should('exist') // Tailwind classes
    })

    it('should handle loading states gracefully', () => {
      cy.visitAndWait('/gallery')
      
      // Should show loading state initially
      cy.get('[data-testid="loading-spinner"]').should('be.visible')
      
      // Then show content
      cy.waitForLoadingToFinish()
      cy.get('[data-testid="story-card"]').should('be.visible')
    })

    it('should display error states appropriately', () => {
      // Mock an error response
      cy.intercept('GET', '**/stories**', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('getStoriesError')
      
      cy.visitAndWait('/gallery')
      cy.wait('@getStoriesError')
      
      cy.contains('Error loading stories').should('be.visible')
      cy.get('[data-testid="retry-button"]').should('be.visible')
    })
  })

  describe('SEO and Meta Tags', () => {
    it('should have proper meta tags', () => {
      cy.visitAndWait('/')
      
      cy.get('meta[name="description"]').should('exist')
      cy.get('meta[name="viewport"]').should('exist')
      cy.get('meta[charset]').should('exist')
    })

    it('should have Open Graph tags', () => {
      cy.visitAndWait('/')
      
      cy.get('meta[property="og:title"]').should('exist')
      cy.get('meta[property="og:description"]').should('exist')
      cy.get('meta[property="og:type"]').should('exist')
    })
  })

  describe('Accessibility', () => {
    it('should have proper heading hierarchy', () => {
      cy.visitAndWait('/')
      
      cy.get('h1').should('exist')
      cy.get('h1').should('have.length', 1)
    })

    it('should have accessible form labels', () => {
      cy.visitAndWait('/login')
      
      cy.get('label[for="email"]').should('exist')
      cy.get('label[for="password"]').should('exist')
      cy.get('input#email').should('exist')
      cy.get('input#password').should('exist')
    })

    it('should have keyboard navigation support', () => {
      cy.visitAndWait('/')
      
      // Test tab navigation
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Test enter key on buttons
      cy.get('[data-testid="nav-gallery"]').focus().type('{enter}')
      cy.url().should('include', '/gallery')
    })

    it('should have sufficient color contrast', () => {
      cy.visitAndWait('/')
      
      // Check text has sufficient contrast (this would need actual color analysis)
      cy.get('body').should('have.css', 'color')
      cy.get('body').should('have.css', 'background-color')
    })

    it('should have alt text for images', () => {
      cy.visitAndWait('/gallery')
      
      cy.get('img').each(($img) => {
        cy.wrap($img).should('have.attr', 'alt')
      })
    })
  })
})