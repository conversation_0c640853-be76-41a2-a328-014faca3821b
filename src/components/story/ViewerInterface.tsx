/**
 * Viewer Interface Component
 * Specialized interface for read-only participants
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Eye,
  EyeOff,
  Heart,
  MessageCircle,
  Users,
  Info,
  AlertCircle,
  ThumbsUp,
  Share2,
  Bookmark,
} from 'lucide-react';
import { usePermissions } from '@/hooks/use-permissions';

interface ViewerInterfaceProps {
  storyId: string;
  storyTitle: string;
  children?: React.ReactNode;
  onRequestContributorRole?: () => void;
  onShareStory?: () => void;
  onBookmarkStory?: () => void;
  className?: string;
}

export const ViewerInterface: React.FC<ViewerInterfaceProps> = ({
  storyId,
  storyTitle,
  children,
  onRequestContributorRole,
  onShareStory,
  onBookmarkStory,
  className,
}) => {
  const { permissions, summary, isViewer, canPerform } = usePermissions({ storyId });

  if (!isViewer) {
    return <>{children}</>;
  }

  return (
    <div className={`viewer-interface ${className || ''}`}>
      {/* Viewer Status Banner */}
      <Card className="mb-4 border-blue-200 bg-blue-50/50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg">Viewing: {storyTitle}</CardTitle>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Read-Only
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              {onShareStory && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onShareStory}
                        className="h-8"
                      >
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Share this story</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              
              {onBookmarkStory && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onBookmarkStory}
                        className="h-8"
                      >
                        <Bookmark className="w-4 h-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Bookmark story</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
          
          <CardDescription className="flex items-center gap-2 text-blue-600">
            <Info className="w-4 h-4" />
            You are following this story as it develops in real-time
          </CardDescription>
        </CardHeader>
        
        {summary && (
          <CardContent className="pt-0">
            <div className="space-y-2">
              <div>
                <h4 className="text-sm font-medium text-blue-800">What you can do:</h4>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  {summary.capabilities.map((capability, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      {capability}
                    </li>
                  ))}
                </ul>
              </div>
              
              {summary.restrictions.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-blue-800">Restrictions:</h4>
                  <ul className="text-sm text-blue-600 mt-1 space-y-1">
                    {summary.restrictions.map((restriction, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <EyeOff className="w-3 h-3" />
                        {restriction}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            
            {/* Request Contributor Role */}
            {onRequestContributorRole && (
              <div className="mt-4 pt-3 border-t border-blue-200">
                <p className="text-sm text-blue-700 mb-2">
                  Want to contribute to this story?
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onRequestContributorRole}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  <Users className="w-4 h-4 mr-2" />
                  Request Contributor Access
                </Button>
              </div>
            )}
          </CardContent>
        )}
      </Card>
      
      {/* Main Content with Viewer Overlay */}
      <div className="relative">
        {children}
        
        {/* Viewer-specific overlays and modifications are handled by child components */}
      </div>
    </div>
  );
};

/**
 * Viewer Message Input Component
 * Replaces normal message input for viewers
 */
interface ViewerMessageInputProps {
  onRequestMessagingAccess?: () => void;
  canReact?: boolean;
  className?: string;
}

export const ViewerMessageInput: React.FC<ViewerMessageInputProps> = ({
  onRequestMessagingAccess,
  canReact = true,
  className,
}) => {
  return (
    <div className={`viewer-message-input ${className || ''}`}>
      <Alert className="border-blue-200 bg-blue-50">
        <AlertCircle className="w-4 h-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Read-only participant</p>
              <p className="text-sm text-blue-600 mt-1">
                {canReact 
                  ? 'You can react to messages but cannot send your own'
                  : 'You can follow the conversation but cannot participate'
                }
              </p>
            </div>
            
            {onRequestMessagingAccess && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRequestMessagingAccess}
                className="border-blue-300 text-blue-700 hover:bg-blue-100 ml-4"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Request Access
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    </div>
  );
};

/**
 * Viewer Contribution Prompt Component
 * Shows when viewer tries to contribute
 */
interface ViewerContributionPromptProps {
  storyTitle: string;
  onRequestContributorAccess?: () => void;
  onContactCreator?: () => void;
  className?: string;
}

export const ViewerContributionPrompt: React.FC<ViewerContributionPromptProps> = ({
  storyTitle,
  onRequestContributorAccess,
  onContactCreator,
  className,
}) => {
  return (
    <Card className={`viewer-contribution-prompt border-amber-200 ${className || ''}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-amber-800">
          <Eye className="w-5 h-5" />
          Read-Only Access
        </CardTitle>
        <CardDescription className="text-amber-700">
          You are currently viewing "{storyTitle}" as a read-only participant
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-amber-700">
            To add content to this story, you need contributor access. The story creator can upgrade your role.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-2">
            {onRequestContributorAccess && (
              <Button
                variant="outline"
                onClick={onRequestContributorAccess}
                className="border-amber-300 text-amber-700 hover:bg-amber-100"
              >
                <Users className="w-4 h-4 mr-2" />
                Request Contributor Access
              </Button>
            )}
            
            {onContactCreator && (
              <Button
                variant="outline"
                onClick={onContactCreator}
                className="border-amber-300 text-amber-700 hover:bg-amber-100"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Story Creator
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Viewer Reaction Component
 * Simplified reaction interface for viewers
 */
interface ViewerReactionBarProps {
  messageId: string;
  currentReactions?: { [emoji: string]: number };
  userReactions?: string[];
  onReact?: (emoji: string) => void;
  className?: string;
}

export const ViewerReactionBar: React.FC<ViewerReactionBarProps> = ({
  messageId,
  currentReactions = {},
  userReactions = [],
  onReact,
  className,
}) => {
  const commonReactions = ['👍', '❤️', '😊', '👏', '🔥'];

  return (
    <div className={`viewer-reaction-bar flex items-center gap-1 ${className || ''}`}>
      {commonReactions.map((emoji) => {
        const count = currentReactions[emoji] || 0;
        const isReacted = userReactions.includes(emoji);
        
        return (
          <TooltipProvider key={emoji}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isReacted ? "default" : "outline"}
                  size="sm"
                  onClick={() => onReact?.(emoji)}
                  className={`h-8 px-2 text-xs ${
                    isReacted 
                      ? 'bg-blue-100 border-blue-300 text-blue-800' 
                      : 'border-gray-300 text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="mr-1">{emoji}</span>
                  {count > 0 && <span>{count}</span>}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isReacted ? `Remove ${emoji} reaction` : `React with ${emoji}`}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
};

/**
 * Permission Gate Component
 * Conditionally renders content based on permissions
 */
interface PermissionGateProps {
  storyId: string;
  requireRole?: 'creator' | 'contributor' | 'viewer';
  requirePermission?: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  storyId,
  requireRole,
  requirePermission,
  fallback,
  children,
}) => {
  const { permissions, canPerform } = usePermissions({ storyId });

  // Check role requirement
  if (requireRole && permissions?.role !== requireRole) {
    return <>{fallback}</>;
  }

  // Check permission requirement
  if (requirePermission && !canPerform(requirePermission as keyof typeof permissions)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};