import React from 'react';

interface Participant {
  userId: string;
  username: string;
  isOnline: boolean;
  isTyping: boolean;
  lastActive: string;
}

interface ParticipantsListProps {
  participants: Participant[];
  currentTurnUserId: string;
  currentUserId: string;
}

const ParticipantsList: React.FC<ParticipantsListProps> = ({
  participants,
  currentTurnUserId,
  currentUserId,
}) => {
  // Sort participants: current user first, then by online status, then alphabetically
  const sortedParticipants = [...participants].sort((a, b) => {
    // Current user first
    if (a.userId === currentUserId) return -1;
    if (b.userId === currentUserId) return 1;
    
    // Then by online status
    if (a.isOnline && !b.isOnline) return -1;
    if (!a.isOnline && b.isOnline) return 1;
    
    // Then alphabetically by username
    return a.username.localeCompare(b.username);
  });

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold mb-3">Participants</h3>
      <ul className="space-y-2">
        {sortedParticipants.map((participant) => (
          <li 
            key={participant.userId}
            className={`flex items-center justify-between p-2 rounded-md ${
              participant.userId === currentTurnUserId 
                ? 'bg-blue-50 border border-blue-200' 
                : ''
            }`}
          >
            <div className="flex items-center">
              <div 
                className={`w-3 h-3 rounded-full mr-2 ${
                  participant.isOnline ? 'bg-green-500' : 'bg-gray-300'
                }`}
              />
              <span className="font-medium">
                {participant.username}
                {participant.userId === currentUserId && ' (You)'}
              </span>
            </div>
            <div className="flex items-center">
              {participant.isTyping && (
                <span className="text-xs text-gray-500 mr-2 italic">typing...</span>
              )}
              {participant.userId === currentTurnUserId && (
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  Current Turn
                </span>
              )}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ParticipantsList;
