// Comprehensive fix for Rollup ESM issues
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { execSync } from "child_process";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("Starting Rollup ESM fix...");

// Function to patch a file with a specific replacement
function patchFile(filePath, searchText, replaceText) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return false;
  }

  // Create backup
  const backupPath = `${filePath}.backup`;
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(filePath, backupPath);
    console.log(`Backup created for ${filePath}`);
  }

  // Read the file content
  const content = fs.readFileSync(filePath, "utf8");

  // Check if the search text exists
  if (!content.includes(searchText)) {
    console.log(`Search text not found in ${filePath}`);
    return false;
  }

  // Replace the text
  const patchedContent = content.replace(searchText, replaceText);

  // Write the patched content
  fs.writeFileSync(filePath, patchedContent);
  console.log(`Successfully patched ${filePath}`);
  return true;
}

// Patch the native.js file
const nativeJsPath = path.join(
  __dirname,
  "node_modules",
  "rollup",
  "dist",
  "native.js",
);
if (fs.existsSync(nativeJsPath)) {
  console.log("Patching native.js...");

  const patchedContent = `// This is a patched version of the Rollup native.js file
'use strict';

// Mock the native module
const native = {
  parse: null,
  parseAsync: async () => null
};

// Export the mock
module.exports = native;`;

  fs.writeFileSync(nativeJsPath, patchedContent);
  console.log("native.js patched successfully");
}

// Patch the parseAst.js file
const parseAstPath = path.join(
  __dirname,
  "node_modules",
  "rollup",
  "dist",
  "es",
  "shared",
  "parseAst.js",
);
patchFile(
  parseAstPath,
  "import { parse, parseAsync } from '../../native.js';",
  `// Patched import to fix CommonJS/ESM mismatch
import pkg from '../../native.js';
const { parse = () => null, parseAsync = () => Promise.resolve(null) } = pkg || {};`,
);

// Create a patched vite script
console.log("Creating patched Vite script...");
const viteScriptContent = `#!/usr/bin/env node

// Patched Vite script to use JS implementation of Rollup
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = 'true';
process.env.ROLLUP_NATIVE_DISABLE = 'true';

// Import and run the original vite CLI
import { createServer } from 'vite';

// Create and start the server
createServer().then(server => server.listen());
console.log('Vite server started...');`;

fs.writeFileSync("vite-patched.mjs", viteScriptContent);
console.log("Created patched Vite script");

// Update package.json to add a patched dev script
console.log("Updating package.json...");
const packageJsonPath = path.join(__dirname, "package.json");
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

// Add patched dev script
packageJson.scripts = {
  ...packageJson.scripts,
  "dev:patched": "node vite-patched.mjs",
};

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log("Updated package.json with patched dev script");

// Create a simple batch file to run the patched dev script
const batchContent = `@echo off
echo ===================================================
echo Word by Word Story - Patched Vite Server
echo ===================================================

echo Starting patched Vite server...
npm run dev:patched

pause`;

fs.writeFileSync("run-vite-patched.bat", batchContent);
console.log("Created run-vite-patched.bat");

console.log("\nFix completed! You can now run the project with:");
console.log("run-vite-patched.bat");
console.log("\nOr use the Express server with:");
console.log("run-express.bat");
