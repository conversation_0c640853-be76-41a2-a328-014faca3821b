import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { User, LogOut, LogIn, Crown, Settings } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const UserMenu = () => {
  const { user, isAuthenticated, logout, isAdmin } = useAuth();

  return (
    <div className="flex items-center gap-3">
      {isAuthenticated ? (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="flex items-center gap-2 text-white hover:text-literary-gold group transition-all">
              <div className="relative">
                {user?.profilePicture ? (
                  <img
                    src={user.profilePicture}
                    alt={user.email || "User"}
                    className="w-8 h-8 rounded-full object-cover border-2 border-literary-gold/50 group-hover:border-literary-gold transition-all"
                  />
                ) : (
                  <User
                    size={20}
                    className="group-hover:scale-110 transition-transform"
                  />
                )}
                {user?.tier !== "free" && (
                  <span className="absolute -top-1 -right-1 bg-literary-gold text-literary-navy rounded-full p-0.5 animate-pulse">
                    <Crown size={10} />
                  </span>
                )}
                {isAdmin() && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5">
                    <Crown size={10} />
                  </span>
                )}
              </div>
              <span className="hidden md:inline-block group-hover:text-literary-gold transition-colors">
                {user?.email || "User"} {isAdmin() && "(Admin)"}
              </span>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link
                to="/profile"
                className="flex items-center gap-2 cursor-pointer"
              >
                <User size={16} />
                <span>Profile</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link
                to="/settings"
                className="flex items-center gap-2 cursor-pointer"
              >
                <Settings size={16} />
                <span>Settings</span>
              </Link>
            </DropdownMenuItem>
            {isAdmin() && (
              <DropdownMenuItem asChild>
                <Link
                  to="/admin"
                  className="flex items-center gap-2 cursor-pointer"
                >
                  <Crown size={16} />
                  <span>Admin Panel</span>
                </Link>
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => logout()}
              className="text-red-600 focus:text-red-600 cursor-pointer"
            >
              <LogOut size={16} className="mr-2" />
              <span>Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <Link to="/login">
          <Button
            variant="outline"
            className="
              bg-transparent text-white border-white/50 
              hover:bg-literary-gold hover:text-literary-navy 
              hover:border-transparent 
              transition-all duration-300 
              group
            "
          >
            <LogIn
              size={16}
              className="mr-2 group-hover:rotate-6 transition-transform"
            />
            Login
          </Button>
        </Link>
      )}
    </div>
  );
};

export default UserMenu;
