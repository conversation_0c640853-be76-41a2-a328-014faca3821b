import React from "react";
import { useAds, AdPlacement } from "@/contexts/AdsContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, XCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import GoogleAd from "@/components/ads/GoogleAd";

interface AdvertisementProps {
  placement: AdPlacement;
  className?: string;
}

// Map placements to slot IDs from .env
const slotMap = {
  header: import.meta.env.VITE_ADSENSE_SLOT_HEADER,
  footer: import.meta.env.VITE_ADSENSE_SLOT_FOOTER,
  content: import.meta.env.VITE_ADSENSE_SLOT_GALLERY_INFEED,
  sidebar: import.meta.env.VITE_ADSENSE_SLOT_STORY_SIDEBAR,
  "story-end": import.meta.env.VITE_ADSENSE_SLOT_STORY_END,
};

export const Advertisement: React.FC<AdvertisementProps> = ({
  placement,
  className = "",
}) => {
  const { shouldShowAd } = useAds();
  const navigate = useNavigate();
  const showAd = shouldShowAd(placement);
  const adSlotId = slotMap[placement];

  if (!showAd) return null;

  // Different ad styles based on placement
  const getAdStyle = () => {
    switch (placement) {
      case "header":
        return "w-full h-16 bg-white border border-gray-200";
      case "sidebar":
        return "w-full h-64 bg-white border border-gray-200";
      case "footer":
        return "w-full h-24 bg-white border border-gray-200";
      case "content":
        return "w-full h-36 bg-white border border-gray-200";
      default:
        return "w-full h-24 bg-white border border-gray-200";
    }
  };

  const handleGoAdFree = () => {
    navigate("/subscription?plan=ad-free");
  };

  return (
    <Card className={`${getAdStyle()} overflow-hidden shadow-sm ${className}`}>
      <CardContent className="p-2 h-full flex flex-col justify-between">
        <div className="flex justify-between items-start">
          <span className="text-xs text-gray-500 font-medium">
            Advertisement
          </span>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
            onClick={handleGoAdFree}
            aria-label="Remove ads"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-grow flex items-center justify-center">
          {adSlotId ? (
            <GoogleAd placement={placement} slot={adSlotId} />
          ) : (
            <div className="text-center text-gray-400">
              <p className="text-sm font-medium">Ad slot not configured</p>
            </div>
          )}
        </div>

        <div className="text-center mt-2">
          <Button
            variant="link"
            size="sm"
            className="text-xs text-literary-gold hover:text-literary-gold/80 p-0 font-medium"
            onClick={handleGoAdFree}
          >
            Remove ads with Premium
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
