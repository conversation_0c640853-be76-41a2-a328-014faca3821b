import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Coins, CreditCard, Zap, Star, Crown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth';

interface TokenPackage {
  id: string;
  name: string;
  tokens: number;
  price: number; // in cents
  bonus?: number;
  popular?: boolean;
  icon: React.ReactNode;
  description: string;
}

interface TokenPurchaseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance?: number;
  onPurchaseComplete?: (tokens: number) => void;
}

const TOKEN_PACKAGES: TokenPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    tokens: 100,
    price: 499, // $4.99
    icon: <Coins className="w-5 h-5" />,
    description: 'Perfect for trying out special actions',
  },
  {
    id: 'popular',
    name: 'Popular Pack',
    tokens: 250,
    price: 999, // $9.99
    bonus: 25,
    popular: true,
    icon: <Zap className="w-5 h-5" />,
    description: 'Most popular choice with bonus tokens',
  },
  {
    id: 'value',
    name: 'Value Pack',
    tokens: 500,
    price: 1899, // $18.99
    bonus: 75,
    icon: <Star className="w-5 h-5" />,
    description: 'Great value with extra bonus tokens',
  },
  {
    id: 'premium',
    name: 'Premium Pack',
    tokens: 1000,
    price: 3499, // $34.99
    bonus: 200,
    icon: <Crown className="w-5 h-5" />,
    description: 'Maximum value for power users',
  },
];

export const TokenPurchaseDialog: React.FC<TokenPurchaseDialogProps> = ({
  isOpen,
  onClose,
  currentBalance = 0,
  onPurchaseComplete,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedPackage, setSelectedPackage] = useState<TokenPackage | null>(null);
  const [customAmount, setCustomAmount] = useState('');
  const [isCustom, setIsCustom] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`;
  };

  const calculateCustomPrice = (tokens: number) => {
    // Base rate: $0.05 per token, with bulk discounts
    let rate = 0.05;
    if (tokens >= 500) rate = 0.04;
    if (tokens >= 1000) rate = 0.035;
    return Math.round(tokens * rate * 100); // Convert to cents
  };

  const handlePackageSelect = (pkg: TokenPackage) => {
    setSelectedPackage(pkg);
    setIsCustom(false);
    setCustomAmount('');
    setError('');
  };

  const handleCustomAmountChange = (value: string) => {
    const numValue = parseInt(value);
    if (value === '' || (numValue >= 10 && numValue <= 10000)) {
      setCustomAmount(value);
      setIsCustom(true);
      setSelectedPackage(null);
      setError('');
    } else if (numValue < 10) {
      setError('Minimum purchase is 10 tokens');
    } else if (numValue > 10000) {
      setError('Maximum purchase is 10,000 tokens');
    }
  };

  const handlePurchase = async () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to purchase tokens.',
        variant: 'destructive',
      });
      return;
    }

    let tokensToAdd = 0;
    let priceInCents = 0;

    if (isCustom && customAmount) {
      tokensToAdd = parseInt(customAmount);
      priceInCents = calculateCustomPrice(tokensToAdd);
    } else if (selectedPackage) {
      tokensToAdd = selectedPackage.tokens + (selectedPackage.bonus || 0);
      priceInCents = selectedPackage.price;
    } else {
      setError('Please select a package or enter a custom amount');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Create Stripe checkout session
      const response = await fetch('/api/v1/payments/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'token_purchase',
          tokens: tokensToAdd,
          amount: priceInCents,
          currency: 'usd',
          metadata: {
            userId: user.id,
            packageId: selectedPackage?.id || 'custom',
            tokenAmount: tokensToAdd,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { url } = await response.json();
      
      // Redirect to Stripe Checkout
      window.location.href = url;

    } catch (err) {
      console.error('Error creating checkout session:', err);
      setError(err instanceof Error ? err.message : 'Failed to initiate purchase');
      
      toast({
        title: 'Purchase Failed',
        description: 'Unable to process your purchase. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getTotalTokens = () => {
    if (isCustom && customAmount) {
      return parseInt(customAmount);
    }
    if (selectedPackage) {
      return selectedPackage.tokens + (selectedPackage.bonus || 0);
    }
    return 0;
  };

  const getTotalPrice = () => {
    if (isCustom && customAmount) {
      return calculateCustomPrice(parseInt(customAmount));
    }
    if (selectedPackage) {
      return selectedPackage.price;
    }
    return 0;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Coins className="w-5 h-5 text-yellow-500" />
            Purchase Tokens
          </DialogTitle>
          <DialogDescription>
            Choose a token package to unlock special story actions and features.
            Current balance: <span className="font-semibold">{currentBalance} tokens</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Token Packages */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Token Packages</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {TOKEN_PACKAGES.map((pkg) => (
                <Card
                  key={pkg.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedPackage?.id === pkg.id
                      ? 'ring-2 ring-primary border-primary'
                      : ''
                  } ${pkg.popular ? 'border-yellow-500' : ''}`}
                  onClick={() => handlePackageSelect(pkg)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {pkg.icon}
                        <CardTitle className="text-sm">{pkg.name}</CardTitle>
                      </div>
                      {pkg.popular && (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          Popular
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{pkg.tokens}</div>
                      <div className="text-sm text-muted-foreground">tokens</div>
                      {pkg.bonus && (
                        <div className="text-sm text-green-600 font-medium">
                          +{pkg.bonus} bonus
                        </div>
                      )}
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold">{formatPrice(pkg.price)}</div>
                    </div>
                    <CardDescription className="text-xs text-center">
                      {pkg.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Custom Amount */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Custom Amount</h3>
            <Card className={isCustom ? 'ring-2 ring-primary border-primary' : ''}>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="custom-tokens">Number of Tokens (10 - 10,000)</Label>
                    <Input
                      id="custom-tokens"
                      type="number"
                      min="10"
                      max="10000"
                      value={customAmount}
                      onChange={(e) => handleCustomAmountChange(e.target.value)}
                      placeholder="Enter token amount"
                      className="mt-1"
                    />
                  </div>
                  {customAmount && parseInt(customAmount) >= 10 && (
                    <div className="text-sm text-muted-foreground">
                      Price: {formatPrice(calculateCustomPrice(parseInt(customAmount)))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Purchase Summary */}
          {(selectedPackage || (isCustom && customAmount && parseInt(customAmount) >= 10)) && (
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="text-lg">Purchase Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Tokens:</span>
                  <span className="font-semibold">{getTotalTokens()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Price:</span>
                  <span className="font-semibold">{formatPrice(getTotalPrice())}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>After purchase:</span>
                  <span>{currentBalance + getTotalTokens()} tokens</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handlePurchase}
            disabled={
              loading ||
              (!selectedPackage && (!isCustom || !customAmount || parseInt(customAmount) < 10))
            }
            className="min-w-[120px]"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Purchase {formatPrice(getTotalPrice())}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 