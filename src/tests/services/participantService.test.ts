/**
 * Tests for Participant Service
 * Tests story creator controls and participant management functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
  getStoryParticipants,
  isStoryCreator,
  removeParticipant,
  updateParticipantRole,
  toggleParticipantMute,
  createStoryInvitation,
  acceptStoryInvitation,
  getStoryInvitations,
  revokeStoryInvitation,
} from '../../services/participantService';

// Mock uuid
vi.mock('uuid', () => ({
  v4: () => 'test-uuid-1234-**************-7890',
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

// Mock supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(),
    rpc: vi.fn(),
  },
}));

// Import mocked modules after mocking
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

describe('ParticipantService', () => {
  // Helper to create chainable mock
  const createMockChain = () => {
    const chain: any = {
      from: vi.fn(() => chain),
      select: vi.fn(() => chain),
      insert: vi.fn(() => chain),
      update: vi.fn(() => chain),
      delete: vi.fn(() => chain),
      eq: vi.fn(() => chain),
      gt: vi.fn(() => chain),
      order: vi.fn(() => chain),
      single: vi.fn(),
    };
    return chain;
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getStoryParticipants', () => {
    it('should fetch story participants successfully', async () => {
      const mockParticipants = [
        {
          id: '1',
          story_id: 'story-1',
          user_id: 'user-1',
          role: 'creator',
          status: 'active',
          profile: { username: 'creator_user', avatar_url: 'avatar1.jpg' },
        },
        {
          id: '2',
          story_id: 'story-1',
          user_id: 'user-2',
          role: 'contributor',
          status: 'active',
          profile: { username: 'contributor_user', avatar_url: 'avatar2.jpg' },
        },
      ];

      const mockChain = createMockChain();
      // Add order method to the chain
      mockChain.order = vi.fn(() => mockChain);
      // The actual query doesn't use .single(), it returns the array directly
      mockChain.order.mockReturnValue({
        data: mockParticipants,
        error: null,
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await getStoryParticipants('story-1');

      expect(result).toEqual(mockParticipants);
      expect(vi.mocked(supabase.from)).toHaveBeenCalledWith('story_participants');
      expect(mockChain.select).toHaveBeenCalledWith(expect.stringContaining('profile:user_id'));
      expect(mockChain.eq).toHaveBeenCalledWith('story_id', 'story-1');
      expect(mockChain.eq).toHaveBeenCalledWith('status', 'active');
      expect(mockChain.order).toHaveBeenCalledWith('joined_at', { ascending: true });
    });

    it('should return empty array on error', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Database error' },
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await getStoryParticipants('story-1');

      expect(result).toEqual([]);
    });
  });

  describe('isStoryCreator', () => {
    it('should return true when user is story creator', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await isStoryCreator('story-1', 'user-1');

      expect(result).toBe(true);
      expect(mockChain.eq).toHaveBeenCalledWith('story_id', 'story-1');
      expect(mockChain.eq).toHaveBeenCalledWith('user_id', 'user-1');
      expect(mockChain.eq).toHaveBeenCalledWith('role', 'creator');
    });

    it('should return false when user is not story creator', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await isStoryCreator('story-1', 'user-2');

      expect(result).toBe(false);
    });
  });

  describe('removeParticipant', () => {
    it('should remove participant successfully when creator', async () => {
      const mockChain = createMockChain();
      
      // First call for creator check
      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });
      
      // Second call for delete operation
      const deleteChain = createMockChain();
      deleteChain.delete.mockReturnValue(deleteChain);
      deleteChain.eq.mockReturnValue(deleteChain);
      deleteChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });
      
      vi.mocked(supabase.from)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(deleteChain);

      const result = await removeParticipant('story-1', 'user-2', 'user-1');

      expect(result.success).toBe(true);
      expect(deleteChain.delete).toHaveBeenCalled();
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Participant removed',
        description: 'The participant has been removed from the story.',
      });
    });

    it('should fail when user is not creator', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await removeParticipant('story-1', 'user-2', 'user-3');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Only the story creator can remove participants');
    });

    it('should fail when creator tries to remove themselves', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await removeParticipant('story-1', 'user-1', 'user-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Story creator cannot remove themselves');
    });
  });

  describe('updateParticipantRole', () => {
    it('should update participant role successfully', async () => {
      // Mock creator check
      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });

      // Mock update operation
      const updateChain = createMockChain();
      updateChain.update.mockReturnValue(updateChain);
      updateChain.eq.mockReturnValue(updateChain);
      updateChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(updateChain);

      const result = await updateParticipantRole('story-1', 'user-2', 'viewer', 'user-1');

      expect(result.success).toBe(true);
      expect(updateChain.update).toHaveBeenCalledWith({
        role: 'viewer',
        updated_at: expect.any(String),
      });
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Role updated',
        description: 'Participant role has been updated to viewer.',
      });
    });

    it('should fail when trying to change creator role', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await updateParticipantRole('story-1', 'user-1', 'viewer', 'user-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Cannot change creator role');
    });
  });

  describe('toggleParticipantMute', () => {
    it('should mute participant successfully', async () => {
      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });

      const updateChain = createMockChain();
      updateChain.update.mockReturnValue(updateChain);
      updateChain.eq.mockReturnValue(updateChain);
      updateChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(updateChain);

      const result = await toggleParticipantMute('story-1', 'user-2', true, 'user-1');

      expect(result.success).toBe(true);
      expect(updateChain.update).toHaveBeenCalledWith({
        status: 'muted',
        updated_at: expect.any(String),
      });
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Participant muted',
        description: 'The participant has been muted and cannot contribute until unmuted.',
      });
    });

    it('should unmute participant successfully', async () => {
      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });

      const updateChain = createMockChain();
      updateChain.update.mockReturnValue(updateChain);
      updateChain.eq.mockReturnValue(updateChain);
      updateChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(updateChain);

      const result = await toggleParticipantMute('story-1', 'user-2', false, 'user-1');

      expect(result.success).toBe(true);
      expect(updateChain.update).toHaveBeenCalledWith({
        status: 'active',
        updated_at: expect.any(String),
      });
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Participant unmuted',
        description: 'The participant can now contribute to the story again.',
      });
    });
  });

  describe('createStoryInvitation', () => {
    beforeEach(() => {
      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: { origin: 'https://example.com' },
        writable: true,
      });
    });

    it('should create invitation successfully', async () => {
      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });

      const mockInvitation = {
        id: 'invite-1',
        story_id: 'story-1',
        invite_code: 'test-uui',
        permissions: 'contributor',
      };

      const insertChain = createMockChain();
      insertChain.insert.mockReturnValue(insertChain);
      insertChain.select.mockReturnValue(insertChain);
      insertChain.single.mockResolvedValueOnce({
        data: mockInvitation,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(insertChain);

      const result = await createStoryInvitation('story-1', 'contributor', 'user-1', {
        email: '<EMAIL>',
        maxUses: 3,
        expiresInDays: 7,
      });

      expect(result.success).toBe(true);
      expect(result.data?.invitation).toEqual(mockInvitation);
      expect(result.data?.inviteLink).toBe('https://example.com/join/test-uui');
      expect(insertChain.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          story_id: 'story-1',
          inviter_user_id: 'user-1',
          invite_code: 'test-uui',
          email: '<EMAIL>',
          permissions: 'contributor',
          max_uses: 3,
        })
      );
    });
  });

  describe('acceptStoryInvitation', () => {
    it('should accept valid invitation successfully', async () => {
      const mockInvitation = {
        id: 'invite-1',
        story_id: 'story-1',
        permissions: 'contributor',
        current_uses: 0,
        max_uses: 1,
        inviter_user_id: 'user-1',
      };

      // Mock invitation lookup
      const inviteChain = createMockChain();
      inviteChain.single.mockResolvedValueOnce({
        data: mockInvitation,
        error: null,
      });

      // Mock existing participant check
      const participantCheckChain = createMockChain();
      participantCheckChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      // Mock participant insert
      const insertChain = createMockChain();
      insertChain.insert.mockReturnValue(insertChain);
      insertChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      // Mock invitation update
      const updateChain = createMockChain();
      updateChain.update.mockReturnValue(updateChain);
      updateChain.eq.mockReturnValue(updateChain);
      updateChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(inviteChain)
        .mockReturnValueOnce(participantCheckChain)
        .mockReturnValueOnce(insertChain)
        .mockReturnValueOnce(updateChain);

      const result = await acceptStoryInvitation('test-code', 'user-2');

      expect(result.success).toBe(true);
      expect(result.data?.storyId).toBe('story-1');
      expect(result.data?.role).toBe('contributor');
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Welcome to the story!',
        description: 'You have successfully joined as a contributor.',
      });
    });

    it('should fail for invalid invitation code', async () => {
      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await acceptStoryInvitation('invalid-code', 'user-2');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid or expired invitation code');
    });

    it('should fail if user is already a participant', async () => {
      const mockInvitation = {
        id: 'invite-1',
        story_id: 'story-1',
        current_uses: 0,
        max_uses: 1,
      };

      const inviteChain = createMockChain();
      inviteChain.single.mockResolvedValueOnce({
        data: mockInvitation,
        error: null,
      });

      const participantCheckChain = createMockChain();
      participantCheckChain.single.mockResolvedValueOnce({
        data: { id: 'participant-1' },
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(inviteChain)
        .mockReturnValueOnce(participantCheckChain);

      const result = await acceptStoryInvitation('test-code', 'user-2');

      expect(result.success).toBe(false);
      expect(result.error).toBe('You are already a participant in this story');
    });

    it('should fail if invitation has reached usage limit', async () => {
      const mockInvitation = {
        id: 'invite-1',
        story_id: 'story-1',
        current_uses: 1,
        max_uses: 1,
      };

      const mockChain = createMockChain();
      mockChain.single.mockResolvedValueOnce({
        data: mockInvitation,
        error: null,
      });
      vi.mocked(supabase.from).mockReturnValue(mockChain);

      const result = await acceptStoryInvitation('test-code', 'user-2');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invitation has reached its usage limit');
    });
  });

  describe('revokeStoryInvitation', () => {
    it('should revoke invitation successfully', async () => {
      const inviteChain = createMockChain();
      inviteChain.single.mockResolvedValueOnce({
        data: { story_id: 'story-1' },
        error: null,
      });

      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: { role: 'creator' },
        error: null,
      });

      const updateChain = createMockChain();
      updateChain.update.mockReturnValue(updateChain);
      updateChain.eq.mockReturnValue(updateChain);
      updateChain.single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(inviteChain)
        .mockReturnValueOnce(creatorChain)
        .mockReturnValueOnce(updateChain);

      const result = await revokeStoryInvitation('invite-1', 'user-1');

      expect(result.success).toBe(true);
      expect(updateChain.update).toHaveBeenCalledWith({
        status: 'revoked',
        updated_at: expect.any(String),
      });
      expect(vi.mocked(toast)).toHaveBeenCalledWith({
        title: 'Invitation revoked',
        description: 'The invitation has been revoked and can no longer be used.',
      });
    });

    it('should fail when user is not creator', async () => {
      const inviteChain = createMockChain();
      inviteChain.single.mockResolvedValueOnce({
        data: { story_id: 'story-1' },
        error: null,
      });

      const creatorChain = createMockChain();
      creatorChain.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Not found' },
      });

      vi.mocked(supabase.from)
        .mockReturnValueOnce(inviteChain)
        .mockReturnValueOnce(creatorChain);

      const result = await revokeStoryInvitation('invite-1', 'user-2');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Only the story creator can revoke invitations');
    });
  });
}); 