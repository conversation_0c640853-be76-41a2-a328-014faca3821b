import { useState, useCallback } from "react";
import { useAuth } from "@/contexts/auth";
import { AI_CREDIT_COSTS } from "@/services/aiServices";
import { useToast } from "@/hooks/use-toast";

// Define credit costs for subscription features
export const CREDIT_COSTS = {
  titleGeneration: AI_CREDIT_COSTS.titleGeneration,
  descriptionGeneration: AI_CREDIT_COSTS.descriptionGeneration,
  wordSuggestion: AI_CREDIT_COSTS.wordSuggestion,
  sentenceGeneration: AI_CREDIT_COSTS.sentenceGeneration,
  paragraphGeneration: AI_CREDIT_COSTS.paragraphGeneration,
  nudge: AI_CREDIT_COSTS.nudge,
  coverArtBasic: AI_CREDIT_COSTS.coverArt.basic,
  coverArtPremium: AI_CREDIT_COSTS.coverArt.premium,
};

// Define subscription tiers and their features
export const SUBSCRIPTION_TIERS = {
  free: {
    name: "Free",
    maxActiveStories: 3,
    maxCollaborators: 3,
    price: 0,
  },
  microtier: {
    name: "Writer",
    maxActiveStories: 5,
    maxCollaborators: 5,
    price: 499, // $4.99
  },
  wordsmith: {
    name: "Words<PERSON>",
    maxActiveStories: 10,
    maxCollaborators: 5,
    price: 999, // $9.99
  },
  storyteller: {
    name: "Storyteller",
    maxActiveStories: 20,
    maxCollaborators: 10,
    price: 1999, // $19.99
  },
  "authors-guild": {
    name: "Authors Guild",
    maxActiveStories: 50, // Changed from Infinity to 50
    maxCollaborators: Infinity,
    price: 2999, // $29.99
  },
};

export type CreditAction =
  | "titleGeneration"
  | "descriptionGeneration"
  | "wordSuggestion"
  | "sentenceGeneration"
  | "paragraphGeneration"
  | "nudge"
  | "coverArtBasic"
  | "coverArtPremium";

export const useSubscription = () => {
  const { user, updateProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Get credits from user or local storage as fallback
  const credits = user?.credits || 0;

  // Get the user's subscription tier
  const userTier = user?.tier || "free";

  // Get the max active stories and collaborators for the user's tier
  const getMaxActiveStories = useCallback(() => {
    return (
      SUBSCRIPTION_TIERS[userTier as keyof typeof SUBSCRIPTION_TIERS]
        ?.maxActiveStories || SUBSCRIPTION_TIERS.free.maxActiveStories
    );
  }, [userTier]);

  const getMaxCollaborators = useCallback(() => {
    return (
      SUBSCRIPTION_TIERS[userTier as keyof typeof SUBSCRIPTION_TIERS]
        ?.maxCollaborators || SUBSCRIPTION_TIERS.free.maxCollaborators
    );
  }, [userTier]);

  const useCredits = useCallback(
    async (action: CreditAction, quantity = 1): Promise<boolean> => {
      if (!user) return false;

      const cost = CREDIT_COSTS[action] * quantity;

      if ((user.credits || 0) < cost) {
        return false;
      }

      setLoading(true);
      try {
        // Update user credits
        const newCredits = (user.credits || 0) - cost;

        // Update user profile with new credits
        if (updateProfile) {
          await updateProfile({ credits: newCredits });
        }

        return true;
      } catch (error) {
        console.error("Error using credits:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [user, updateProfile],
  );

  const purchaseCredits = useCallback(
    async (amount: number): Promise<boolean> => {
      if (!user) return false;

      setLoading(true);
      try {
        // Simulate purchase API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Update user credits
        const newCredits = (user.credits || 0) + amount;

        // Update user profile with new credits
        if (updateProfile) {
          await updateProfile({ credits: newCredits });
        }

        return true;
      } catch (error) {
        console.error("Error purchasing credits:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [user, updateProfile],
  );

  // Add the functions needed for the Subscription component
  const handleBuyCredits = useCallback(
    async (type: string, amount: number, cents: number) => {
      setLoading(true);
      try {
        const success = await purchaseCredits(amount);
        if (success) {
          toast({
            title: "Credits Purchased",
            description: `You have successfully purchased ${amount} ${type} credits.`,
          });
          return true;
        } else {
          toast({
            title: "Purchase Failed",
            description: "Failed to purchase credits. Please try again.",
            variant: "destructive",
          });
          return false;
        }
      } catch (error) {
        console.error("Credit purchase error:", error);
        toast({
          title: "Purchase Error",
          description: "An error occurred during purchase.",
          variant: "destructive",
        });
        return false;
      } finally {
        setLoading(false);
      }
    },
    [purchaseCredits, toast],
  );

  const checkCredits = useCallback(() => {
    // Simply returns the current credits, can be extended to fetch from API
    return credits;
  }, [credits]);

  const isLoading = loading;

  return {
    credits,
    loading,
    isLoading,
    userTier,
    useCredits,
    purchaseCredits,
    handleBuyCredits,
    checkCredits,
    getMaxActiveStories,
    getMaxCollaborators,
    SUBSCRIPTION_TIERS,
    CREDIT_COSTS,
  };
};
