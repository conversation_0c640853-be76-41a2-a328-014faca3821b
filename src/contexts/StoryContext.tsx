import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { Story, Contribution, Author } from '../types/story';
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/hooks/use-toast";
import { supabase } from '../lib/supabase';
import { PostgrestError, FunctionsError, RealtimeChannel } from '@supabase/supabase-js';
import { debounce } from 'lodash';

export enum StoryContributionMode {
  WORD = "word",
  MULTI_WORD = "words",
  SENTENCE = "sentence",
  PARAGRAPH = "paragraph"
}

export type SupabaseApiError = PostgrestError | FunctionsError | { message: string; code: string };

interface StoryContextType {
  currentStory: Story | null;
  contributions: Contribution[];
  loading: boolean;
  loadStory: (storyId: string) => Promise<{ data: Story | null; error: SupabaseApiError | null }>;
  addContributionToStory: (content: string) => Promise<{ data: Contribution | null; error: SupabaseApiError | null }>;
  createStory: (storyData: Partial<Story & { contributionModeInternal?: StoryContributionMode, wordsPerContributionInternal?: number }>) => Promise<{ data: Story | null; error: SupabaseApiError | null }>;
  addWord: (storyId: string, wordContent: string) => Promise<{ data: Contribution | null; error: SupabaseApiError | null }>;
  completeStory: (storyId: string) => Promise<{ data: Story | null; error: SupabaseApiError | null }>;
  typingUsers: { user_id: string, username?: string, last_activity: string }[];
  handleUserTyping: (isTyping: boolean) => void;
}

const StoryContext = createContext<StoryContextType | null>(null);

interface StoryProviderProps {
  children: ReactNode;
}

export const StoryProvider = ({ children }: StoryProviderProps) => {
  const [currentStory, setCurrentStory] = useState<Story | null>(null);
  const [contributions, setContributions] = useState<Contribution[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const [contributionsChannel, setContributionsChannel] = useState<RealtimeChannel | null>(null);
  const [presenceChannel, setPresenceChannel] = useState<RealtimeChannel | null>(null);
  const [typingUsersByStory, setTypingUsersByStory] = useState<Record<string, { user_id: string, username?: string, last_activity: string }[]>>({});

  const handleError = (error: SupabaseApiError | Error | unknown) => {
    console.error("StoryContext Error:", error);
    let message = "An unexpected error occurred.";
    if (typeof error === 'object' && error !== null && 'message' in error) {
        message = (error as { message: string }).message;
    }
    toast({
        title: "Error",
        description: message,
        variant: "destructive",
    });
  };

  const loadStory = async (storyId: string): Promise<{ data: Story | null; error: SupabaseApiError | null }> => {
    setLoading(true);
    const result: { data: Story | null; error: SupabaseApiError | null } = { data: null, error: null };
    try {
      const { data: storyData, error: storyError } = await supabase
        .from('stories')
        .select(`
          *,
          author:author_id(id, username, avatar_url),
          contributions(*, author:user_id(id, username, avatar_url))
        `)
        .eq('id', storyId)
        .single();

      if (storyError) {
        handleError(storyError);
        result.error = storyError;
        setCurrentStory(null);
        setContributions([]);
      } else if (storyData) {
        const typedStoryData = storyData as unknown as Story;
        setCurrentStory(typedStoryData);
        setContributions(typedStoryData.contributions || []);
        result.data = typedStoryData;
      } else {
        setCurrentStory(null);
        setContributions([]);
        result.error = { message: 'Story not found', code: '404' };
        handleError(result.error);
      }
    } catch (error) {
      const err = error as Error;
      handleError(err);
      result.error = { message: err.message || 'Failed to load story', code: 'CLIENT_ERROR' };
    }
    setLoading(false);
    return result;
  };

  const addContributionToStory = async (content: string): Promise<{ data: Contribution | null; error: SupabaseApiError | null }> => {
    if (!currentStory) {
      const error = { message: 'No current story selected', code: 'NO_STORY' };
      handleError(error);
      return { data: null, error };
    }
    if (!user) {
        const error = { message: 'User not authenticated', code: 'AUTH_REQUIRED' };
        handleError(error);
        return { data: null, error };
    }

    setLoading(true);
    const result: { data: Contribution | null; error: SupabaseApiError | null } = { data: null, error: null };
    try {
      const { data: newContribution, error: functionError } = await supabase.functions.invoke('create-contribution', {
        body: {
          story_id: currentStory.id,
          content: content,
        }
      });

      if (functionError) {
        handleError(functionError);
        result.error = functionError;
      } else if (newContribution) {
        result.data = newContribution as Contribution;
        await loadStory(currentStory.id);
      } else {
        await loadStory(currentStory.id);
      }
    } catch (error) {
      const err = error as Error;
      handleError(err);
      result.error = { message: err.message || 'Failed to add contribution', code: 'CLIENT_ERROR' };
    }
    setLoading(false);
    return result;
  };

  const createStory = async (storyData: Partial<Story & { contributionModeInternal?: StoryContributionMode, wordsPerContributionInternal?: number }>): Promise<{ data: Story | null; error: SupabaseApiError | null }> => {
    if (!user) {
      const authError = { message: "User not authenticated to create a story", code: "AUTH_REQUIRED" };
      handleError(authError);
      return { data: null, error: authError };
    }
    setLoading(true);
    const result: { data: Story | null; error: SupabaseApiError | null } = { data: null, error: null };

    const payload = {
      title: storyData.title,
      description: storyData.description,
      is_public: storyData.is_public,
      max_contributors: storyData.max_contributors,
      max_words_per_contribution: storyData.max_words_per_contribution,
      genre: storyData.genre,
      status: storyData.status || 'in_progress',
    };

    try {
      const { data: newStoryData, error: functionError } = await supabase.functions.invoke('create-story', {
        body: payload
      });

      if (functionError) {
        handleError(functionError);
        result.error = functionError;
      } else {
        result.data = newStoryData as Story;
      }

    } catch (error) {
      const err = error as Error;
      handleError(err);
      result.error = { message: err.message || 'Failed to create story', code: 'CLIENT_ERROR' };
    }
    setLoading(false);
    return result;
  };

  const addWord = async (storyId: string, wordContent: string): Promise<{ data: Contribution | null; error: SupabaseApiError | null }> => {
    if (!storyId || !wordContent) {
      const argError = { message: "Story ID and word content are required for addWord", code: "ARGS_MISSING" };
      handleError(argError);
      return { data: null, error: argError };
    }
    console.warn("addWord function in StoryContext is a placeholder and needs proper implementation.");
    const { data, error } = await supabase.functions.invoke('create-contribution', {
         body: { story_id: storyId, content: wordContent, mode: 'word' }
    });
    if (error) handleError(error);
    return { data: data as Contribution, error };
  };

  const completeStory = async (storyId: string): Promise<{ data: Story | null; error: SupabaseApiError | null }> => {
    if (!user) {
      const authError = { message: "User not authenticated to complete a story", code: "AUTH_REQUIRED" };
      handleError(authError);
      return { data: null, error: authError };
    }
    if (!currentStory || currentStory.id !== storyId) {
      const contextError = { message: "Story context mismatch or story not loaded.", code: "CONTEXT_ERROR" };
      handleError(contextError);
      return { data: null, error: contextError };
    }
    if (currentStory.author.id !== user.id) {
      const permError = { message: "Only the story author can mark it as complete.", code: "PERMISSION_DENIED" };
      handleError(permError);
      return { data: null, error: permError };
    }
    if (currentStory.status === 'completed') {
      const alreadyDoneError = { message: "This story is already marked as complete.", code: "ALREADY_COMPLETED" };
      return { data: currentStory, error: alreadyDoneError };
    }

    setLoading(true);
    const result: { data: Story | null; error: SupabaseApiError | null } = { data: null, error: null };
    try {
      const { data: updatedStoryData, error: functionError } = await supabase.functions.invoke('complete-story', {
        body: { story_id: storyId }
      });

      if (functionError) {
        handleError(functionError);
        result.error = functionError;
      } else if (updatedStoryData) {
        result.data = updatedStoryData as Story;
        setCurrentStory(result.data);
        setContributions(result.data.contributions || []);
        toast({ title: "Story Completed!", description: `"${result.data.title}" has been marked as complete.` });
      } else {
        await loadStory(storyId);
        toast({ title: "Story Updated", description: "Story status has been updated." });
      }
    } catch (error) {
      const err = error as Error;
      handleError(err);
      result.error = { message: err.message || 'Failed to complete story', code: 'CLIENT_ERROR' };
    }
    setLoading(false);
    return result;
  };

  useEffect(() => {
    // Clean up existing subscription when component unmounts or currentStory changes
    if (contributionsChannel) {
      supabase.removeChannel(contributionsChannel);
      setContributionsChannel(null);
    }

    if (currentStory?.id && user) { // Ensure user is loaded to prevent RLS issues if policies depend on user
      const channel = supabase
        .channel(`story-${currentStory.id}-contributions`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'contributions',
            filter: `story_id=eq.${currentStory.id}`,
          },
          (payload) => {
            console.log('New contribution received via real-time:', payload);
            const newContribution = payload.new as Contribution;
            // Important: Ensure the newContribution has the author object embedded
            // If the payload.new from Supabase doesn't include the author, 
            // we might need to fetch it or adjust the story loading/contribution adding logic 
            // to always return the full author details.
            // For now, assuming payload.new is compatible or Story type handles potential partial author.
            
            // To prevent duplicates if addContributionToStory also updates state optimistically
            // or if multiple subscriptions fire, check if contribution already exists.
            setContributions((prevContributions) => {
              if (!prevContributions.find(c => c.id === newContribution.id)) {
                // Sort contributions by order after adding the new one
                return [...prevContributions, newContribution].sort((a, b) => a.order - b.order);
              } 
              return prevContributions;
            });
            
            // Optionally, show a toast or notification
            // toast({ title: "New Contribution!", description: "The story has been updated." });
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to contributions for story ${currentStory.id}`);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`Subscription error for story ${currentStory.id}:`, err);
            toast({ title: "Real-time Error", description: `Could not connect to real-time updates for this story. ${err?.message || ''}`, variant: "destructive" });
          }
        });
      
      setContributionsChannel(channel);
    }

    // Cleanup function for when the effect dependencies change or component unmounts
    return () => {
      if (contributionsChannel) {
        supabase.removeChannel(contributionsChannel);
      }
    };
  }, [currentStory?.id, user, toast, contributionsChannel]);

  // Real-time presence subscription
  useEffect(() => {
    if (presenceChannel) {
      supabase.removeChannel(presenceChannel);
      setPresenceChannel(null);
    }

    if (currentStory?.id && user) {
      const channel = supabase
        .channel(`story-${currentStory.id}-presence`)
        .on(
          'postgres_changes',
          {
            event: '*', // Listen to INSERT, UPDATE, DELETE
            schema: 'public',
            table: 'presence',
            filter: `story_id=eq.${currentStory.id}`,
          },
          (payload) => {
            console.log('Presence update received:', payload);
            const record = (payload.new || payload.old) as { story_id: string; user_id: string; is_typing: boolean; username?: string; last_activity: string };
            
            if (!record || record.user_id === user.id) { // Don't process updates for the current user's own typing status here
              return;
            }

            setTypingUsersByStory(prev => {
              const storyTypingUsers = [...(prev[record.story_id] || [])];
              const existingUserIndex = storyTypingUsers.findIndex(u => u.user_id === record.user_id);

              if (payload.eventType === 'DELETE' || !record.is_typing) {
                if (existingUserIndex !== -1) {
                  storyTypingUsers.splice(existingUserIndex, 1);
                }
              } else { // INSERT or UPDATE with is_typing = true
                const userEntry = { user_id: record.user_id, username: record.username, last_activity: record.last_activity };
                if (existingUserIndex !== -1) {
                  storyTypingUsers[existingUserIndex] = userEntry;
                } else {
                  storyTypingUsers.push(userEntry);
                }
              }
              return { ...prev, [record.story_id]: storyTypingUsers };
            });
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to presence for story ${currentStory.id}`);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`Presence subscription error for story ${currentStory.id}:`, err);
            toast({ title: "Real-time Presence Error", description: `Could not connect to presence updates for this story. ${err?.message || ''}`, variant: "destructive" });
          }
        });
      setPresenceChannel(channel);
    }

    return () => {
      if (presenceChannel) {
        supabase.removeChannel(presenceChannel);
      }
    };
  }, [currentStory?.id, user, toast, presenceChannel]);

  // Cleanup stale typing users
  useEffect(() => {
    const interval = setInterval(() => {
      if (!currentStory?.id) return;
      setTypingUsersByStory(prev => {
        const storyTypingUsers = prev[currentStory.id] || [];
        const now = new Date().getTime();
        const activeThreshold = 10000; // 10 seconds
        const updatedStoryTypingUsers = storyTypingUsers.filter(u => {
          return now - new Date(u.last_activity).getTime() < activeThreshold;
        });

        if (updatedStoryTypingUsers.length !== storyTypingUsers.length) {
          return { ...prev, [currentStory.id]: updatedStoryTypingUsers };
        }
        return prev;
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [currentStory?.id]);

  const debouncedUpdatePresence = debounce(async (isTyping: boolean) => {
    if (!currentStory || !user) return;

    // Access user metadata using type assertion as a workaround for strict typing
    const metaUser = user as any;
    const displayName = metaUser.user_metadata?.username || 
                        metaUser.user_metadata?.user_name || 
                        metaUser.raw_user_meta_data?.username || 
                        metaUser.raw_user_meta_data?.user_name || 
                        user.email;

    const { error } = await supabase.from('presence').upsert({
      story_id: currentStory.id,
      user_id: user.id,
      username: displayName,
      is_typing: isTyping,
      last_activity: new Date().toISOString(),
    }, { onConflict: 'story_id,user_id' });

    if (error) {
      console.error('Error updating presence:', error);
      toast({ title: "Presence Error", description: "Could not update typing status.", variant: "destructive" });
    } else {
      // If isTyping is false, also immediately update local state for current user to provide responsive UI
      // The broadcast will eventually update others, but this makes local UI faster.
      // However, we decided above to not process current user's updates from the channel, so this is not strictly needed.
      // The main purpose of this function is to inform OTHERS.
    }
  }, 500); // Debounce time: 500ms

  const handleUserTyping = (isTyping: boolean) => {
    debouncedUpdatePresence(isTyping);
  };
  
  const currentStoryTypingUsers = currentStory ? typingUsersByStory[currentStory.id] || [] : [];

  const value: StoryContextType = {
    currentStory,
    contributions,
    loading,
    loadStory,
    addContributionToStory,
    createStory,
    addWord,
    completeStory,
    typingUsers: currentStoryTypingUsers,
    handleUserTyping,
  };

  return (
    <StoryContext.Provider value={value}>
      {children}
    </StoryContext.Provider>
  );
};

export const useStory = () => {
  const context = useContext(StoryContext);
  if (context === null) {
    throw new Error('useStory must be used within a StoryProvider');
  }
  return context;
};
