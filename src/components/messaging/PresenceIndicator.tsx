import React from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export interface PresenceUser {
  id: string;
  username: string;
  avatar_url?: string;
  isOnline: boolean;
  isTyping?: boolean;
  lastSeen?: Date;
  isActive?: boolean;
  role?: 'creator' | 'contributor' | 'viewer';
}

export interface PresenceIndicatorProps {
  users: PresenceUser[];
  maxDisplay?: number;
  showDetails?: boolean;
  variant?: 'compact' | 'full';
  className?: string;
}

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({
  users,
  maxDisplay = 5,
  showDetails = true,
  variant = 'compact',
  className,
}) => {
  const onlineUsers = users.filter(user => user.isOnline);
  const typingUsers = users.filter(user => user.isTyping);
  const displayUsers = onlineUsers.slice(0, maxDisplay);
  const remainingCount = Math.max(0, onlineUsers.length - maxDisplay);

  const getPresenceText = (user: PresenceUser) => {
    if (user.isTyping) return 'Typing...';
    if (user.isOnline && user.isActive) return 'Active';
    if (user.isOnline) return 'Online';
    if (user.lastSeen) {
      return `Last seen ${formatDistanceToNow(user.lastSeen, { addSuffix: true })}`;
    }
    return 'Offline';
  };

  const getStatusColor = (user: PresenceUser) => {
    if (user.isTyping) return 'bg-blue-500 animate-pulse';
    if (user.isOnline && user.isActive) return 'bg-green-500';
    if (user.isOnline) return 'bg-yellow-500';
    return 'bg-gray-400 dark:bg-gray-600';
  };

  if (variant === 'compact') {
    return (
      <TooltipProvider>
        <div className={cn('flex items-center gap-2', className)}>
          {/* User Avatars Stack */}
          <div className="flex -space-x-1">
            {displayUsers.map((user, index) => (
              <Tooltip key={user.id}>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <Avatar 
                      className={cn(
                        'w-6 h-6 border-2 border-background shadow-sm transition-transform hover:scale-110 cursor-pointer',
                        user.isTyping && 'animate-pulse ring-2 ring-blue-500/50',
                        `z-[${displayUsers.length - index}]`
                      )}
                    >
                      <AvatarImage src={user.avatar_url} alt={user.username} />
                      <AvatarFallback className="text-xs bg-muted">
                        {user.username.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    {/* Status Indicator */}
                    <div
                      className={cn(
                        'absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full border border-background',
                        getStatusColor(user)
                      )}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <div className="text-center">
                    <p className="font-semibold">{user.username}</p>
                    {user.role && (
                      <Badge variant="outline" className="text-xs mb-1">
                        {user.role}
                      </Badge>
                    )}
                    <p className="text-xs text-muted-foreground">
                      {getPresenceText(user)}
                    </p>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
            
            {remainingCount > 0 && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="w-6 h-6 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-semibold text-muted-foreground shadow-sm">
                    +{remainingCount}
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{remainingCount} more online</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>

          {/* Status Text */}
          {showDetails && (
            <div className="text-xs text-muted-foreground">
              {onlineUsers.length > 0 ? (
                <span>
                  {onlineUsers.length} online
                  {typingUsers.length > 0 && ` • ${typingUsers.length} typing`}
                </span>
              ) : (
                <span>No one online</span>
              )}
            </div>
          )}
        </div>
      </TooltipProvider>
    );
  }

  // Full variant with detailed list
  return (
    <div className={cn('space-y-2', className)}>
      <div className="text-sm font-medium text-muted-foreground">
        {onlineUsers.length > 0 ? `${onlineUsers.length} Online` : 'No one online'}
      </div>
      
      {onlineUsers.length > 0 && (
        <div className="space-y-1">
          {displayUsers.map((user) => (
            <div key={user.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
              <div className="relative">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user.avatar_url} alt={user.username} />
                  <AvatarFallback className="text-xs">
                    {user.username.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div
                  className={cn(
                    'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background',
                    getStatusColor(user)
                  )}
                />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium truncate">{user.username}</p>
                  {user.role && (
                    <Badge variant="outline" className="text-xs">
                      {user.role}
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {getPresenceText(user)}
                </p>
              </div>
            </div>
          ))}
          
          {remainingCount > 0 && (
            <div className="text-xs text-muted-foreground text-center py-1">
              and {remainingCount} more...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PresenceIndicator; 