import { 
  getBrowserInfo, 
  handleBrowserQuirks, 
  checkFeatureSupport,
  checkCssSupport,
  measurePageLoad,
  testOnAllMobileDevices,
  testOnAllTabletDevices,
  testOnAllDesktopSizes
} from '../../support/browsers/browser-utils'

describe('Cross-Browser Compatibility', () => {
  beforeEach(() => {
    handleBrowserQuirks()
  })

  context('Browser Feature Detection', () => {
    it('should detect browser capabilities', () => {
      cy.visitAndWait('/')
      
      const browser = getBrowserInfo()
      cy.log(`Testing on: ${browser.displayName} ${browser.version}`)
      
      checkFeatureSupport().then((features) => {
        // Essential features that should be supported
        expect(features.localStorage).to.be.true
        expect(features.sessionStorage).to.be.true
        
        // Log optional features
        cy.log('Service Worker support:', features.serviceWorker)
        cy.log('Touch Events support:', features.touchEvents)
        cy.log('WebGL support:', features.webGL)
      })
    })

    it('should support required CSS features', () => {
      cy.visitAndWait('/')
      
      // Test CSS Grid support
      checkCssSupport('display', 'grid')
      
      // Test Flexbox support
      checkCssSupport('display', 'flex')
      
      // Test CSS Custom Properties
      checkCssSupport('--test-var', 'test-value')
      
      // Test modern CSS features
      checkCssSupport('gap', '1rem')
      checkCssSupport('aspect-ratio', '16/9')
    })
  })

  context('Page Load Performance', () => {
    it('should load main pages within performance thresholds', () => {
      const pages = ['/', '/gallery', '/login', '/pricing']
      
      pages.forEach(page => {
        measurePageLoad(page)
      })
    })

    it('should handle large content loads efficiently', () => {
      cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
      
      measurePageLoad('/gallery')
      cy.wait('@getStories')
      
      // Check that large lists don't impact performance
      cy.get('[data-testid=\"story-card\"]').should('be.visible')
    })
  })

  context('Authentication Across Browsers', () => {
    beforeEach(() => {
      cy.mockSupabaseAuth()
    })

    it('should handle login flow correctly', () => {
      cy.visitAndWait('/login')
      
      cy.get('[data-testid=\"email-input\"]').type('<EMAIL>')
      cy.get('[data-testid=\"password-input\"]').type('password123')
      cy.get('[data-testid=\"login-button\"]').click()
      
      cy.waitForLoadingToFinish()
      cy.url().should('include', '/dashboard')
    })

    it('should persist session correctly', () => {
      cy.visitAndWait('/dashboard')
      
      // Refresh page
      cy.reload()
      
      // Should remain logged in
      cy.url().should('include', '/dashboard')
      cy.get('[data-testid=\"user-menu\"]').should('be.visible')
    })

    it('should handle logout across browsers', () => {
      cy.visitAndWait('/dashboard')
      
      cy.get('[data-testid=\"user-menu\"]').click()
      cy.get('[data-testid=\"logout-button\"]').click()
      
      cy.url().should('eq', Cypress.config().baseUrl + '/')
    })
  })

  context('Form Interactions', () => {
    beforeEach(() => {
      cy.mockSupabaseAuth()
    })

    it('should handle form submissions correctly', () => {
      cy.visitAndWait('/create-story')
      
      cy.get('[data-testid=\"story-title-input\"]').type('Cross-Browser Test Story')
      cy.get('[data-testid=\"story-description-input\"]').type('Testing form submission across browsers')
      
      cy.get('[data-testid=\"create-story-button\"]').click()
      cy.waitForLoadingToFinish()
      
      // Should redirect to story page
      cy.url().should('include', '/story/')
    })

    it('should validate forms consistently', () => {
      cy.visitAndWait('/create-story')
      
      // Submit empty form
      cy.get('[data-testid=\"create-story-button\"]').click()
      
      // Should show validation errors
      cy.contains('Title is required').should('be.visible')
      cy.contains('Description is required').should('be.visible')
    })

    it('should handle special characters in forms', () => {
      cy.visitAndWait('/create-story')
      
      const specialTitle = 'Story with émojis 🎭 and spëcial châractërs'
      const specialDescription = 'Description with various symbols: @#$%^&*()_+-=[]{}|;:,.<>?'
      
      cy.get('[data-testid=\"story-title-input\"]').type(specialTitle)
      cy.get('[data-testid=\"story-description-input\"]').type(specialDescription)
      
      // Should handle special characters
      cy.get('[data-testid=\"story-title-input\"]').should('have.value', specialTitle)
      cy.get('[data-testid=\"story-description-input\"]').should('have.value', specialDescription)
    })
  })

  context('JavaScript Functionality', () => {
    it('should execute JavaScript correctly', () => {
      cy.visitAndWait('/')
      
      // Test modern JavaScript features
      cy.window().then((win) => {
        // Test arrow functions
        const testArrow = () => 'arrow-function-works'
        expect(testArrow()).to.equal('arrow-function-works')
        
        // Test template literals
        const testTemplate = `template-literal-works`
        expect(testTemplate).to.equal('template-literal-works')
        
        // Test async/await support
        const testAsync = async () => 'async-works'
        testAsync().then(result => {
          expect(result).to.equal('async-works')
        })
        
        // Test destructuring
        const { location } = win
        expect(location).to.exist
        
        // Test spread operator
        const testArray = [1, 2, 3]
        const spreadArray = [...testArray, 4]
        expect(spreadArray).to.deep.equal([1, 2, 3, 4])
      })
    })

    it('should handle event listeners correctly', () => {
      cy.visitAndWait('/gallery')
      
      // Test click events
      cy.get('[data-testid=\"search-input\"]').click().should('be.focused')
      
      // Test keyboard events
      cy.get('[data-testid=\"search-input\"]').type('test{enter}')
      
      // Test focus events
      cy.get('[data-testid=\"search-input\"]').blur().should('not.be.focused')
    })
  })

  context('Local Storage and Session Storage', () => {
    it('should handle localStorage correctly', () => {
      cy.visitAndWait('/')
      
      cy.window().then((win) => {
        // Test localStorage
        win.localStorage.setItem('test-key', 'test-value')
        expect(win.localStorage.getItem('test-key')).to.equal('test-value')
        
        // Test localStorage with JSON
        const testObject = { key: 'value', number: 123 }
        win.localStorage.setItem('test-object', JSON.stringify(testObject))
        const retrieved = JSON.parse(win.localStorage.getItem('test-object') || '{}')
        expect(retrieved).to.deep.equal(testObject)
        
        // Cleanup
        win.localStorage.removeItem('test-key')
        win.localStorage.removeItem('test-object')
      })
    })

    it('should handle sessionStorage correctly', () => {
      cy.visitAndWait('/')
      
      cy.window().then((win) => {
        // Test sessionStorage
        win.sessionStorage.setItem('session-test', 'session-value')
        expect(win.sessionStorage.getItem('session-test')).to.equal('session-value')
        
        // Cleanup
        win.sessionStorage.removeItem('session-test')
      })
    })
  })

  context('Error Handling', () => {
    it('should handle network errors gracefully', () => {
      // Mock network error
      cy.intercept('GET', '**/stories**', { forceNetworkError: true }).as('networkError')
      
      cy.visitAndWait('/gallery')
      cy.wait('@networkError')
      
      // Should show error message
      cy.contains('Error loading').should('be.visible')
      cy.get('[data-testid=\"retry-button\"]').should('be.visible')
    })

    it('should handle JavaScript errors gracefully', () => {
      cy.visitAndWait('/')
      
      cy.window().then((win) => {
        // Inject an error to test error handling
        win.addEventListener('error', (e) => {
          cy.log('Caught error:', e.message)
        })
        
        // Test that the page still functions after an error
        cy.get('[data-testid=\"nav-gallery\"]').should('be.visible').click()
        cy.url().should('include', '/gallery')
      })
    })
  })
})

// Browser-specific tests
describe('Browser-Specific Features', () => {
  context('Chrome/Chromium-specific tests', () => {
    it('should handle Chrome-specific features', () => {
      if (Cypress.browser.family === 'chromium') {
        cy.visitAndWait('/')
        
        // Test Chrome-specific APIs if used
        cy.window().then((win) => {
          if ('chrome' in win) {
            cy.log('Chrome-specific APIs available')
          }
        })
      }
    })
  })

  context('Firefox-specific tests', () => {
    it('should handle Firefox-specific behaviors', () => {
      if (Cypress.browser.name === 'firefox') {
        cy.visitAndWait('/')
        
        // Test Firefox-specific handling
        cy.get('body').should('have.css', 'scroll-behavior', 'auto')
      }
    })
  })

  context('Safari/WebKit-specific tests', () => {
    it('should handle Safari-specific behaviors', () => {
      if (Cypress.browser.name === 'webkit') {
        cy.visitAndWait('/')
        
        // Test WebKit-specific handling
        cy.get('body').should('have.css', '-webkit-overflow-scrolling', 'touch')
      }
    })
  })
})