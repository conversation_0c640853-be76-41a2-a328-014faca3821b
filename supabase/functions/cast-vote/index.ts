import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY for cast-vote function.');
}

interface VoteInput {
  vote_type: 'upvote' | 'downvote';
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for cast-vote:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use POST.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Optional: Check if story exists (RLS on votes table might implicitly handle this if FK constraint fails)
    const { data: storyData, error: storyError } = await supabase
        .from('stories')
        .select('id')
        .eq('id', storyId)
        .maybeSingle(); // Use maybeSingle to not error if story not found by this check

    if (storyError) { // An actual error during story check
        console.error("Error checking story existence:", storyError);
        // Potentially return 500, or proceed and let vote RLS/FK constraint handle it
    }
    if (!storyError && !storyData) { // Story explicitly not found
        return new Response(JSON.stringify({ error: 'Story not found.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404,
        });
    }


    const body: VoteInput = await req.json();

    if (!body.vote_type || (body.vote_type !== 'upvote' && body.vote_type !== 'downvote')) {
      return new Response(JSON.stringify({ error: 'vote_type is required and must be "upvote" or "downvote".' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const voteData = {
      story_id: storyId,
      user_id: user.id,
      vote_type: body.vote_type,
    };

    // Upsert the vote
    // conflict_target is deprecated, use onConflict instead if Supabase JS v2.
    // However, Supabase JS client v2 `.upsert()` has `onConflict` in options.
    // The RLS policy "Allow user to update their own vote" should have `USING (auth.uid() = user_id)`
    // and `WITH CHECK (auth.uid() = user_id)`.
    // The "Allow authenticated users to insert votes" should have `WITH CHECK (auth.uid() = user_id)`.
    const { data, error, status } = await supabase
      .from('votes')
      .upsert(voteData, { onConflict: 'story_id,user_id' }) // Specify unique constraint columns for conflict
      .select()
      .single();


    if (error) {
      console.error('Supabase upsert vote error:', error);
      // RLS denial could result in a specific error code or a generic one.
      // If RLS on 'votes' denies insert/update, it should error.
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: (error as any).status || 500, // Use Supabase error status if available
      });
    }
    
    // Supabase .upsert().select().single() typically returns the inserted/updated row.
    // HTTP status for upsert: 201 if created, 200 if updated.
    // The `status` from the response object isn't the HTTP status directly here,
    // but rather PostgREST's status code for the operation.
    // We need to determine if it was an insert or update if we want to return 200 vs 201.
    // A common way is to check if created_at is very recent or if specific fields changed.
    // For simplicity, returning 200 for successful upsert is acceptable, or always 201 if we don't differentiate.
    // Let's assume 200 is fine as the resource state is now `data`.

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200, // OK (since it's an upsert, could be create or update)
    });

  } catch (err) {
    console.error('Error processing cast-vote request:', err);
    const statusCode = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
}); 