-- Migration: Add user-to-user token transfer system
-- File: supabase/migrations/0013_add_token_transfers.sql

-- Create token_transfers table to track peer-to-peer transfers
CREATE TABLE IF NOT EXISTS public.token_transfers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL CHECK (amount > 0),
    sender_balance_before INTEGER NOT NULL,
    sender_balance_after INTEGER NOT NULL,
    recipient_balance_before INTEGER NOT NULL,
    recipient_balance_after INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    transfer_message TEXT,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    completed_at TIMESTAMPTZ,
    
    -- Prevent self-transfers
    CONSTRAINT no_self_transfer CHECK (sender_id != recipient_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_token_transfers_sender ON public.token_transfers(sender_id);
CREATE INDEX IF NOT EXISTS idx_token_transfers_recipient ON public.token_transfers(recipient_id);
CREATE INDEX IF NOT EXISTS idx_token_transfers_status ON public.token_transfers(status);
CREATE INDEX IF NOT EXISTS idx_token_transfers_created_at ON public.token_transfers(created_at DESC);

-- Add new transaction type for transfers
ALTER TABLE public.token_transactions 
DROP CONSTRAINT IF EXISTS token_transactions_transaction_type_check,
ADD CONSTRAINT token_transactions_transaction_type_check 
CHECK (transaction_type IN ('earned', 'spent', 'refund', 'admin_adjustment', 'transfer_sent', 'transfer_received'));

-- Add transfer_id column to link transaction records to transfers
ALTER TABLE public.token_transactions 
ADD COLUMN IF NOT EXISTS transfer_id UUID REFERENCES public.token_transfers(id) ON DELETE SET NULL;

-- Enable RLS on token_transfers
ALTER TABLE public.token_transfers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for token_transfers
CREATE POLICY "Users can view their own transfers as sender"
ON public.token_transfers FOR SELECT 
USING (auth.uid() = sender_id);

CREATE POLICY "Users can view their own transfers as recipient"
ON public.token_transfers FOR SELECT 
USING (auth.uid() = recipient_id);

-- Function to find user by username
CREATE OR REPLACE FUNCTION public.find_user_by_username(p_username TEXT)
RETURNS TABLE (
    user_id UUID,
    username TEXT,
    display_name TEXT,
    avatar_url TEXT,
    is_active BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        up.id as user_id,
        up.username,
        up.display_name,
        up.avatar_url,
        (up.created_at IS NOT NULL AND up.id IS NOT NULL) as is_active
    FROM public.user_profiles up
    WHERE LOWER(up.username) = LOWER(p_username)
    AND up.username IS NOT NULL;
END;
$$;

-- Function to transfer tokens between users
CREATE OR REPLACE FUNCTION public.transfer_tokens_between_users(
    p_sender_id UUID,
    p_recipient_username TEXT,
    p_amount INTEGER,
    p_transfer_message TEXT DEFAULT NULL
)
RETURNS TABLE (
    success BOOLEAN,
    transfer_id UUID,
    sender_new_balance INTEGER,
    recipient_new_balance INTEGER,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_recipient_id UUID;
    v_recipient_username TEXT;
    v_sender_balance INTEGER;
    v_recipient_balance INTEGER;
    v_sender_new_balance INTEGER;
    v_recipient_new_balance INTEGER;
    v_transfer_id UUID;
BEGIN
    -- Validate input
    IF p_amount <= 0 THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 0, 0, 'Transfer amount must be positive';
        RETURN;
    END IF;
    
    -- Find recipient by username
    SELECT user_id, username INTO v_recipient_id, v_recipient_username
    FROM public.find_user_by_username(p_recipient_username)
    LIMIT 1;
    
    IF v_recipient_id IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 0, 0, 'Recipient username not found';
        RETURN;
    END IF;
    
    -- Prevent self-transfer
    IF p_sender_id = v_recipient_id THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 0, 0, 'Cannot transfer tokens to yourself';
        RETURN;
    END IF;
    
    -- Lock both users' token records (order by UUID to prevent deadlock)
    IF p_sender_id < v_recipient_id THEN
        SELECT token_balance INTO v_sender_balance
        FROM public.user_tokens 
        WHERE user_id = p_sender_id 
        FOR UPDATE;
        
        SELECT token_balance INTO v_recipient_balance
        FROM public.user_tokens 
        WHERE user_id = v_recipient_id 
        FOR UPDATE;
    ELSE
        SELECT token_balance INTO v_recipient_balance
        FROM public.user_tokens 
        WHERE user_id = v_recipient_id 
        FOR UPDATE;
        
        SELECT token_balance INTO v_sender_balance
        FROM public.user_tokens 
        WHERE user_id = p_sender_id 
        FOR UPDATE;
    END IF;
    
    -- Check if sender exists and has tokens initialized
    IF v_sender_balance IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 0, 0, 'Sender tokens not initialized';
        RETURN;
    END IF;
    
    -- Check if recipient exists, initialize if needed
    IF v_recipient_balance IS NULL THEN
        INSERT INTO public.user_tokens (user_id, token_balance, total_tokens_earned)
        VALUES (v_recipient_id, 0, 0);
        v_recipient_balance := 0;
    END IF;
    
    -- Check sufficient balance
    IF v_sender_balance < p_amount THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, v_sender_balance, v_recipient_balance, 'Insufficient tokens for transfer';
        RETURN;
    END IF;
    
    -- Calculate new balances
    v_sender_new_balance := v_sender_balance - p_amount;
    v_recipient_new_balance := v_recipient_balance + p_amount;
    
    -- Generate transfer ID
    v_transfer_id := uuid_generate_v4();
    
    -- Create transfer record
    INSERT INTO public.token_transfers (
        id,
        sender_id,
        recipient_id,
        amount,
        sender_balance_before,
        sender_balance_after,
        recipient_balance_before,
        recipient_balance_after,
        status,
        transfer_message,
        completed_at
    ) VALUES (
        v_transfer_id,
        p_sender_id,
        v_recipient_id,
        p_amount,
        v_sender_balance,
        v_sender_new_balance,
        v_recipient_balance,
        v_recipient_new_balance,
        'completed',
        p_transfer_message,
        now()
    );
    
    -- Update sender balance
    UPDATE public.user_tokens 
    SET 
        token_balance = v_sender_new_balance,
        total_tokens_spent = total_tokens_spent + p_amount,
        updated_at = now()
    WHERE user_id = p_sender_id;
    
    -- Update recipient balance
    UPDATE public.user_tokens 
    SET 
        token_balance = v_recipient_new_balance,
        total_tokens_earned = total_tokens_earned + p_amount,
        updated_at = now()
    WHERE user_id = v_recipient_id;
    
    -- Record sender transaction
    INSERT INTO public.token_transactions (
        user_id,
        transaction_type,
        amount,
        balance_after,
        description,
        transfer_id
    ) VALUES (
        p_sender_id,
        'transfer_sent',
        -p_amount,
        v_sender_new_balance,
        CASE 
            WHEN p_transfer_message IS NOT NULL THEN 
                format('Sent %s tokens to @%s: %s', p_amount, v_recipient_username, p_transfer_message)
            ELSE 
                format('Sent %s tokens to @%s', p_amount, v_recipient_username)
        END,
        v_transfer_id
    );
    
    -- Record recipient transaction
    INSERT INTO public.token_transactions (
        user_id,
        transaction_type,
        amount,
        balance_after,
        description,
        transfer_id
    ) VALUES (
        v_recipient_id,
        'transfer_received',
        p_amount,
        v_recipient_new_balance,
        CASE 
            WHEN p_transfer_message IS NOT NULL THEN 
                format('Received %s tokens from @%s: %s', p_amount, (SELECT username FROM public.user_profiles WHERE id = p_sender_id), p_transfer_message)
            ELSE 
                format('Received %s tokens from @%s', p_amount, (SELECT username FROM public.user_profiles WHERE id = p_sender_id))
        END,
        v_transfer_id
    );
    
    RETURN QUERY SELECT TRUE, v_transfer_id, v_sender_new_balance, v_recipient_new_balance, NULL::TEXT;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Update transfer record as failed if it was created
        IF v_transfer_id IS NOT NULL THEN
            UPDATE public.token_transfers 
            SET status = 'failed', error_message = SQLERRM 
            WHERE id = v_transfer_id;
        END IF;
        
        RETURN QUERY SELECT FALSE, v_transfer_id, COALESCE(v_sender_balance, 0), COALESCE(v_recipient_balance, 0), SQLERRM;
END;
$$;

-- Function to get user's transfer history
CREATE OR REPLACE FUNCTION public.get_user_transfer_history(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    transfer_id UUID,
    is_sender BOOLEAN,
    other_user_id UUID,
    other_username TEXT,
    other_display_name TEXT,
    other_avatar_url TEXT,
    amount INTEGER,
    transfer_message TEXT,
    status TEXT,
    created_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tt.id as transfer_id,
        (tt.sender_id = p_user_id) as is_sender,
        CASE 
            WHEN tt.sender_id = p_user_id THEN tt.recipient_id 
            ELSE tt.sender_id 
        END as other_user_id,
        CASE 
            WHEN tt.sender_id = p_user_id THEN up_recipient.username 
            ELSE up_sender.username 
        END as other_username,
        CASE 
            WHEN tt.sender_id = p_user_id THEN up_recipient.display_name 
            ELSE up_sender.display_name 
        END as other_display_name,
        CASE 
            WHEN tt.sender_id = p_user_id THEN up_recipient.avatar_url 
            ELSE up_sender.avatar_url 
        END as other_avatar_url,
        tt.amount,
        tt.transfer_message,
        tt.status,
        tt.created_at,
        tt.completed_at
    FROM public.token_transfers tt
    LEFT JOIN public.user_profiles up_sender ON tt.sender_id = up_sender.id
    LEFT JOIN public.user_profiles up_recipient ON tt.recipient_id = up_recipient.id
    WHERE tt.sender_id = p_user_id OR tt.recipient_id = p_user_id
    ORDER BY tt.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.find_user_by_username(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.transfer_tokens_between_users(UUID, TEXT, INTEGER, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_transfer_history(UUID, INTEGER, INTEGER) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.token_transfers IS 'Records all token transfers between users';
COMMENT ON FUNCTION public.transfer_tokens_between_users(UUID, TEXT, INTEGER, TEXT) IS 'Transfers tokens from sender to recipient by username';
COMMENT ON FUNCTION public.find_user_by_username(TEXT) IS 'Finds user information by username for transfer validation';
COMMENT ON FUNCTION public.get_user_transfer_history(UUID, INTEGER, INTEGER) IS 'Gets transfer history for a user with pagination';