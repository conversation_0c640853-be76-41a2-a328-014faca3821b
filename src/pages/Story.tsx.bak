import React, { useState, useEffect, useRef, useCallback } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import {
  useStory,
  Story,
  StoryContributionMode,
} from "@/contexts/StoryContext";
import { useRealTime } from "@/hooks/use-real-time";
import { ContributionMode } from "@/services/realTimeService";
import { ContributionValidator } from "@/services/contributionValidator";
import { useSubscription } from "@/hooks/use-subscription";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Calendar,
  Check,
  Clock,
  Download,
  Edit,
  Flag,
  Loader2,
  Share2,
  Star,
  User,
  Users,
  Pencil,
  Bell,
  Trash2,
  AlertTriangle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { AiWordSuggestions } from "@/components/story/AiWordSuggestions";
import AiContributionSuggestions from "@/components/story/AiContributionSuggestions";
import SocialShare from "@/components/story/SocialShare";
import { useMutation } from "@tanstack/react-query";
import type { Word } from "@/types";
import { useCreditOperation } from "@/hooks/use-credit-operation";

const TYPING_UPDATE_INTERVAL = 500;

const StoryPage: React.FC = () => {
  const { storyId } = useParams<{ storyId: string }>();
  const {
    getStoryById,
    activeStory,
    addWord,
    addContribution,
    completeStory,
    publishStory,
    updateTypingState,
    clearTypingState,
    nudgeCurrentUser,
    deleteStory,
  } = useStory();
  const realTime = useRealTime();
  const { user, isAuthenticated } = useAuth();
  const { useCredits, credits } = useSubscription();
  const { handleCreditOperation, isUsingCredits } = useCreditOperation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [loading, setLoading] = useState(true);
  const [inputValue, setInputValue] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [isCompletingStory, setIsCompletingStory] = useState(false);
  const [isPublishingStory, setIsPublishingStory] = useState(false);
  const [isNudging, setIsNudging] = useState(false);

  const storyContentRef = useRef<HTMLDivElement>(null);
  const typingUpdateTimeout = useRef<NodeJS.Timeout | null>(null);

  const { mutate: nudgeUser, isPending } = useMutation({
    mutationFn: async () => {
      if (!storyId || !activeStory || !user) {
        throw new Error("Cannot nudge user at this time");
      }

      const success = await handleCreditOperation("nudge");
      if (!success) {
        throw new Error("Failed to use credits for nudging");
      }

      const nudgeSuccess = await nudgeCurrentUser(storyId);
      if (!nudgeSuccess) {
        throw new Error("Could not send the nudge");
      }

      return nudgeSuccess;
    },
    onSuccess: () => {
      toast({
        title: "Nudge Sent",
        description: "Successfully nudged the current player.",
      });
    },
    onError: (error: Error) => {
      console.error("Error nudging user:", error);
      toast({
        title: "Error",
        description:
          error.message || "An error occurred while nudging the user.",
        variant: "destructive",
      });
    },
  });

  useEffect(() => {
    const loadStory = async () => {
      if (!storyId) return;

      try {
        await getStoryById(storyId);
        setLoading(false);
      } catch (err) {
        console.error("Error loading story:", err);
        setLoading(false);
      }
    };

    loadStory();
  }, [storyId, getStoryById]);

  useEffect(() => {
    if (!storyId) return;

    const pollInterval = setInterval(async () => {
      try {
        if (!user || activeStory?.typingInfo?.userId !== user.id) {
          await getStoryById(storyId);
        }
      } catch (err) {
        console.error("Error polling story updates:", err);
      }
    }, 3000);

    return () => clearInterval(pollInterval);
  }, [storyId, getStoryById, activeStory, user]);

  useEffect(() => {
    if (storyContentRef.current) {
      storyContentRef.current.scrollTop = storyContentRef.current.scrollHeight;
    }
  }, [activeStory?.words, activeStory?.typingInfo]);

  // Handle input changes and typing notifications
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Only send typing updates if it's the user's turn
    if (
      activeStory &&
      user &&
      activeStory.currentTurn === user.id &&
      !typingUpdateTimeout.current
    ) {
      updateTypingState(activeStory.id, true);
      
      // Also broadcast typing status to real-time service
      if (storyId) {
        realTime.broadcastTypingStatus(storyId, user.id, user.username, true);
      }

      // Clear previous timeout
      if (typingUpdateTimeout.current) {
        clearTimeout(typingUpdateTimeout.current);
      }

      // Set new timeout
      typingUpdateTimeout.current = setTimeout(() => {
        if (activeStory && user) {
          updateTypingState(activeStory.id, false);
          
          // Also broadcast typing stopped to real-time service
          if (storyId) {
            realTime.broadcastTypingStatus(storyId, user.id, user.username, false);
          }
        }
        typingUpdateTimeout.current = null;
      }, TYPING_UPDATE_INTERVAL);
    }
  };

  // Update word count when input changes
  useEffect(() => {
    if (inputValue) {
      const words = inputValue.trim().split(/\s+/);
      setWordCount(words.length);
    } else {
      setWordCount(0);
    }
  }, [inputValue]);

  // Validate input based on contribution mode
  const validateInput = (content: string): { isValid: boolean; error?: string } => {
    if (!activeStory) return { isValid: false, error: 'Story not found' };
    
    // Convert StoryContributionMode to ContributionMode
    let mode: ContributionMode;
    switch (activeStory.contributionMode) {
      case StoryContributionMode.WORD:
        mode = ContributionMode.WORD;
        break;
      case StoryContributionMode.MULTI_WORD:
        mode = ContributionMode.MULTI_WORD;
        break;
      case StoryContributionMode.SENTENCE:
        mode = ContributionMode.SENTENCE;
        break;
      case StoryContributionMode.PARAGRAPH:
        mode = ContributionMode.PARAGRAPH;
        break;
      default:
        mode = ContributionMode.WORD;
    }
    
    return ContributionValidator.validate(
      content,
      mode,
      activeStory.wordsPerContribution
    );
  };

  // Handle contribution submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim() || !isAuthenticated || !user || !activeStory) {
      return;
    }

    if (activeStory.currentTurn !== user.id) {
      toast({
        title: "Not your turn",
        description: "Please wait for your turn to contribute.",
        variant: "destructive",
      });
      return;
    }
    
    // Validate input based on contribution mode
    const validation = validateInput(inputValue.trim());
    if (!validation.isValid) {
      setValidationError(validation.error || 'Invalid contribution');
      return;
    }
    
    setValidationError(null);

    try {
      setIsSubmitting(true);
      
      // Use appropriate method based on contribution mode
      if (activeStory.contributionMode === StoryContributionMode.WORD) {
        await addWord(activeStory.id, inputValue.trim());
      } else {
        await addContribution(
          activeStory.id, 
          inputValue.trim(), 
          activeStory.contributionMode
        );
      }
      
      setInputValue("");
      clearTypingState(activeStory.id);
      
      // Broadcast typing status stopped
      if (storyId && user) {
        realTime.broadcastTypingStatus(storyId, user.id, user.username, false);
      }
    } catch (error) {
      console.error("Error adding contribution:", error);
      toast({
        title: "Error",
        description:
          "There was a problem adding your contribution. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteStory = async () => {
    if (!storyId || !activeStory) return;

    try {
      const success = await deleteStory(storyId);
      if (success) {
        toast({
          title: "Story deleted",
          description: "The story has been successfully deleted.",
        });
        navigate("/dashboard");
      } else {
        toast({
          title: "Error",
          description: "Failed to delete the story. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting story:", error);
      toast({
        title: "Error",
        description: "An error occurred while deleting the story.",
        variant: "destructive",
      });
    }
  };

  const isParticipant =
    activeStory?.participants.some((p) => p.id === user?.id) || false;
  const isUserTurn = activeStory?.currentTurn === user?.id;
  const canNudge =
    isParticipant && !isUserTurn && user?.credits && user.credits >= 1;

  const storyText = activeStory?.words.map((w) => w.word).join(" ");

  const getContributionLabel = () => {
    if (!activeStory) return "Contribution";

    switch (activeStory.contributionMode) {
      case StoryContributionMode.WORD:
        return "Word";
      case StoryContributionMode.MULTI_WORD:
        return `${activeStory.wordsPerContribution} Words`;
      case StoryContributionMode.SENTENCE:
        return "Sentence";
      case StoryContributionMode.PARAGRAPH:
        return "Paragraph";
      default:
        return "Contribution";
    }
  };

  const getContributionPlaceholder = () => {
    if (!activeStory) return "Add your contribution...";

    switch (activeStory.contributionMode) {
      case StoryContributionMode.WORD:
        return "Add your word...";
      case StoryContributionMode.MULTI_WORD:
        return `Add ${activeStory.wordsPerContribution} words...`;
      case StoryContributionMode.SENTENCE:
        return "Add your sentence...";
      case StoryContributionMode.PARAGRAPH:
        return "Add your paragraph...";
      default:
        return "Add your contribution...";
    }
  };

  const requiresRegistration = (mode?: StoryContributionMode) => {
    return mode !== StoryContributionMode.WORD;
  };

  const getTypingUser = () => {
    if (!activeStory?.typingInfo) return null;

    return activeStory.participants.find(
      (p) => p.id === activeStory.typingInfo?.userId,
    );
  };

  const typingUser = getTypingUser();
  const isCurrentUserTyping = typingUser?.id === user?.id;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-literary-burgundy/20 rounded-full"></div>
          <div className="h-4 w-48 mt-4 bg-literary-burgundy/20 rounded"></div>
          <div className="h-3 w-36 mt-2 bg-literary-burgundy/10 rounded"></div>
        </div>
      </div>
    );
  }

  if (!activeStory) {
    return (
      <div className="max-w-3xl mx-auto text-center py-12">
        <h2 className="text-2xl font-serif mb-4">Story Not Found</h2>
        <p className="text-gray-600 mb-6">
          The story you're looking for doesn't exist or has been removed.
        </p>
        <Button onClick={() => navigate("/dashboard")}>
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-serif">{activeStory.title}</h1>
            {activeStory.status === "active" ? (
              <Badge className="bg-green-600 hover:bg-green-700">Active</Badge>
            ) : (
              <Badge
                variant="outline"
                className="border-gray-400 text-gray-600"
              >
                Completed
              </Badge>
            )}
          </div>
          <div className="flex items-center text-sm text-gray-600 mt-2">
            <Users className="h-4 w-4 mr-1" />
            <span>{activeStory.participants.length} participants</span>
            <span className="mx-2">•</span>
            <Calendar className="h-4 w-4 mr-1" />
            <span>
              Created{" "}
              {formatDistanceToNow(new Date(activeStory.createdAt), {
                addSuffix: true,
              })}
            </span>
            <span className="mx-2">•</span>
            <span>
              {activeStory.contributionMode === StoryContributionMode.WORD
                ? "One word"
                : activeStory.contributionMode === StoryContributionMode.MULTI_WORD
                  ? `${activeStory.wordsPerContribution} words`
                  : activeStory.contributionMode === StoryContributionMode.SENTENCE
                    ? "Full sentences"
                    : "Paragraphs"}{" "}
              at a time
            </span>
          </div>
        </div>

        <div className="flex mt-4 md:mt-0 space-x-2">
          {activeStory?.createdBy.id === user?.id && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Story
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Story</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{activeStory?.title}"? This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteStory()}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          {activeStory?.status === "completed" && !activeStory?.isPublished && (
            <Button
              variant="outline"
              onClick={() => publishStory(activeStory.id)}
              disabled={isPublishingStory}
            >
              {isPublishingStory ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Star className="h-4 w-4 mr-2" />
              )}
              Publish to Gallery
            </Button>
          )}

          {(activeStory?.status === "completed" ||
            activeStory?.isPublished) && (
            <SocialShare
              storyId={storyId || ""}
              storyTitle={activeStory?.title || ""}
            />
          )}

          <Button variant="outline" className="px-2">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-3">
          <Card className="shadow-lg border-literary-gold/10">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Story</CardTitle>
              <CardDescription>
                {activeStory.wordCount}{" "}
                {activeStory.contributionMode !== StoryContributionMode.WORD &&
                activeStory.contributionMode !== StoryContributionMode.MULTI_WORD
                  ? "contributions"
                  : "words"}{" "}
                • Last updated{" "}
                {formatDistanceToNow(new Date(activeStory.updatedAt), {
                  addSuffix: true,
                })}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                ref={storyContentRef}
                className="paper-bg min-h-[300px] max-h-[500px] p-5 rounded-md overflow-y-auto leading-relaxed text-literary-ink"
              >
                {activeStory.words.map((wordObj, idx) => (
                  <span
                    key={idx}
                    className="story-word"
                    style={{
                      animationDelay: `${idx * 0.05}s`,
                    }}
                  >
                    {wordObj.word}
                    {idx !== activeStory.words.length - 1 ? " " : ""}
                  </span>
                ))}

                {activeStory.status === "active" &&
                  typingUser &&
                  !isCurrentUserTyping && (
                    <div className="mt-2 border-t border-dashed border-gray-300 pt-2">
                      <div className="flex items-center text-gray-500 text-sm mb-1">
                        <Pencil className="h-3 w-3 mr-1 animate-pulse" />
                        <span>{typingUser.username} is typing...</span>
                      </div>
                      <div className="text-gray-400 italic">
                        {activeStory.typingInfo?.content}
                        <span className="animate-pulse">|</span>
                      </div>
                    </div>
                  )}
              </div>
            </CardContent>
            {activeStory.status === "active" && (
              <CardFooter className="border-t pt-4">
                {isParticipant ? (
                  isUserTurn ? (
                    <form onSubmit={handleSubmit} className="mt-4">
                      <div className="space-y-2">
                        {activeStory?.contributionMode === StoryContributionMode.WORD && (
                          <div className="flex items-center space-x-2">
                            <Input
                              type="text"
                              placeholder="Enter your word..."
                              value={inputValue}
                              onChange={handleInputChange}
                              disabled={
                                !isAuthenticated ||
                                !user ||
                                !activeStory ||
                                activeStory.currentTurn !== user.id ||
                                activeStory.status !== "active" ||
                                isSubmitting
                              }
                              className="flex-1"
                            />
                            <Button
                              type="submit"
                              disabled={
                                !inputValue.trim() ||
                                !isAuthenticated ||
                                !user ||
                                !activeStory ||
                                activeStory.currentTurn !== user.id ||
                                activeStory.status !== "active" ||
                                isSubmitting
                              }
                            >
                              {isSubmitting ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                "Submit"
                              )}
                            </Button>
                          </div>
                        )}
                        
                        {activeStory?.contributionMode === StoryContributionMode.MULTI_WORD && (
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Input
                                type="text"
                                placeholder={`Enter up to ${activeStory.wordsPerContribution} words...`}
                                value={inputValue}
                                onChange={handleInputChange}
                                disabled={
                                  !isAuthenticated ||
                                  !user ||
                                  !activeStory ||
                                  activeStory.currentTurn !== user.id ||
                                  activeStory.status !== "active" ||
                                  isSubmitting
                                }
                                className="flex-1"
                              />
                              <Button
                                type="submit"
                                disabled={
                                  !inputValue.trim() ||
                                  !isAuthenticated ||
                                  !user ||
                                  !activeStory ||
                                  activeStory.currentTurn !== user.id ||
                                  activeStory.status !== "active" ||
                                  isSubmitting ||
                                  wordCount > (activeStory.wordsPerContribution || 5)
                                }
                              >
                                {isSubmitting ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  "Submit"
                                )}
                              </Button>
                            </div>
                            <p className="text-xs text-gray-500">
                              {wordCount}/{activeStory.wordsPerContribution || 5} words
                            </p>
                          </div>
                        )}
                        
                        {validationError && (
                        <div className="text-red-500 text-sm mt-2">{validationError}</div>
                      )}

                      {isUserTurn &&
                        activeStory.contributionMode === "word" && (
                          <AiWordSuggestions
                            currentWord={inputValue}
                            words={activeStory.words}
                            onSelectWord={(word) => setInputValue(word)}
                            storyId={storyId || ""}
                          />
                        )}

                      {isUserTurn &&
                        activeStory.contributionMode === "sentence" &&
                        user && (
                          <AiContributionSuggestions
                            storyId={storyId || ""}
                            mode="sentence"
                            words={activeStory.words}
                            onSelectContribution={(sentence) =>
                              setInputValue(sentence)
                            }
                          />
                        )}

                      {isUserTurn &&
                        activeStory.contributionMode === "paragraph" &&
                        user && (
                          <AiContributionSuggestions
                            storyId={storyId || ""}
                            mode="paragraph"
                            words={activeStory.words}
                            onSelectContribution={(paragraph) =>
                              setInputValue(paragraph)
                            }
                          />
                        )}
                    </form>
                  ) : (
                    <div className="w-full">
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600 flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>
                            Waiting for{" "}
                            {activeStory.participants.find(
                              (p) => p.id === activeStory.currentTurn,
                            )?.username || "someone"}{" "}
                            to add{" "}
                            {activeStory.contributionMode === "word"
                              ? "a word"
                              : activeStory.contributionMode === "words"
                                ? `${activeStory.wordsPerContribution} words`
                                : `a ${activeStory.contributionMode}`}{" "}
                            ...
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            onClick={(e) => {
                              e.preventDefault();
                              nudgeUser();
                            }}
                            disabled={isPending || !canNudge || isUsingCredits}
                            variant="outline"
                            size="sm"
                            className="border-amber-500 text-amber-600 hover:bg-amber-500 hover:text-white"
                          >
                            {isPending || isUsingCredits ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Bell className="h-4 w-4 mr-2" />
                            )}
                            Nudge (1 credit)
                          </Button>

                          {activeStory.createdBy.id === user?.id && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => completeStory(activeStory.id)}
                              disabled={isCompletingStory}
                              className="border-literary-burgundy text-literary-burgundy hover:bg-literary-burgundy hover:text-white"
                            >
                              {isCompletingStory ? (
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                              ) : (
                                <Check className="h-4 w-4 mr-1" />
                              )}
                              Complete Story
                            </Button>
                          )}
                        </div>
                      </div>

                      {activeStory.lastNudged &&
                        activeStory.lastNudged.timestamp && (
                          <div className="mt-2 text-xs text-amber-600 flex items-center">
                            <Bell className="h-3 w-3 mr-1" />
                            <span>
                              {activeStory.participants.find(
                                (p) =>
                                  p.id === activeStory.lastNudged?.nudgedBy,
                              )?.username || "Someone"}{" "}
                              nudged{" "}
                              {formatDistanceToNow(
                                new Date(activeStory.lastNudged.timestamp),
                                { addSuffix: true },
                              )}
                            </span>
                          </div>
                        )}
                    </div>
                  )
                ) : (
                  <div className="w-full text-center p-3 bg-amber-50 rounded-md">
                    <AlertTriangle className="h-4 w-4 text-amber-600 mx-auto mb-1" />
                    <p className="text-amber-800 text-sm">
                      {requiresRegistration(activeStory.contributionMode) &&
                      !isAuthenticated ? (
                        <>
                          This story requires{" "}
                          {activeStory.contributionMode === "words"
                            ? `${activeStory.wordsPerContribution} words`
                            : `full ${activeStory.contributionMode}`}{" "}
                          contributions.
                          <Link
                            to="/login"
                            className="font-medium text-literary-burgundy underline ml-1"
                          >
                            Sign in
                          </Link>{" "}
                          to join.
                        </>
                      ) : (
                        "You're not a participant in this story. Ask the creator to invite you to contribute."
                      )}
                    </p>
                  </div>
                )}
              </CardFooter>
            )}
          </Card>
        </div>

        <div className="md:col-span-1">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Participants</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {activeStory.participants.map((participant) => (
                <div
                  key={participant.id}
                  className={`flex items-center p-2 rounded-md ${
                    participant.id === activeStory.currentTurn &&
                    activeStory.status === "active"
                      ? "bg-literary-burgundy/10 border border-literary-burgundy/30"
                      : ""
                  } ${typingUser?.id === participant.id ? "animate-pulse" : ""}`}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={participant.profilePicture}
                      alt={participant.username}
                    />
                    <AvatarFallback className="bg-literary-navy/10 text-literary-navy">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-2">
                    <p className="text-sm font-medium leading-none">
                      {participant.username}
                      {activeStory.createdBy.id === participant.id && (
                        <span className="ml-1 text-xs text-gray-500">
                          (Creator)
                        </span>
                      )}
                    </p>
                    {participant.id === activeStory.currentTurn &&
                      activeStory.status === "active" && (
                        <p className="text-xs text-literary-burgundy font-medium mt-1">
                          Current Turn
                        </p>
                      )}
                    {typingUser?.id === participant.id && (
                      <p className="text-xs text-green-600 font-medium mt-1 flex items-center">
                        <Pencil className="h-3 w-3 mr-1 animate-pulse" />
                        Typing...
                      </p>
                    )}
                  </div>
                </div>
              ))}

              {activeStory.createdBy.id === user?.id &&
                activeStory.status === "active" &&
                activeStory.participants.length < getParticipantLimit() && (
                  <Link to={`/invite/${storyId}`}>
                    <Button
                      variant="outline"
                      className="w-full mt-4 text-sm border-dashed"
                    >
                      + Invite More People
                    </Button>
                  </Link>
                )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

const getParticipantLimit = () => {
  const user = JSON.parse(localStorage.getItem("oneWordStoryUser") || "{}");

  switch (user?.tier) {
    case "authors-guild":
      return Infinity; // Unlimited
    case "storyteller":
      return 10;
    case "wordsmith":
      return 5;
    default:
      return 3; // Free tier
  }
};

export default StoryPage;
