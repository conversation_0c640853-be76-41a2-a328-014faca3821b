import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import TermsAndConditions from "@/components/legal/TermsAndConditions";

const TermsPage: React.FC = () => {
  return (
    <div className="container max-w-3xl py-8 space-y-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" className="mr-4" asChild>
          <Link to="/">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to home
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Terms and Conditions</h1>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="h-auto">
          <TermsAndConditions />
        </div>
      </div>
    </div>
  );
};

export default TermsPage;
