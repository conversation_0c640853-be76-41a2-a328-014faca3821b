-- Firebase Auth Compatibility Migration - Final Version
-- Correct order: Drop policies BEFORE altering column types

DO $$
BEGIN
    -- Step 1: Drop all RLS policies FIRST (before altering columns)
    DROP POLICY IF EXISTS "Users can read their own notifications" ON public.notifications;
    DROP POLICY IF EXISTS "Users can update their own notifications (mark as read/unread)" ON public.notifications;
    DROP POLICY IF EXISTS "Users can view all profiles" ON public.user_profiles;
    DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
    DROP POLICY IF EXISTS "Stories are viewable by everyone" ON public.stories;
    DROP POLICY IF EXISTS "Users can create their own stories" ON public.stories;
    DROP POLICY IF EXISTS "Users can update their own stories" ON public.stories;
    DROP POLICY IF EXISTS "Users can delete their own stories" ON public.stories;
    DROP POLICY IF EXISTS "Contributions are viewable by everyone" ON public.contributions;
    DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
    DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
    DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;
    
    -- Step 2: Drop triggers and functions
    DROP TRIGGER IF EXISTS trigger_new_contribution_notification ON public.contributions;
    DROP FUNCTION IF EXISTS public.handle_new_contribution_notification();
    
    -- Step 3: Drop all foreign key constraints
    ALTER TABLE public.notifications 
        DROP CONSTRAINT IF EXISTS notifications_user_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_actor_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_story_id_fkey,
        DROP CONSTRAINT IF EXISTS notifications_contribution_id_fkey;
    
    ALTER TABLE public.stories DROP CONSTRAINT IF EXISTS stories_author_id_fkey;
    ALTER TABLE public.contributions DROP CONSTRAINT IF EXISTS contributions_user_id_fkey;
    
    -- Step 4: Drop indexes
    DROP INDEX IF EXISTS idx_notifications_user_id_is_read;
    DROP INDEX IF EXISTS idx_notifications_user_id_created_at;
    
    -- Step 5: NOW we can safely alter column types
    ALTER TABLE public.notifications 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT,
        ALTER COLUMN actor_id TYPE TEXT USING actor_id::TEXT;
    
    ALTER TABLE public.user_profiles 
        ALTER COLUMN id TYPE TEXT USING id::TEXT;
    
    ALTER TABLE public.stories 
        ALTER COLUMN author_id TYPE TEXT USING author_id::TEXT;
    
    ALTER TABLE public.contributions 
        ALTER COLUMN user_id TYPE TEXT USING user_id::TEXT;
    
    -- Step 6: Recreate indexes
    CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
    CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);
    
    -- Step 7: Create new service role policies
    CREATE POLICY "Service role can manage all notifications"
    ON public.notifications FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all profiles"
    ON public.user_profiles FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all stories"
    ON public.stories FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    CREATE POLICY "Service role can manage all contributions"
    ON public.contributions FOR ALL 
    USING (true)
    WITH CHECK (true);
    
    RAISE NOTICE 'Firebase Auth compatibility migration completed successfully!';
END $$; 