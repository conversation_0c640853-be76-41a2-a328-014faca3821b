<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Word by Word Story - Static Demo</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
          Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        line-height: 1.6;
        color: #333;
      }
      h1 {
        color: #2563eb;
        text-align: center;
      }
      .card {
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        background-color: #f9f9f9;
      }
      .story-container {
        margin-top: 2rem;
      }
      .story {
        padding: 1rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin-bottom: 1rem;
      }
      .story-title {
        font-weight: bold;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
      }
      .story-content {
        margin-bottom: 0.5rem;
      }
      .story-meta {
        font-size: 0.8rem;
        color: #666;
      }
      .input-container {
        display: flex;
        margin-top: 1rem;
      }
      input {
        flex: 1;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-right: 0.5rem;
      }
      button {
        padding: 0.5rem 1rem;
        background-color: #2563eb;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #1d4ed8;
      }
      .note {
        background-color: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 1rem;
        margin-top: 2rem;
      }
    </style>
  </head>
  <body>
    <h1>Word by Word Story</h1>

    <div class="card">
      <h2>How It Works</h2>
      <p>
        Contribute to stories one word at a time, creating collaborative
        narratives with other users.
      </p>
      <p>
        Each person can only add one word at a time, making the story
        unpredictable and fun!
      </p>
    </div>

    <div class="story-container">
      <h2>Sample Stories</h2>

      <div class="story">
        <div class="story-title">The Mysterious Forest</div>
        <div class="story-content">
          Once upon a time there was a mysterious forest where animals could
          talk and trees would whisper secrets to travelers who...
        </div>
        <div class="story-meta">
          Contributors: 12 | Words: 24 | Created: 2 days ago
        </div>
      </div>

      <div class="story">
        <div class="story-title">Space Adventure</div>
        <div class="story-content">
          The spaceship crashed onto an unknown planet with strange purple
          vegetation and three moons that...
        </div>
        <div class="story-meta">
          Contributors: 8 | Words: 16 | Created: 5 days ago
        </div>
      </div>
    </div>

    <div class="card">
      <h2>Add Your Word</h2>
      <p>Select a story and add your word to continue the narrative.</p>

      <div class="input-container">
        <input
          type="text"
          placeholder="Enter a single word..."
          maxlength="20"
        />
        <button>Add Word</button>
      </div>
    </div>

    <div class="note">
      <h3>Static Demo</h3>
      <p>
        This is a static HTML demo of the Word by Word Story application. The
        actual application will have user authentication, real-time updates, and
        database storage.
      </p>
      <p>
        To deploy the full application to Vercel with Neon PostgreSQL, follow
        the instructions in the README.md file.
      </p>
    </div>

    <script>
      // Simple demo functionality
      document.querySelector("button").addEventListener("click", function () {
        const input = document.querySelector("input");
        const word = input.value.trim();

        if (word && word.split(" ").length === 1) {
          alert(`Word "${word}" added to the story!`);
          input.value = "";
        } else {
          alert("Please enter a single word.");
        }
      });
    </script>
  </body>
</html>
