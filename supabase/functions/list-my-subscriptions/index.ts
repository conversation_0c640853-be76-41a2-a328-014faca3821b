import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY for list-my-subscriptions function.');
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for list-my-subscriptions:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use GET.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10); // Default limit 25

    const validatedLimit = Math.min(Math.max(1, limit), 100); // Clamp limit
    const offset = (page - 1) * validatedLimit;

    // Fetch subscriptions for the authenticated user, joining with story details
    // RLS: "Allow user to see their own subscriptions"
    // RLS on stories: "Allow public read access to public stories" OR "Allow authenticated users to read their own stories"
    // This join will respect both.
    const query = supabase
      .from('subscriptions')
      .select(`
        id,
        story_id,
        user_id,
        created_at,
        stories (
          id,
          title,
          status,
          is_public,
          author_id
        )
      `, { count: 'exact' })
      .eq('user_id', user.id) // Filter by authenticated user's ID
      .order('created_at', { ascending: false }) // Sort by most recent subscription
      .range(offset, offset + validatedLimit - 1);
      
    const { data, error, count } = await query;

    if (error) {
      console.error('Supabase list-my-subscriptions error:', error);
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: (error as any).status || 500,
      });
    }

    return new Response(
      JSON.stringify({
        data,
        meta: {
          total_items: count || 0,
          current_page: page,
          items_per_page: validatedLimit,
          total_pages: Math.ceil((count || 0) / validatedLimit),
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200, // OK
      }
    );

  } catch (err) {
    console.error('Error processing list-my-subscriptions request:', err);
    const statusCode = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
}); 