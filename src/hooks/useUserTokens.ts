import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth/hooks';
import { tokenService, TokenBalance, SPECIAL_ACTION_COSTS } from '@/services/tokenService';
import { useToast } from '@/hooks/use-toast';

export interface UseUserTokensOptions {
  autoFetch?: boolean;
  refetchInterval?: number;
}

export interface UseUserTokensReturn {
  tokenBalance: number;
  totalEarned: number;
  totalSpent: number;
  isLoading: boolean;
  error: string | null;
  canAfford: (specialType: 'gotcha' | 'reverse' | 'golden') => boolean;
  getActionCost: (specialType: 'gotcha' | 'reverse' | 'golden') => number;
  processSpecialAction: (
    specialType: 'gotcha' | 'reverse' | 'golden',
    storyId: string,
    contributionId?: string
  ) => Promise<{ success: boolean; newBalance?: number; error?: string }>;
  addTokens: (amount: number, description?: string) => Promise<boolean>;
  refreshBalance: () => Promise<void>;
}

/**
 * Custom hook for managing user token operations
 */
export const useUserTokens = (options: UseUserTokensOptions = {}): UseUserTokensReturn => {
  const { autoFetch = true, refetchInterval } = options;
  const { user } = useAuth();
  const { toast } = useToast();

  const [tokenBalance, setTokenBalance] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [totalSpent, setTotalSpent] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch user's token balance
  const fetchTokenBalance = useCallback(async () => {
    if (!user?.id) {
      setTokenBalance(0);
      setTotalEarned(0);
      setTotalSpent(0);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const balance = await tokenService.getUserTokenBalance(user.id);
      
      if (balance) {
        setTokenBalance(balance.balance);
        setTotalEarned(balance.totalEarned);
        setTotalSpent(balance.totalSpent);
      } else {
        setError('Failed to fetch token balance');
      }
    } catch (err) {
      console.error('Error fetching token balance:', err);
      setError('Failed to fetch token balance');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Check if user can afford a special action
  const canAfford = useCallback((specialType: 'gotcha' | 'reverse' | 'golden'): boolean => {
    const cost = SPECIAL_ACTION_COSTS[specialType];
    return tokenBalance >= cost;
  }, [tokenBalance]);

  // Get the cost for a special action
  const getActionCost = useCallback((specialType: 'gotcha' | 'reverse' | 'golden'): number => {
    return SPECIAL_ACTION_COSTS[specialType];
  }, []);

  // Process a special action (spend tokens)
  const processSpecialAction = useCallback(async (
    specialType: 'gotcha' | 'reverse' | 'golden',
    storyId: string,
    contributionId?: string
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> => {
    if (!user?.id) {
      return { success: false, error: 'User not authenticated' };
    }

    const cost = getActionCost(specialType);
    
    if (!canAfford(specialType)) {
      return {
        success: false,
        error: `Insufficient tokens. You need ${cost} tokens but only have ${tokenBalance}.`
      };
    }

    try {
      const result = await tokenService.processSpecialAction(
        user.id,
        specialType,
        storyId,
        contributionId
      );

      if (result.success && result.newBalance !== undefined) {
        setTokenBalance(result.newBalance);
        setTotalSpent(prev => prev + cost);
        
        toast({
          title: 'Special Action Used!',
          description: `${specialType} action applied. ${cost} tokens spent.`,
          variant: 'default',
        });
      } else if (!result.success) {
        toast({
          title: 'Action Failed',
          description: result.error || 'Failed to process special action',
          variant: 'destructive',
        });
      }

      return result;
    } catch (err) {
      console.error('Error processing special action:', err);
      const errorMessage = 'Failed to process special action';
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      return { success: false, error: errorMessage };
    }
  }, [user?.id, tokenBalance, canAfford, getActionCost, toast]);

  // Add tokens to user's balance
  const addTokens = useCallback(async (amount: number, description?: string): Promise<boolean> => {
    if (!user?.id) {
      return false;
    }

    try {
      const result = await tokenService.addTokens(user.id, amount, description);
      
      if (result.success && result.newBalance !== undefined) {
        setTokenBalance(result.newBalance);
        setTotalEarned(prev => prev + amount);
        
        toast({
          title: 'Tokens Added!',
          description: `${amount} tokens added to your balance.`,
          variant: 'default',
        });
        
        return true;
      } else {
        toast({
          title: 'Failed to Add Tokens',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive',
        });
        
        return false;
      }
    } catch (err) {
      console.error('Error adding tokens:', err);
      toast({
        title: 'Error',
        description: 'Failed to add tokens',
        variant: 'destructive',
      });
      
      return false;
    }
  }, [user?.id, toast]);

  // Refresh balance manually
  const refreshBalance = useCallback(async () => {
    await fetchTokenBalance();
  }, [fetchTokenBalance]);

  // Auto-fetch on mount and when user changes
  useEffect(() => {
    if (autoFetch) {
      fetchTokenBalance();
    }
  }, [autoFetch, fetchTokenBalance]);

  // Set up interval for auto-refresh if specified
  useEffect(() => {
    if (refetchInterval && refetchInterval > 0) {
      const interval = setInterval(fetchTokenBalance, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [refetchInterval, fetchTokenBalance]);

  return {
    tokenBalance,
    totalEarned,
    totalSpent,
    isLoading,
    error,
    canAfford,
    getActionCost,
    processSpecialAction,
    addTokens,
    refreshBalance,
  };
}; 