# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Deployment scripts
deploy-*.js
setup-vercel-env.js
vercel-cli-setup.md

# Local development files
comprehensive-fix.mjs
simple-express-server.mjs
express-server.mjs
run-local.bat
run-express.bat
fix-rollup.js
patch-rollup.js
build-local.cjs
fix-and-run.bat
simple-dev.js
server.js
public/
