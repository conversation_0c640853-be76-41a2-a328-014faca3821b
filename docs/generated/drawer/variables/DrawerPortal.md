[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [drawer](../README.md) / DrawerPortal

# Variable: DrawerPortal()

> `const` **DrawerPortal**: (`props`) => `Element` = `DrawerPrimitive.Portal`

Defined in: [src/components/ui/drawer.tsx:19](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/drawer.tsx#L19)

## Parameters

### props

`DialogPortalProps`

## Returns

`Element`
