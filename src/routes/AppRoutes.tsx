// This file is deprecated and should no longer be used.
// All routing has been moved to App.tsx
// This file is kept for reference only and should be removed once the new routing system is confirmed to be working.

import { Navigate } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import Index from "@/pages/Index";
import Login from "@/pages/Login";
import Register from "@/pages/Register";
import Story from "@/pages/Story";
import Dashboard from "@/pages/Dashboard";
import Gallery from "@/pages/Gallery";
import CreateStory from "@/pages/CreateStory";
import Profile from "@/pages/Profile";
import Pricing from "@/pages/Pricing";
import AdFreeSubscription from "@/pages/AdFreeSubscription";
import Terms from "@/pages/Terms";
import Admin from "@/pages/Admin";
import NotFound from "@/pages/NotFound";
import { useAuth } from "@/contexts/auth";

// This component is deprecated and no longer used
const AppRoutes = () => {
  return null;
};

export default AppRoutes;
