import React from "react";
import { useAuth } from "@/contexts/auth";
import { BookText } from "lucide-react";

interface Step1WelcomeProps {
  onContinue: () => void;
}

export const Step1Welcome: React.FC<Step1WelcomeProps> = ({ onContinue }) => {
  const { user } = useAuth();

  return (
    <div className="space-y-6 text-center">
      <div className="mx-auto bg-primary/10 h-20 w-20 rounded-full flex items-center justify-center">
        <BookText className="h-10 w-10 text-primary" />
      </div>

      <h2 className="text-2xl font-bold">
        Welcome to Word by Word Story
        {user?.username ? `, ${user.username}` : ""}!
      </h2>

      <p className="text-muted-foreground">
        Join our community of storytellers to create collaborative stories one
        word at a time. We're excited to see what you'll create!
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
        <div className="bg-card border rounded-lg p-4 text-left">
          <h3 className="font-medium mb-2">Create Stories</h3>
          <p className="text-sm text-muted-foreground">
            Start new stories and invite others to join
          </p>
        </div>

        <div className="bg-card border rounded-lg p-4 text-left">
          <h3 className="font-medium mb-2">Collaborate</h3>
          <p className="text-sm text-muted-foreground">
            Take turns adding words to build unique narratives
          </p>
        </div>

        <div className="bg-card border rounded-lg p-4 text-left">
          <h3 className="font-medium mb-2">Share & Discover</h3>
          <p className="text-sm text-muted-foreground">
            Share your creations and explore stories from others
          </p>
        </div>
      </div>
    </div>
  );
};
