[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [dialog](../README.md) / DialogDescription

# Variable: DialogDescription

> `const` **DialogDescription**: `ForwardRefExoticComponent`\<`Omit`\<`DialogDescriptionProps` & `RefAttributes`\<`HTMLParagraphElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLParagraphElement`\>\>

Defined in: [src/components/ui/dialog.tsx:97](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/dialog.tsx#L97)
