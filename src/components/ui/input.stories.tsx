import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Input } from './input';
import { Label } from './label';
import { Search as SearchIcon, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';

const meta: Meta<typeof Input> = {
  title: 'UI/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible input component that supports various types, states, and styling options.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'number', 'search', 'tel', 'url'],
      description: 'The input type',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the input is disabled',
    },
    value: {
      control: 'text',
      description: 'Input value (controlled)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic inputs
export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
  },
};

export const WithValue: Story = {
  args: {
    value: 'Pre-filled value',
    placeholder: 'Enter text...',
  },
};

export const Email: Story = {
  args: {
    type: 'email',
    placeholder: '<EMAIL>',
  },
};

export const Password: Story = {
  args: {
    type: 'password',
    placeholder: 'Enter password',
  },
};

export const SearchInput: Story = {
  args: {
    type: 'search',
    placeholder: 'Search stories...',
  },
};

export const Number: Story = {
  args: {
    type: 'number',
    placeholder: '0',
    min: 0,
    max: 100,
  },
};

// States
export const Disabled: Story = {
  args: {
    placeholder: 'Disabled input',
    disabled: true,
  },
};

export const ReadOnly: Story = {
  args: {
    value: 'Read-only value',
    readOnly: true,
  },
};

// With labels
export const WithLabel: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="story-title">Story Title</Label>
      <Input
        id="story-title"
        type="text"
        placeholder="Enter your story title"
      />
    </div>
  ),
};

export const WithHelperText: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="username">Username</Label>
      <Input
        id="username"
        type="text"
        placeholder="Enter username"
      />
      <p className="text-sm text-muted-foreground">
        Your username will be visible to other story contributors.
      </p>
    </div>
  ),
};

// Error states
export const WithError: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email-error">Email</Label>
      <Input
        id="email-error"
        type="email"
        placeholder="<EMAIL>"
        className="border-destructive focus-visible:ring-destructive"
        aria-describedby="email-error-message"
      />
      <p id="email-error-message" className="text-sm text-destructive">
        Please enter a valid email address.
      </p>
    </div>
  ),
};

export const WithSuccess: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email-success">Email</Label>
      <Input
        id="email-success"
        type="email"
        value="<EMAIL>"
        className="border-green-500 focus-visible:ring-green-500"
        aria-describedby="email-success-message"
      />
      <p id="email-success-message" className="text-sm text-green-600">
        Email format is valid.
      </p>
    </div>
  ),
};

// With icons
export const WithLeadingIcon: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="search-input">Search</Label>
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          id="search-input"
          type="search"
          placeholder="Search stories..."
          className="pl-10"
        />
      </div>
    </div>
  ),
};

export const WithTrailingIcon: Story = {
  render: () => (
    <div className="grid w-full max-w-sm items-center gap-1.5">
      <Label htmlFor="email-trailing">Email</Label>
      <div className="relative">
        <Input
          id="email-trailing"
          type="email"
          placeholder="<EMAIL>"
          className="pr-10"
        />
        <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      </div>
    </div>
  ),
};

// Interactive examples
export const PasswordToggle: Story = {
  render: () => {
    const [showPassword, setShowPassword] = useState(false);

    return (
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label htmlFor="password-toggle">Password</Label>
        <div className="relative">
          <Input
            id="password-toggle"
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter password"
            className="pr-10"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>
    );
  },
};

export const CharacterCounter: Story = {
  render: () => {
    const [value, setValue] = useState('');
    const maxLength = 50;

    return (
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label htmlFor="story-description">Story Description</Label>
        <Input
          id="story-description"
          type="text"
          placeholder="Describe your story..."
          value={value}
          onChange={(e) => setValue(e.target.value)}
          maxLength={maxLength}
        />
        <p className="text-sm text-muted-foreground text-right">
          {value.length}/{maxLength}
        </p>
      </div>
    );
  },
};

// Sizes demonstration
export const AllSizes: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label>Small Input</Label>
        <Input
          placeholder="Small input..."
          className="h-8 text-sm"
        />
      </div>
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label>Default Input</Label>
        <Input
          placeholder="Default input..."
        />
      </div>
      <div className="grid w-full max-w-sm items-center gap-1.5">
        <Label>Large Input</Label>
        <Input
          placeholder="Large input..."
          className="h-12 text-lg"
        />
      </div>
    </div>
  ),
};

// Form example
export const StoryCreationForm: Story = {
  render: () => (
    <div className="w-full max-w-md space-y-4">
      <div className="space-y-2">
        <Label htmlFor="story-title-form">Story Title *</Label>
        <Input
          id="story-title-form"
          type="text"
          placeholder="Enter an engaging title"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="story-description-form">Description</Label>
        <Input
          id="story-description-form"
          type="text"
          placeholder="Briefly describe your story concept"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="max-contributions">Maximum Contributions</Label>
        <Input
          id="max-contributions"
          type="number"
          placeholder="100"
          min="10"
          max="1000"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="tags">Tags (comma-separated)</Label>
        <Input
          id="tags"
          type="text"
          placeholder="adventure, fantasy, comedy"
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example form showing how inputs are used in the story creation process.',
      },
    },
  },
};