[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [button](../README.md) / buttonVariants

# Variable: buttonVariants()

> `const` **buttonVariants**: (`props?`) => `string`

Defined in: [src/components/ui/button.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/button.tsx#L7)

## Parameters

### props?

`ConfigVariants`\<\{ `size`: \{ `default`: `string`; `icon`: `string`; `lg`: `string`; `sm`: `string`; \}; `variant`: \{ `default`: `string`; `destructive`: `string`; `ghost`: `string`; `link`: `string`; `outline`: `string`; `secondary`: `string`; \}; \}\> & `ClassProp`

## Returns

`string`
