-- supabase/migrations/YYYYMMDDHHMMSS_update_user_profiles_for_stripe.sql

ALTER TABLE public.user_profiles
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT NULL,
ADD COLUMN IF NOT EXISTS tier TEXT NULL,
ADD COLUMN IF NOT EXISTS is_ad_free BOOLEAN DEFAULT FALSE;

COMMENT ON COLUMN public.user_profiles.stripe_customer_id IS 'Stores the Stripe Customer ID for this user, linked via user_profiles.id to auth.users.id.';
COMMENT ON COLUMN public.user_profiles.tier IS 'Represents the user''s subscription tier (e.g., free, premium) - managed by Stripe webhooks.';
COMMENT ON COLUMN public.user_profiles.is_ad_free IS 'Indicates if the user has an ad-free subscription - managed by Stripe webhooks.';

-- Optional: If you want to enforce that stripe_customer_id is unique if present
-- This ensures one Stripe customer ID cannot be accidentally linked to multiple profiles.
-- However, ensure this is desired as it might complicate things if a user changes Supabase auth account
-- but wants to keep their Stripe subscription. For now, it's commented out.
-- CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_stripe_customer_id_if_not_null
-- ON public.user_profiles (stripe_customer_id)
-- WHERE stripe_customer_id IS NOT NULL;