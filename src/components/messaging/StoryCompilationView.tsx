import React, { useState, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ArrowLeft,
  BookOpen,
  Download,
  Share2,
  Eye,
  FileText,
  Printer,
  Copy,
  Zap,
  RotateCcw,
  Crown,
  Settings,
  ChevronLeft,
  ChevronRight,
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { format } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import StoryNavigationBar from './StoryNavigationBar';

export interface Contribution {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
}

export interface StoryCompilationViewProps {
  storyTitle: string;
  storyDescription?: string;
  contributions: Contribution[];
  isOpen: boolean;
  onClose: () => void;
  onBackToThread?: () => void;
  onShare?: () => void;
  onExport?: (format: 'txt' | 'pdf' | 'markdown') => void;
  className?: string;
}

type ReadingMode = 'clean' | 'annotated' | 'manuscript' | 'timeline';
type FontSize = 'sm' | 'md' | 'lg' | 'xl';

const specialTypeConfig = {
  gotcha: {
    icon: Zap,
    label: 'Gotcha Word',
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950',
  },
  reverse: {
    icon: RotateCcw,
    label: 'Reverse Move',
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-950',
  },
  golden: {
    icon: Crown,
    label: 'Golden Contribution',
    color: 'text-amber-600 dark:text-amber-400',
    bgColor: 'bg-amber-50 dark:bg-amber-950',
  },
};

export const StoryCompilationView: React.FC<StoryCompilationViewProps> = ({
  storyTitle,
  storyDescription,
  contributions,
  isOpen,
  onClose,
  onBackToThread,
  onShare,
  onExport,
  className,
}) => {
  const { toast } = useToast();
  const printRef = useRef<HTMLDivElement>(null);

  const [readingMode, setReadingMode] = useState<ReadingMode>('clean');
  const [fontSize, setFontSize] = useState<FontSize>('md');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedContribution, setSelectedContribution] = useState<string | null>(null);

  // Compile story text
  const compiledStory = contributions
    .sort((a, b) => a.position - b.position)
    .map(c => c.content)
    .join(' ');

  // Get story statistics
  const stats = {
    wordCount: compiledStory.split(/\s+/).filter(word => word.length > 0).length,
    characterCount: compiledStory.length,
    contributorCount: new Set(contributions.map(c => c.author.id)).size,
    specialActions: contributions.filter(c => c.special_type).length,
  };

  // Font size mapping
  const fontSizeClasses = {
    sm: 'text-sm leading-relaxed',
    md: 'text-base leading-relaxed',
    lg: 'text-lg leading-relaxed',
    xl: 'text-xl leading-relaxed',
  };

  // Handle export functions
  const handleExport = useCallback(async (format: 'txt' | 'pdf' | 'markdown') => {
    try {
      let content = '';
      
      switch (format) {
        case 'txt':
          content = `${storyTitle}\n${'='.repeat(storyTitle.length)}\n\n`;
          if (storyDescription) {
            content += `${storyDescription}\n\n`;
          }
          content += `${compiledStory}\n\n`;
          content += `---\nStats: ${stats.wordCount} words, ${stats.contributorCount} contributors`;
          break;
          
        case 'markdown':
          content = `# ${storyTitle}\n\n`;
          if (storyDescription) {
            content += `*${storyDescription}*\n\n`;
          }
          content += `${compiledStory}\n\n`;
          content += `---\n**Story Statistics:**\n`;
          content += `- Words: ${stats.wordCount}\n`;
          content += `- Contributors: ${stats.contributorCount}\n`;
          content += `- Contributions: ${contributions.length}\n`;
          break;
          
        case 'pdf':
          // For PDF, we'd use a library like jsPDF or trigger a print dialog
          window.print();
          return;
      }

      // Create and download file
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${storyTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: 'Story exported!',
        description: `Successfully exported as ${format.toUpperCase()}`,
      });

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export failed',
        description: 'Please try again later.',
        variant: 'destructive',
      });
    }
  }, [storyTitle, storyDescription, compiledStory, stats, contributions.length, toast]);

  // Handle copy to clipboard
  const handleCopyStory = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(compiledStory);
      toast({
        title: 'Story copied!',
        description: 'The story has been copied to your clipboard.',
      });
    } catch (error) {
      toast({
        title: 'Copy failed',
        description: 'Please select and copy the text manually.',
        variant: 'destructive',
      });
    }
  }, [compiledStory, toast]);

  // Navigation functions
  const goToContribution = useCallback((contributionId: string) => {
    setSelectedContribution(contributionId);
    const element = document.getElementById(`contribution-${contributionId}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);

  const goToPrevious = useCallback(() => {
    if (!selectedContribution) return;
    const currentIndex = contributions.findIndex(c => c.id === selectedContribution);
    if (currentIndex > 0) {
      goToContribution(contributions[currentIndex - 1].id);
    }
  }, [selectedContribution, contributions, goToContribution]);

  const goToNext = useCallback(() => {
    if (!selectedContribution) return;
    const currentIndex = contributions.findIndex(c => c.id === selectedContribution);
    if (currentIndex < contributions.length - 1) {
      goToContribution(contributions[currentIndex + 1].id);
    }
  }, [selectedContribution, contributions, goToContribution]);

  // Render contribution based on reading mode
  const renderContribution = (contribution: Contribution, index: number) => {
    const isSelected = selectedContribution === contribution.id;
    const specialConfig = contribution.special_type ? specialTypeConfig[contribution.special_type] : null;

    switch (readingMode) {
      case 'clean':
        return (
          <span
            key={contribution.id}
            id={`contribution-${contribution.id}`}
            className={cn(
              'cursor-pointer transition-all duration-200',
              isSelected && 'bg-primary/10 px-1 py-0.5 rounded',
              specialConfig && 'font-medium'
            )}
            onClick={() => goToContribution(contribution.id)}
          >
            {contribution.content}
            {index < contributions.length - 1 && ' '}
          </span>
        );

      case 'annotated':
        return (
          <div
            key={contribution.id}
            id={`contribution-${contribution.id}`}
            className={cn(
              'group relative mb-4 p-4 rounded-lg border transition-all duration-200 cursor-pointer',
              isSelected ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50',
              specialConfig && specialConfig.bgColor
            )}
            onClick={() => goToContribution(contribution.id)}
          >
            {specialConfig && (
              <div className="flex items-center gap-2 mb-2">
                <specialConfig.icon className={cn('w-4 h-4', specialConfig.color)} />
                <Badge variant="outline" className="text-xs">
                  {specialConfig.label}
                </Badge>
                {contribution.token_cost && (
                  <Badge variant="secondary" className="text-xs">
                    {contribution.token_cost} tokens
                  </Badge>
                )}
              </div>
            )}
            
            <p className="mb-3">{contribution.content}</p>
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <Avatar className="w-5 h-5">
                  <AvatarImage src={contribution.author.avatar_url} />
                  <AvatarFallback className="text-xs">
                    {contribution.author.username.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <span>{contribution.author.username}</span>
              </div>
              <div className="flex items-center gap-2">
                <span>#{contribution.position}</span>
                <span>{format(new Date(contribution.created_at), 'MMM d, h:mm a')}</span>
              </div>
            </div>
          </div>
        );

      case 'manuscript':
        return (
          <div
            key={contribution.id}
            id={`contribution-${contribution.id}`}
            className={cn(
              'relative cursor-pointer transition-all duration-200',
              isSelected && 'bg-primary/10 px-1 py-0.5 rounded'
            )}
            onClick={() => goToContribution(contribution.id)}
          >
            <span className="mr-1 text-xs text-muted-foreground font-mono">
              [{contribution.position}]
            </span>
            <span className={specialConfig ? 'font-medium' : ''}>
              {contribution.content}
            </span>
            {index < contributions.length - 1 && ' '}
          </div>
        );

      case 'timeline':
        return (
          <div
            key={contribution.id}
            id={`contribution-${contribution.id}`}
            className={cn(
              'flex gap-4 p-4 rounded-lg transition-all duration-200 cursor-pointer',
              isSelected ? 'bg-primary/10 border border-primary' : 'hover:bg-muted/50'
            )}
            onClick={() => goToContribution(contribution.id)}
          >
            <div className="flex-shrink-0">
              <Avatar className="w-8 h-8">
                <AvatarImage src={contribution.author.avatar_url} />
                <AvatarFallback className="text-xs">
                  {contribution.author.username.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">{contribution.author.username}</span>
                <Badge variant="outline" className="text-xs">#{contribution.position}</Badge>
                {specialConfig && (
                  <Badge variant="secondary" className="text-xs">
                    <specialConfig.icon className="w-3 h-3 mr-1" />
                    {specialConfig.label}
                  </Badge>
                )}
                <span className="text-xs text-muted-foreground ml-auto">
                  {format(new Date(contribution.created_at), 'MMM d, h:mm a')}
                </span>
              </div>
              <p className="text-sm">{contribution.content}</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent 
        side="right" 
        className={cn(
          'w-full sm:max-w-2xl lg:max-w-4xl p-0 flex flex-col',
          isFullscreen && 'sm:max-w-full lg:max-w-full',
          className
        )}
      >
        <SheetHeader className="p-6 border-b bg-background/95 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={onBackToThread || onClose}
                className="h-9 w-9"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <SheetTitle className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  {storyTitle}
                </SheetTitle>
                <SheetDescription className="mt-1">
                  {stats.wordCount} words • {stats.contributorCount} contributors • {contributions.length} contributions
                </SheetDescription>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Navigation Controls */}
              {selectedContribution && (
                <div className="flex items-center gap-1 mr-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={goToPrevious}
                    disabled={!selectedContribution || contributions.findIndex(c => c.id === selectedContribution) === 0}
                    className="h-8 w-8"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={goToNext}
                    disabled={!selectedContribution || contributions.findIndex(c => c.id === selectedContribution) === contributions.length - 1}
                    className="h-8 w-8"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* View Controls */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9">
                    <Settings className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Reading Mode</DropdownMenuLabel>
                  {[
                    { value: 'clean', label: 'Clean Text', desc: 'Story only' },
                    { value: 'annotated', label: 'Annotated', desc: 'With details' },
                    { value: 'manuscript', label: 'Manuscript', desc: 'With numbers' },
                    { value: 'timeline', label: 'Timeline', desc: 'Chronological' },
                  ].map((mode) => (
                    <DropdownMenuItem
                      key={mode.value}
                      onClick={() => setReadingMode(mode.value as ReadingMode)}
                      className={readingMode === mode.value ? 'bg-accent' : ''}
                    >
                      <div>
                        <div className="font-medium">{mode.label}</div>
                        <div className="text-xs text-muted-foreground">{mode.desc}</div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                  
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Font Size</DropdownMenuLabel>
                  {[
                    { value: 'sm', label: 'Small' },
                    { value: 'md', label: 'Medium' },
                    { value: 'lg', label: 'Large' },
                    { value: 'xl', label: 'Extra Large' },
                  ].map((size) => (
                    <DropdownMenuItem
                      key={size.value}
                      onClick={() => setFontSize(size.value as FontSize)}
                      className={fontSize === size.value ? 'bg-accent' : ''}
                    >
                      {size.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen Toggle */}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="h-9 w-9"
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9">
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Export Story</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => handleExport('txt')}>
                    <FileText className="w-4 h-4 mr-2" />
                    Export as TXT
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('markdown')}>
                    <FileText className="w-4 h-4 mr-2" />
                    Export as Markdown
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('pdf')}>
                    <Printer className="w-4 h-4 mr-2" />
                    Print / Save as PDF
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleCopyStory}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy to Clipboard
                  </DropdownMenuItem>
                  
                  {onShare && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={onShare}>
                        <Share2 className="w-4 h-4 mr-2" />
                        Share Story
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </SheetHeader>

        {/* Story Navigation Bar */}
        <StoryNavigationBar
          contributions={contributions.map(c => ({
            id: c.id,
            content: c.content,
            author: c.author,
            created_at: c.created_at,
            position: c.position,
            special_type: c.special_type,
            token_cost: c.token_cost,
          }))}
          currentContribution={selectedContribution}
          onNavigateToContribution={goToContribution}
          onCreateBookmark={(position) => {
            toast({
              title: 'Bookmark created!',
              description: `Created bookmark at position ${position}`,
            });
          }}
          onToggleSearch={() => {
            toast({
              title: 'Search coming soon!',
              description: 'Story search functionality is being developed.',
            });
          }}
          showMinimap={true}
        />

        {/* Story Content */}
        <ScrollArea className="flex-1">
          <div
            ref={printRef}
            className={cn(
              'p-6 print:p-8',
              fontSizeClasses[fontSize],
              readingMode === 'clean' && 'max-w-4xl mx-auto',
              readingMode === 'manuscript' && 'font-mono'
            )}
          >
            {/* Story Header for Print */}
            <div className="print:block hidden mb-8">
              <h1 className="text-3xl font-bold mb-2">{storyTitle}</h1>
              {storyDescription && (
                <p className="text-lg text-muted-foreground mb-4">{storyDescription}</p>
              )}
              <div className="text-sm text-muted-foreground border-b pb-4 mb-8">
                {stats.wordCount} words • {stats.contributorCount} contributors • Created {format(new Date(contributions[0]?.created_at || new Date()), 'MMMM d, yyyy')}
              </div>
            </div>

            {/* Story Content */}
            <div className={cn(
              readingMode === 'clean' && 'leading-8',
              readingMode === 'annotated' && 'space-y-4',
              readingMode === 'manuscript' && 'leading-7',
              readingMode === 'timeline' && 'space-y-2'
            )}>
              {contributions
                .sort((a, b) => a.position - b.position)
                .map((contribution, index) => renderContribution(contribution, index))}
            </div>

            {/* Story Footer */}
            <div className="mt-12 pt-8 border-t">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-primary">{stats.wordCount}</div>
                  <div className="text-sm text-muted-foreground">Words</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">{stats.contributorCount}</div>
                  <div className="text-sm text-muted-foreground">Contributors</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">{contributions.length}</div>
                  <div className="text-sm text-muted-foreground">Contributions</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-primary">{stats.specialActions}</div>
                  <div className="text-sm text-muted-foreground">Special Actions</div>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};

export default StoryCompilationView;