    // src/pages/StoryPage.tsx
    import React, { useEffect } from 'react';
    import { useParams } from 'react-router-dom';
    import { useStory } from '@/contexts/StoryContext';
    import { useAuth } from '@/contexts/auth';
    import StoryDisplay from '@/components/StoryDisplay'; // Corrected path
    import StoryContributionInput from '@/components/stories/StoryContributionInput';
    import { Loader2 } from 'lucide-react';
    import { Button } from '@/components/ui/button'; // Added Button import
    import { useToast } from '@/hooks/use-toast'; // Added for ALREADY_COMPLETED toast
    
    const StoryPage: React.FC = () => {
      const { storyId } = useParams<{ storyId: string }>();
      const { currentStory, loadStory, loading: storyLoading, completeStory } = useStory();
      const { user, loading: authLoading } = useAuth();
      const { toast } = useToast(); // For ALREADY_COMPLETED message
    
      useEffect(() => {
        if (storyId) {
          // Ensure loadStory is stable or add to deps if it changes
          loadStory(storyId);
        }
      }, [storyId, loadStory]); // Add loadStory if its identity can change and is a dependency
    
      if (storyLoading || authLoading) {
        return (
          <div className="flex justify-center items-center min-h-screen">
            <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
            <p className="ml-3 text-lg">Loading story...</p>
          </div>
        );
      }
    
      if (!currentStory || currentStory.id !== storyId) {
        return (
          <div className="text-center py-10">
            <p className="text-xl text-red-500">Story not found or access denied.</p>
            {/* Consider adding a Link to go back or to a stories list */}
          </div>
        );
      }
      
      const handleCompleteStory = async () => {
        if (!currentStory) return;
        
        const { error } = await completeStory(currentStory.id);
    
        if (error) {
          // Check if it's our custom error shape with a 'code' property
          // The SupabaseApiError type in context handles this union.
          if (typeof error === 'object' && error !== null && 'code' in error && typeof (error as any).code === 'string') {
            const customError = error as { message: string; code: string; details?: string; hint?: string; }; // Cast to known shape
            if (customError.code === 'ALREADY_COMPLETED') {
              toast({ title: "Info", description: "Story is already complete." });
            } else {
              // Other custom errors from context are already toasted by context's handleError
              console.error("Failed to complete story (custom error code):".concat(customError.code), customError.message);
            }
          } else {
            // Handle Supabase PostgrestError or FunctionsError (or other generic errors)
            // The context's handleError should have already shown a toast with error.message
            console.error("Failed to complete story (Supabase error or other):", (error as Error).message || error);
          }
        } else {
          // Success already toasted from context. UI will update via context state change.
          console.log("Story completion initiated successfully.");
        }
      };
      
      const isAuthor = user?.id === currentStory?.author.id;
    
      return (
        <div className="container mx-auto px-4 py-8 max-w-3xl">
          <StoryDisplay story={currentStory} currentUserId={user?.id} />
          
          {isAuthor && currentStory.status === 'in_progress' && (
            <Button 
              onClick={handleCompleteStory} 
              className="mt-6 w-full bg-green-600 hover:bg-green-700 text-white" // Added text-white for visibility
              disabled={storyLoading} // Disable if overall page is loading
            >
              Mark Story as Complete
            </Button>
          )}
    
          {currentStory.status === 'in_progress' && (
            <StoryContributionInput storyId={currentStory.id} />
          )}
        </div>
      );
    };
    
    export default StoryPage;