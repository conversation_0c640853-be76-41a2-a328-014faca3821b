// Express server for local development
import express from "express";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import axios from "axios";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
app.use(express.json()); // Middleware to parse JSON bodies

// Configure CORS properly
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "http://localhost:5173");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization",
  );
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  res.header("Access-Control-Allow-Credentials", "true");

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    return res.status(204).end();
  }
  next();
});

const PORT = process.env.PORT || 8080;

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, "public")));

// API routes - import dynamically since we're in an ESM file
let createStoryHandler;
try {
  // Import the TypeScript file (Node.js will handle .ts files with ts-node/esm)
  const module = await import("./src/api/create-story.ts");
  createStoryHandler = module.createStory;
} catch (error) {
  console.error("Error importing createStory handler:", error);
  createStoryHandler = (req, res) => {
    res.status(500).json({ 
      error: "Story creation handler not available",
      details: error.message 
    });
  };
}

// Register the API route
app.post("/api/stories", createStoryHandler);

// DeepInfra API proxy to avoid CORS issues
app.post("/api/ai-proxy", async (req, res) => {
  try {
    // Forward the request to DeepInfra
    const response = await axios({
      method: "post",
      url: "https://api.deepinfra.com/v1/inference",
      headers: {
        "Content-Type": "application/json",
        Authorization: req.headers.authorization,
      },
      data: req.body,
      // Don't follow redirects to prevent CORS preflight request issues
      maxRedirects: 0,
    });

    // Return the response data
    res.status(response.status).json(response.data);
  } catch (error) {
    console.error("DeepInfra proxy error:", error.message);

    // Return the error response
    if (error.response) {
      res.status(error.response.status).json({
        error: true,
        message: error.message,
        data: error.response.data,
      });
    } else {
      res.status(500).json({
        error: true,
        message: "Failed to connect to DeepInfra API",
      });
    }
  }
});

// For all other routes, serve index.html (for SPA routing)
app.get("*", (req, res) => {
  const indexPath = path.join(__dirname, "public", "index.html");

  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    // If index.html doesn't exist, create a basic one
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Word by Word Story</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
          }
          h1 {
            color: #333;
          }
          .card {
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            background-color: #f9f9f9;
          }
        </style>
      </head>
      <body>
        <h1>Word by Word Story</h1>
        <div class="card">
          <h2>Development Server</h2>
          <p>This is a simple Express server for local development.</p>
          <p>To see your actual application, you need to build it first with:</p>
          <pre>npm run build</pre>
          <p>Then copy the files from the 'dist' directory to the 'public' directory.</p>
        </div>
      </body>
      </html>
    `;
    res.send(html);
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
  console.log("Press Ctrl+C to stop the server");
});
