# API Documentation

## Overview

The Word by Word Story application uses Supabase as the backend database and provides various API endpoints for managing stories, users, contributions, and subscriptions. This document covers all available API endpoints and their usage.

## Base URL

- **Development**: `http://localhost:5173/api`
- **Production**: `https://word-by-word-story.vercel.app/api`

## Authentication

The application uses Supabase Auth for authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### Authentication Endpoints

#### POST /auth/login
Authenticate a user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "user_metadata": {
      "username": "user123"
    }
  },
  "session": {
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "expires_at": **********
  }
}
```

**Error Response:**
```json
{
  "error": "Invalid credentials",
  "status": 401
}
```

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "newuser",
  "confirm_password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "user_metadata": {
      "username": "newuser"
    }
  },
  "message": "Registration successful. Please check your email for verification."
}
```

#### POST /auth/logout
Log out the current user.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "message": "Logout successful"
}
```

### Stories Endpoints

#### GET /stories
Retrieve a list of stories with optional filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20, max: 100)
- `status` (string): Filter by status ('ACTIVE', 'COMPLETED', 'PAUSED')
- `search` (string): Search in title and description
- `sort` (string): Sort by field ('created_at', 'updated_at', 'title', 'contribution_count')
- `order` (string): Sort order ('asc', 'desc')

**Example Request:**
```
GET /stories?page=1&limit=10&status=ACTIVE&sort=created_at&order=desc
```

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "title": "The Mysterious Journey",
      "description": "A collaborative adventure story",
      "status": "ACTIVE",
      "contribution_mode": "word",
      "words_per_contribution": 1,
      "max_contributions": 100,
      "current_contribution_count": 45,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z",
      "creator": {
        "id": "uuid",
        "username": "storyteller",
        "avatar_url": "https://example.com/avatar.jpg"
      },
      "participants_count": 12,
      "latest_contribution": {
        "text": "adventure",
        "user": {
          "username": "contributor1"
        },
        "created_at": "2024-01-01T11:30:00Z"
      }
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "total_items": 87,
    "items_per_page": 20
  }
}
```

#### GET /stories/:id
Retrieve a specific story with its contributions.

**Path Parameters:**
- `id` (string): Story UUID

**Query Parameters:**
- `include_contributions` (boolean): Include story contributions (default: false)
- `contributions_limit` (number): Limit contributions returned (default: 50)

**Response:**
```json
{
  "id": "uuid",
  "title": "The Mysterious Journey",
  "description": "A collaborative adventure story",
  "status": "ACTIVE",
  "contribution_mode": "word",
  "words_per_contribution": 1,
  "max_contributions": 100,
  "current_contribution_count": 45,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "creator": {
    "id": "uuid",
    "username": "storyteller",
    "avatar_url": "https://example.com/avatar.jpg"
  },
  "contributions": [
    {
      "id": "uuid",
      "text": "Once",
      "position": 1,
      "user": {
        "id": "uuid",
        "username": "user1",
        "avatar_url": "https://example.com/avatar1.jpg"
      },
      "created_at": "2024-01-01T08:00:00Z",
      "is_special": false,
      "special_type": null
    },
    {
      "id": "uuid",
      "text": "upon",
      "position": 2,
      "user": {
        "id": "uuid",
        "username": "user2",
        "avatar_url": "https://example.com/avatar2.jpg"
      },
      "created_at": "2024-01-01T08:15:00Z",
      "is_special": false,
      "special_type": null
    }
  ],
  "can_contribute": true,
  "user_participation": {
    "has_contributed": true,
    "last_contribution_at": "2024-01-01T10:00:00Z",
    "contribution_count": 3
  }
}
```

#### POST /stories
Create a new story.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "My New Story",
  "description": "An exciting collaborative adventure",
  "contribution_mode": "word",
  "words_per_contribution": 1,
  "max_contributions": 200,
  "tags": ["adventure", "fantasy"],
  "is_public": true
}
```

**Response:**
```json
{
  "id": "uuid",
  "title": "My New Story",
  "description": "An exciting collaborative adventure",
  "status": "ACTIVE",
  "contribution_mode": "word",
  "words_per_contribution": 1,
  "max_contributions": 200,
  "current_contribution_count": 0,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "creator": {
    "id": "uuid",
    "username": "creator",
    "avatar_url": "https://example.com/avatar.jpg"
  },
  "participants_count": 1
}
```

#### PUT /stories/:id
Update a story (only by creator or admin).

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Updated Story Title",
  "description": "Updated description",
  "status": "PAUSED"
}
```

#### DELETE /stories/:id
Delete a story (only by creator or admin).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "message": "Story deleted successfully"
}
```

### Contributions Endpoints

#### POST /stories/:id/contributions
Add a contribution to a story.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Path Parameters:**
- `id` (string): Story UUID

**Request Body:**
```json
{
  "text": "adventure",
  "special_type": null
}
```

**For special contributions:**
```json
{
  "text": "REVERSE",
  "special_type": "reverse"
}
```

**Response:**
```json
{
  "id": "uuid",
  "text": "adventure",
  "position": 46,
  "user": {
    "id": "uuid",
    "username": "contributor",
    "avatar_url": "https://example.com/avatar.jpg"
  },
  "created_at": "2024-01-01T12:00:00Z",
  "is_special": false,
  "special_type": null,
  "story": {
    "id": "uuid",
    "current_contribution_count": 46
  }
}
```

#### GET /stories/:id/contributions
Get contributions for a specific story.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 50)
- `since` (string): ISO timestamp to get contributions since

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "text": "adventure",
      "position": 46,
      "user": {
        "id": "uuid",
        "username": "contributor",
        "avatar_url": "https://example.com/avatar.jpg"
      },
      "created_at": "2024-01-01T12:00:00Z",
      "is_special": false,
      "special_type": null
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 2,
    "total_items": 46,
    "items_per_page": 50
  }
}
```

### User Endpoints

#### GET /users/profile
Get current user profile.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "username": "user123",
  "avatar_url": "https://example.com/avatar.jpg",
  "bio": "Passionate storyteller",
  "created_at": "2024-01-01T00:00:00Z",
  "stats": {
    "stories_created": 5,
    "contributions_made": 89,
    "stories_participated": 23
  },
  "subscription": {
    "is_active": true,
    "tier": "premium",
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

#### PUT /users/profile
Update user profile.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "username": "newusername",
  "bio": "Updated bio",
  "avatar_url": "https://example.com/new-avatar.jpg"
}
```

### Subscription Endpoints

#### GET /subscriptions/status
Get current subscription status.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "is_subscribed": true,
  "tier": "premium",
  "status": "active",
  "current_period_start": "2024-01-01T00:00:00Z",
  "current_period_end": "2024-02-01T00:00:00Z",
  "cancel_at_period_end": false,
  "subscription_id": "sub_123",
  "customer_id": "cus_123"
}
```

#### POST /subscriptions/create-checkout-session
Create a Stripe checkout session.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "price_id": "price_monthly_ad_free",
  "success_url": "https://app.com/payment-success",
  "cancel_url": "https://app.com/pricing"
}
```

**Response:**
```json
{
  "url": "https://checkout.stripe.com/pay/cs_123",
  "session_id": "cs_123"
}
```

#### POST /subscriptions/create-portal-session
Create a Stripe customer portal session.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "return_url": "https://app.com/dashboard"
}
```

**Response:**
```json
{
  "url": "https://billing.stripe.com/p/session/123"
}
```

#### POST /subscriptions/verify-payment
Verify payment after checkout success.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "session_id": "cs_123"
}
```

**Response:**
```json
{
  "success": true,
  "subscription": {
    "id": "sub_123",
    "status": "active",
    "current_period_end": "2024-02-01T00:00:00Z"
  }
}
```

### Analytics Endpoints

#### POST /analytics/events
Track user events for analytics.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "event": "story_created",
  "properties": {
    "story_id": "uuid",
    "contribution_mode": "word",
    "is_public": true
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "event_id": "uuid"
}
```

## Error Responses

All API endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "status": 400,
  "details": {
    "field": "Additional error details"
  }
}
```

### Common Error Codes

- `400` - Bad Request: Invalid request data
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `422` - Unprocessable Entity: Validation errors
- `429` - Too Many Requests: Rate limit exceeded
- `500` - Internal Server Error: Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute per IP
- **Story creation**: 10 stories per hour per user
- **Contributions**: 60 contributions per hour per user
- **General endpoints**: 1000 requests per hour per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Webhooks

### Stripe Webhooks

#### POST /webhooks/stripe
Handle Stripe webhook events.

**Events handled:**
- `checkout.session.completed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

### Real-time Updates

The application uses Supabase real-time subscriptions for live updates:

- **Story contributions**: Real-time updates when new contributions are added
- **User presence**: Track active users in stories
- **Typing indicators**: Show when users are typing

## SDKs and Libraries

### JavaScript/TypeScript

```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'your-supabase-url',
  'your-supabase-anon-key'
)

// Get stories
const { data: stories, error } = await supabase
  .from('stories')
  .select('*')
  .eq('status', 'ACTIVE')
  .order('created_at', { ascending: false })

// Add contribution
const { data: contribution, error } = await supabase
  .from('contributions')
  .insert({
    story_id: 'story-uuid',
    text: 'adventure',
    user_id: 'user-uuid'
  })
```

### React Hooks

```typescript
import { useQuery, useMutation } from '@tanstack/react-query'

// Fetch stories
const { data: stories, isLoading } = useQuery({
  queryKey: ['stories'],
  queryFn: () => fetchStories()
})

// Add contribution
const addContribution = useMutation({
  mutationFn: (contribution) => addStoryContribution(contribution),
  onSuccess: () => {
    queryClient.invalidateQueries(['stories'])
  }
})
```

## Testing

### API Testing with curl

```bash
# Get stories
curl -X GET "https://api.example.com/stories?status=ACTIVE" \
  -H "Authorization: Bearer your-jwt-token"

# Create story
curl -X POST "https://api.example.com/stories" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Story",
    "description": "A test story",
    "contribution_mode": "word"
  }'
```

### Postman Collection

A Postman collection is available with pre-configured requests for all endpoints:
[Download Postman Collection](./postman/word-by-word-story.postman_collection.json)

## Changelog

### v1.2.0 (2024-01-15)
- Added subscription management endpoints
- Added analytics tracking
- Enhanced error responses
- Added rate limiting

### v1.1.0 (2024-01-01)
- Added special contributions (gotcha words, reverse)
- Added real-time presence indicators
- Enhanced story filtering options
- Added user statistics

### v1.0.0 (2023-12-01)
- Initial API release
- Basic story and contribution management
- User authentication
- Real-time updates

---

For more information or support, please contact our development team or create an issue in our GitHub repository.