[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [dialog](../README.md) / DialogClose

# Variable: DialogClose

> `const` **DialogClose**: `ForwardRefExoticComponent`\<`DialogCloseProps` & `RefAttributes`\<`HTMLButtonElement`\>\> = `DialogPrimitive.Close`

Defined in: [src/components/ui/dialog.tsx:13](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/dialog.tsx#L13)
