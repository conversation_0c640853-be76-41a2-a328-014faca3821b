import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const validatedLimit = Math.min(Math.max(1, limit), 100);
    const offset = (page - 1) * validatedLimit;

    // First, verify the story exists and is accessible via RLS
    // This helps provide a more accurate 404 if the story itself isn't visible/existent
    const { data: storyData, error: storyError } = await supabase
      .from('stories')
      .select('id')
      .eq('id', storyId)
      .single();

    if (storyError || !storyData) {
      console.error('Story fetch error or not found:', storyError);
      return new Response(JSON.stringify({ error: 'Story not found or access denied.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    const query = supabase
      .from('contributions')
      .select('*', { count: 'exact' })
      .eq('story_id', storyId)
      .order('order', { ascending: true })
      .range(offset, offset + validatedLimit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Supabase contributions query error:', error);
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: error.code && !isNaN(parseInt(error.code)) ? parseInt(error.code) : 500,
      });
    }

    return new Response(
      JSON.stringify({
        data,
        meta: {
          total_items: count || 0,
          current_page: page,
          items_per_page: validatedLimit,
          total_pages: Math.ceil((count || 0) / validatedLimit),
          story_id: storyId
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (err) {
    console.error('Error processing request for list-contributions:', err);
    return new Response(JSON.stringify({ error: err.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 