import React from "react";
import { CreditOptionCard } from "@/components/pricing/CreditOptionCard";
import { Star } from "lucide-react";

interface CoverArtCreditsCardProps {
  onBuy: (type: string, amount: number, cents: number) => void;
  isLoading: boolean;
}

export const CoverArtCreditsCard: React.FC<CoverArtCreditsCardProps> = ({
  onBuy,
  isLoading,
}) => {
  const options = [
    {
      label: "100",
      creditsLabel: "100 credits",
      credits: 100,
      cents: 399,
      price: "$3.99",
    },
    {
      label: "300",
      creditsLabel: "300 credits",
      credits: 300,
      cents: 999,
      price: "$9.99",
    },
    {
      label: "1000",
      creditsLabel: "1000 credits",
      credits: 1000,
      cents: 2999,
      price: "$29.99",
    },
  ];

  return (
    <CreditOptionCard
      icon={<Star className="h-4 w-4 text-literary-gold" />}
      title="Medium Pack"
      description="Great for regular users. Get enough credits for multiple cover art generations and more."
      options={options}
      onBuy={onBuy}
      isLoading={isLoading}
      type="Credits"
    />
  );
};
