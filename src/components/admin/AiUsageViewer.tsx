import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomProgress } from "@/components/ui/custom-progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Loader2, RefreshCw, Search, Trash2 } from "lucide-react";
import aiLogger from "@/utils/ai-logger";

// Import test credits utility for easier testing
import { assignTestUserCredits, createTestUser } from "@/utils/test-credits";

interface AiLogItem {
  id: string;
  type: string;
  userId: string;
  timestamp: string;
  creditsUsed: number;
  tokensUsed: number;
  [key: string]: any;
}

const AiUsageViewer: React.FC = () => {
  const [logs, setLogs] = useState<AiLogItem[]>([]);
  const [summary, setSummary] = useState<any>({});
  const [refreshKey, setRefreshKey] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filterType, setFilterType] = useState<string>("all");

  // Load logs on component mount and whenever refreshKey changes
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      const loadedLogs = aiLogger.getLogs();
      const summary = aiLogger.getUsageSummary();

      setLogs(loadedLogs);
      setSummary(summary);
      setLoading(false);
    }, 300); // Small delay for UI feedback
  }, [refreshKey]);

  // Filter logs by type
  const filteredLogs =
    filterType === "all" ? logs : logs.filter((log) => log.type === filterType);

  // Get unique log types for filter
  const logTypes = Array.from(new Set(logs.map((log) => log.type)));

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return dateString;
    }
  };

  // Refresh logs
  const handleRefresh = () => {
    setRefreshKey((prev) => prev + 1);
  };

  // Clear logs
  const handleClearLogs = () => {
    if (confirm("Are you sure you want to clear all logs?")) {
      aiLogger.clearLogs();
      handleRefresh();
    }
  };

  // Add test credits
  const handleAddTestCredits = () => {
    const success = assignTestUserCredits();
    if (!success) {
      createTestUser();
    }
    alert("Test user created or updated with 100 credits.");
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>AI Usage Logs</span>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearLogs}
              disabled={loading}
            >
              <Trash2 className="h-4 w-4 text-red-500" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddTestCredits}
              disabled={loading}
            >
              Add Test Credits
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          Monitor AI operations, credits, and token usage for testing
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
            <TabsTrigger value="charts">Charts</TabsTrigger>
          </TabsList>
          <TabsContent value="summary" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Operations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {summary.totalOperations || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Total API operations
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Credits Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {summary.totalCreditsUsed || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Total credits consumed
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Tokens Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {summary.totalTokensUsed || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Estimated token usage
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-2">Operations by Type</h3>
              {Object.entries(summary.operationsByType || {}).map(
                ([type, count]: [string, any]) => (
                  <div key={type} className="mb-2">
                    <div className="flex justify-between text-sm mb-1">
                      <span>{type}</span>
                      <span>{count}</span>
                    </div>
                    <CustomProgress
                      value={count}
                      max={summary.totalOperations || 1}
                      className="h-2"
                    />
                  </div>
                ),
              )}
            </div>
          </TabsContent>

          <TabsContent value="logs" className="mt-4">
            <div className="flex justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {logTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="text-sm text-muted-foreground">
                Showing {filteredLogs.length} of {logs.length} logs
              </div>
            </div>

            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[180px]">Timestamp</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead className="text-right">Credits</TableHead>
                    <TableHead className="text-right">Tokens</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                        <div className="mt-2 text-sm text-muted-foreground">
                          Loading logs...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <div className="text-sm text-muted-foreground">
                          No logs found
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLogs
                      .slice()
                      .reverse()
                      .map((log) => (
                        <TableRow key={log.id}>
                          <TableCell className="font-mono text-xs">
                            {formatDate(log.timestamp)}
                          </TableCell>
                          <TableCell>{log.type}</TableCell>
                          <TableCell className="font-mono text-xs">
                            {log.userId?.substring(0, 8)}...
                          </TableCell>
                          <TableCell className="text-right">
                            {log.creditsUsed || 0}
                          </TableCell>
                          <TableCell className="text-right">
                            {log.tokensUsed || 0}
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          <TabsContent value="charts" className="mt-4">
            <div className="text-center py-12 text-muted-foreground">
              Charts coming soon...
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="border-t pt-4 text-xs text-muted-foreground">
        AI usage monitoring for testing purposes only. Data is stored in browser
        localStorage.
      </CardFooter>
    </Card>
  );
};

export default AiUsageViewer;
