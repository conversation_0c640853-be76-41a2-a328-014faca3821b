[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toggle](../README.md) / toggleVariants

# Variable: toggleVariants()

> `const` **toggleVariants**: (`props?`) => `string`

Defined in: [src/components/ui/toggle.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toggle.tsx#L7)

## Parameters

### props?

`ConfigVariants`\<\{ `size`: \{ `default`: `string`; `lg`: `string`; `sm`: `string`; \}; `variant`: \{ `default`: `string`; `outline`: `string`; \}; \}\> & `ClassProp`

## Returns

`string`
