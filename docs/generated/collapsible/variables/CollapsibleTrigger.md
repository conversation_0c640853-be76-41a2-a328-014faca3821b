[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [collapsible](../README.md) / CollapsibleTrigger

# Variable: CollapsibleTrigger

> `const` **CollapsibleTrigger**: `ForwardRefExoticComponent`\<`CollapsibleTriggerProps` & `RefAttributes`\<`HTMLButtonElement`\>\> = `CollapsiblePrimitive.CollapsibleTrigger`

Defined in: [src/components/ui/collapsible.tsx:5](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/collapsible.tsx#L5)
