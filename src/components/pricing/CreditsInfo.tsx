import React from "react";

export const CreditsInfo: React.FC = () => (
  <div className="mt-6 bg-literary-paper p-4 rounded-lg border border-literary-gold/30 text-sm text-literary-navy">
    <h3 className="font-medium mb-2">Credit Usage Guide</h3>
    <p className="mb-2">
      Each feature costs a specific number of credits from your balance. The
      values below are <strong>estimates</strong>. The exact number of credits
      used during generation will be provided prior to generation for your
      approval.
    </p>
    <div className="space-y-2">
      <div className="grid grid-cols-2 gap-2">
        <div className="font-medium">Feature</div>
        <div className="font-medium">Cost (credits)</div>
        <div>Word Suggestion</div>
        <div>4</div>
        <div>Gotcha Word</div>
        <div>4</div>
        <div>Sentence Generation</div>
        <div>15</div>
        <div>Paragraph Generation</div>
        <div>30</div>
        <div>Basic Cover Art</div>
        <div>15</div>
        <div>Premium Cover Art</div>
        <div>30</div>
        <div>Title Generation</div>
        <div>6</div>
        <div>Description Generation</div>
        <div>10</div>
        <div>Story Nudge</div>
        <div>4</div>
      </div>
    </div>
    <p className="mt-4 text-xs text-gray-600">
      All credit purchases are final. Credits do not expire.
    </p>
  </div>
);
