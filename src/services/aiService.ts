/**
 * AI Service for generating titles, story descriptions, sentences, and "gotcha" words
 * This is a mock service for the demo that simulates AI-generated content
 */

/**
 * Generate a title for a story based on a topic or the current content
 * @param topic Optional topic for the title
 * @param storyContent Optional current story content
 * @returns A promise that resolves to an AI-generated title
 */
export const generateMagicTitle = async (topic?: string, storyContent?: string): Promise<string> => {
  // In a real implementation, this would call an AI API
  // For the demo, we'll generate a title based on the topic or content
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // If a topic is provided, use it to generate the title
  if (topic && topic.trim()) {
    const topicWords = topic.trim().split(/\s+/).filter(word => word.length > 0);
    
    // Add some creative flair based on the topic
    const prefixes = ["The", "A Journey into", "Tales of", "Chronicles of", "Beyond the"];
    const middlePhrases = ["World of", "Realm of", "Kingdom of", "Age of", "Legend of"];
    const suffixes = ["Adventure", "Mystery", "Quest", "Journey", "Tale", "Chronicles"];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const middlePhrase = Math.random() > 0.5 ? `${middlePhrases[Math.floor(Math.random() * middlePhrases.length)]} ` : "";
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    // Capitalize topic words
    const formattedTopicWords = topicWords.map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    );
    
    return `${prefix} ${middlePhrase}${formattedTopicWords.join(" ")} ${suffix}`;
  }
  
  // If content is provided, use it to generate the title
  if (storyContent && storyContent.trim()) {
    const contentWords = storyContent.trim().split(/\s+/).filter(word => word.length > 0);
    
    // Use some words from the content for the title
    const titleWords = [];
    const maxWords = Math.min(3, contentWords.length);
    
    // Pick some words from the content
    for (let i = 0; i < maxWords; i++) {
      const randomIndex = Math.floor(Math.random() * contentWords.length);
      const word = contentWords[randomIndex];
      titleWords.push(word.charAt(0).toUpperCase() + word.slice(1));
      contentWords.splice(randomIndex, 1);
    }
    
    // Add some creative flair
    const prefixes = ["The", "A Journey of", "Tales of", "Chronicles of", "Beyond the"];
    const suffixes = ["Adventure", "Mystery", "Quest", "Journey", "Tale", "Chronicles"];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    if (titleWords.length > 0) {
      return `${prefix} ${titleWords.join(" ")} ${suffix}`;
    }
  }
  
  // If no topic or content is provided, generate a random title
  const randomTitles = [
    "The Untold Adventure",
    "Whispers of the Unknown",
    "Beyond the Horizon",
    "Chronicles of Mystery",
    "The Secret Journey",
    "Tales of Wonder",
    "The Lost Expedition",
    "Echoes of Destiny",
    "The Forgotten Path",
    "Legends of the Deep"
  ];
  
  return randomTitles[Math.floor(Math.random() * randomTitles.length)];
};

/**
 * Generate a story description based on a title or topic
 * @param title Optional story title
 * @param topic Optional topic for the description
 * @returns A promise that resolves to an AI-generated story description
 */
export const generateStoryDescription = async (title?: string, topic?: string): Promise<string> => {
  // In a real implementation, this would call an AI API
  // For the demo, we'll generate a description based on the title or topic
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // If a title is provided, use it to generate the description
  if (title && title.trim()) {
    // Extract key words from the title
    const titleWords = title.trim().toLowerCase().split(/\s+/)
      .filter(word => word.length > 3 && !['the', 'and', 'but', 'for', 'from', 'with'].includes(word));
    
    if (titleWords.length > 0) {
      // Pick a random word from the title to use in the description
      const keyWord = titleWords[Math.floor(Math.random() * titleWords.length)];
      
      // Templates that include the key word
      const templates = [
        `A thrilling tale of adventure and discovery in a world where ${keyWord} holds the key to everything.`,
        `When the ${keyWord} appears, nothing will ever be the same for our heroes.`,
        `Journey into the unknown as the mystery of the ${keyWord} unfolds before your eyes.`,
        `In a land where ${keyWord} is both feared and revered, one group dares to uncover the truth.`,
        `The secret of the ${keyWord} has remained hidden for centuries, until now.`
      ];
      
      return templates[Math.floor(Math.random() * templates.length)];
    }
  }
  
  // If a topic is provided, use it to generate the description
  if (topic && topic.trim()) {
    const topicLower = topic.trim().toLowerCase();
    
    // Templates that include the topic
    const templates = [
      `An epic adventure set in a world of ${topicLower}, where danger and wonder await at every turn.`,
      `Discover the secrets of ${topicLower} in this thrilling journey through uncharted territories.`,
      `When ${topicLower} becomes the center of an ancient prophecy, heroes must rise to face the challenge.`,
      `The fate of ${topicLower} hangs in the balance as forces of good and evil clash in an ultimate showdown.`,
      `A tale of courage and friendship, where ${topicLower} brings together unlikely allies in a quest for truth.`
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  }
  
  // If no title or topic is provided, generate a generic description
  const genericDescriptions = [
    "An epic adventure awaits in a world full of mystery and danger. Heroes will rise, villains will fall, and the fate of everything hangs in the balance.",
    "Journey through uncharted territories where ancient secrets lie dormant, waiting to be discovered by those brave enough to seek them.",
    "When darkness threatens to engulf the world, unlikely heroes must band together to face challenges beyond their imagination.",
    "In a realm where magic and reality intertwine, the line between friend and foe blurs as an ancient prophecy begins to unfold.",
    "A tale of courage, friendship, and discovery that will take you to the edges of imagination and back again."
  ];
  
  return genericDescriptions[Math.floor(Math.random() * genericDescriptions.length)];
};

/**
 * Generate a sentence for the story based on the current content or a topic
 * @param storyContent Optional current story content
 * @param topic Optional topic for the sentence
 * @returns A promise that resolves to an AI-generated sentence
 */
export const generateMagicSentence = async (storyContent?: string, topic?: string, gotchaWord?: string): Promise<string> => {
  // In a real implementation, this would call an AI API
  // For the demo, we'll generate a sentence based on the content or topic
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1200));
  
  // If a gotcha word is provided, prioritize using it in the sentence
  if (gotchaWord && gotchaWord.trim()) {
    const word = gotchaWord.trim().toLowerCase();
    
    // Templates that include the gotcha word
    const templates = [
      `The concept of ${word} was something they had only read about in ancient texts.`,
      `"I've always found ${word} to be a fascinating subject," remarked the scholar thoughtfully.`,
      `There was a certain ${word} about the place that made everyone feel uneasy.`,
      `The guide explained that ${word} was central to understanding the local culture.`,
      `She couldn't help but think about the ${word} they had discussed earlier that day.`,
      `The strange ${word} they encountered would forever change their perspective.`,
      `"Do you believe in ${word}?" asked the stranger with a mysterious smile.`,
      `The professor's lecture on ${word} had captivated the entire audience.`,
      `The ancient manuscript described ${word} as the key to unlocking untold mysteries.`,
      `They stood in awe as the manifestation of ${word} appeared before their eyes.`
    ];
    
    // If there's also a topic, try to incorporate both
    if (topic && topic.trim()) {
      const topicLower = topic.trim().toLowerCase();
      const combinedTemplates = [
        `The ${topicLower} exhibited a remarkable ${word} that defied conventional understanding.`,
        `"The relationship between ${topicLower} and ${word} has always been complex," the expert explained.`,
        `As they explored the ${topicLower}, they couldn't ignore the ${word} that permeated the atmosphere.`,
        `The ancient texts described how ${topicLower} and ${word} were inextricably linked throughout history.`,
        `The ${word} of the ${topicLower} was unlike anything they had ever encountered before.`
      ];
      
      // 70% chance to use a template with both topic and gotcha word
      if (Math.random() < 0.7) {
        return combinedTemplates[Math.floor(Math.random() * combinedTemplates.length)];
      }
    }
    
    return templates[Math.floor(Math.random() * templates.length)];
  }
  
  // If only a topic is provided, use it to generate a sentence
  if (topic && topic.trim()) {
    const topicLower = topic.trim().toLowerCase();
    
    // Templates that include the topic
    const templates = [
      `The ${topicLower} appeared suddenly, catching everyone by surprise.`,
      `No one had expected to find ${topicLower} in such a remote location.`,
      `"I've never seen ${topicLower} quite like this before," whispered the guide with awe in their voice.`,
      `The legend of the ${topicLower} had been passed down for generations, but seeing it in person was something else entirely.`,
      `As they approached the ${topicLower}, a sense of both wonder and caution filled the air.`
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  }
  
  // If content is provided, try to generate a contextual continuation
  if (storyContent && storyContent.trim()) {
    // Check if the story has certain keywords to generate more contextual sentences
    const storyLower = storyContent.toLowerCase();
    
    if (storyLower.includes('forest') || storyLower.includes('tree') || storyLower.includes('wood')) {
      const forestSentences = [
        "The ancient trees seemed to whisper secrets as they passed beneath their towering canopies.",
        "A shaft of golden sunlight broke through the dense foliage, illuminating their path forward.",
        "The forest floor was soft underfoot, carpeted with moss and fallen leaves from countless seasons past.",
        "Strange bird calls echoed through the woods, unlike any they had heard before.",
        "The scent of pine and earth filled their lungs with each breath, invigorating their senses."
      ];
      return forestSentences[Math.floor(Math.random() * forestSentences.length)];
    }
    
    if (storyLower.includes('ocean') || storyLower.includes('sea') || storyLower.includes('water') || storyLower.includes('wave')) {
      const oceanSentences = [
        "The waves crashed against the shore with hypnotic rhythm, a timeless dance of water and earth.",
        "Salt spray filled the air as the boat crested another massive wave, sending droplets of seawater across the deck.",
        "The vast expanse of blue stretched to the horizon, hiding countless mysteries beneath its surface.",
        "Seabirds wheeled overhead, their cries barely audible over the constant roar of the ocean.",
        "The water changed from turquoise to deep blue as they ventured further from the shore, a sign of the depths below."
      ];
      return oceanSentences[Math.floor(Math.random() * oceanSentences.length)];
    }
    
    if (storyLower.includes('city') || storyLower.includes('building') || storyLower.includes('street')) {
      const citySentences = [
        "The city skyline glittered with thousands of lights, a constellation of human creation against the night sky.",
        "Crowds moved through the busy streets like currents in a river, each person focused on their own destination.",
        "The ancient architecture stood in stark contrast to the modern glass towers that had sprung up around it.",
        "Street vendors called out their wares, adding their voices to the symphony of urban sounds.",
        "A bell tolled somewhere in the distance, its sound echoing off the stone buildings that lined the narrow streets."
      ];
      return citySentences[Math.floor(Math.random() * citySentences.length)];
    }
  }
  
  // Generic sentences that could fit in most stories
  const genericSentences = [
    "The adventure continued as they ventured deeper into the unknown.",
    "Suddenly, a mysterious figure appeared from the shadows.",
    "The group exchanged nervous glances, unsure of what to do next.",
    "A gentle breeze carried whispers of ancient secrets.",
    "Without warning, the ground beneath them began to tremble.",
    "In that moment, everything changed forever.",
    "The answer had been right in front of them all along.",
    "Little did they know, their journey was just beginning.",
    "As night fell, the true nature of their quest became clear.",
    "Through perseverance and courage, they would overcome any obstacle."
  ];
  
  return genericSentences[Math.floor(Math.random() * genericSentences.length)];
};

/**
 * Interface for the Gotcha word system
 */
export interface GotchaWordState {
  word: string;
  isActive: boolean;
  canBeSkipped: boolean;
  skipsAvailable: number;
  forNextTurn?: boolean; // Flag to indicate this word is for the next turn
}

/**
 * Generate a sentence or phrase that includes a specific gotcha word
 * @param gotchaWord The word that must be included in the output
 * @param context Optional context to help generate a relevant sentence
 * @returns A promise that resolves to a sentence containing the gotcha word
 */
export const generateSentenceWithGotchaWord = async (gotchaWord: string, context?: string): Promise<string> => {
  // In a real implementation, this would call an AI API
  // For the demo, we'll use templates to ensure the gotcha word is included
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const word = gotchaWord.trim().toLowerCase();
  
  // Check if we have context to make the sentence more relevant
  if (context && context.trim()) {
    const contextLower = context.trim().toLowerCase();
    
    // Check for specific themes in the context
    if (contextLower.includes('adventure') || contextLower.includes('journey') || contextLower.includes('quest')) {
      const adventureTemplates = [
        `Their adventure led them to discover the true meaning of ${word}.`,
        `The journey through the wilderness revealed how ${word} could change everything.`,
        `"Every quest requires understanding ${word}," said the wise guide.`,
        `The path ahead would test their knowledge of ${word} and much more.`,
        `They never expected that ${word} would be the key to completing their mission.`
      ];
      return adventureTemplates[Math.floor(Math.random() * adventureTemplates.length)];
    }
    
    if (contextLower.includes('mystery') || contextLower.includes('secret') || contextLower.includes('hidden')) {
      const mysteryTemplates = [
        `The mystery deepened when ${word} was mentioned in the ancient text.`,
        `"The secret lies in understanding ${word}," whispered the hooded figure.`,
        `Hidden within the code was a reference to ${word} that changed everything.`,
        `The detective realized that ${word} was the missing piece of the puzzle.`,
        `The concealed truth about ${word} would soon be revealed to all.`
      ];
      return mysteryTemplates[Math.floor(Math.random() * mysteryTemplates.length)];
    }
    
    if (contextLower.includes('magic') || contextLower.includes('wizard') || contextLower.includes('spell')) {
      const magicTemplates = [
        `The wizard explained that ${word} was essential to casting the spell correctly.`,
        `Magic flowed through the air, carrying the essence of ${word} with it.`,
        `"To master this incantation, you must first understand ${word}," the elder mage instructed.`,
        `The ancient grimoire contained secrets of ${word} that few could comprehend.`,
        `When the spell activated, it created a brilliant display of ${word} that amazed everyone present.`
      ];
      return magicTemplates[Math.floor(Math.random() * magicTemplates.length)];
    }
  }
  
  // Generic templates that can work with any word
  const genericTemplates = [
    `The concept of ${word} was something they had never fully understood until now.`,
    `"I've always found ${word} to be a fascinating subject," remarked the scholar thoughtfully.`,
    `There was a certain ${word} about the place that made everyone feel different.`,
    `The guide explained that ${word} was central to understanding what happened next.`,
    `She couldn't help but think about the ${word} they had discussed earlier that day.`,
    `The strange ${word} they encountered would forever change their perspective.`,
    `"Do you believe in ${word}?" asked the stranger with a mysterious smile.`,
    `The professor's lecture on ${word} had captivated the entire audience.`,
    `The ancient manuscript described ${word} as the key to unlocking untold mysteries.`,
    `They stood in awe as the manifestation of ${word} appeared before their eyes.`
  ];
  
  return genericTemplates[Math.floor(Math.random() * genericTemplates.length)];
};

/**
 * Generate a "gotcha" word that the next contributor must use
 * @param difficulty Optional difficulty level (1-3)
 * @returns A promise that resolves to an AI-generated gotcha word
 */
export const generateGotchaWord = async (difficulty: number = 2): Promise<string> => {
  // In a real implementation, this would call an AI API
  // For the demo, we'll use predefined lists of challenging words by difficulty
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Easy words (difficulty 1)
  const easyGotchaWords = [
    "adventure",
    "mystery",
    "treasure",
    "journey",
    "discover",
    "ancient",
    "magical",
    "secret",
    "whisper",
    "shadow",
    "brilliant",
    "curious",
    "dazzling",
    "enchanted",
    "fantastic"
  ];
  
  // Medium words (difficulty 2)
  const mediumGotchaWords = [
    "serendipity",
    "ephemeral",
    "quintessential",
    "mellifluous",
    "ubiquitous",
    "cacophony",
    "juxtaposition",
    "clandestine",
    "euphoria",
    "perplexity",
    "labyrinthine",
    "enigmatic",
    "eloquent",
    "resilient",
    "whimsical"
  ];
  
  // Hard words (difficulty 3)
  const hardGotchaWords = [
    "perspicacious",
    "surreptitious",
    "ineffable",
    "obfuscation",
    "sesquipedalian",
    "antediluvian",
    "phantasmagorical",
    "verisimilitude",
    "prestidigitation",
    "pulchritudinous",
    "obstreperous",
    "parsimonious",
    "loquacious",
    "supercilious",
    "grandiloquent"
  ];
  
  // Select the appropriate word list based on difficulty
  let wordList: string[];
  switch (difficulty) {
    case 1:
      wordList = easyGotchaWords;
      break;
    case 3:
      wordList = hardGotchaWords;
      break;
    case 2:
    default:
      wordList = mediumGotchaWords;
      break;
  }
  
  return wordList[Math.floor(Math.random() * wordList.length)];
};

/**
 * Initialize a new gotcha word state
 * @param word The gotcha word
 * @param skipsAvailable Number of skips available (default: 1)
 * @returns A new gotcha word state object
 */
export const initGotchaWordState = (word: string, skipsAvailable: number = 1): GotchaWordState => {
  return {
    word,
    isActive: true,
    canBeSkipped: true,
    skipsAvailable
  };
};

/**
 * Use a skip card on the current gotcha word
 * @param state Current gotcha word state
 * @returns Updated gotcha word state
 */
export const useSkipCard = (state: GotchaWordState): GotchaWordState => {
  if (!state.canBeSkipped || state.skipsAvailable <= 0) {
    return state;
  }
  
  return {
    ...state,
    isActive: false,
    skipsAvailable: state.skipsAvailable - 1
  };
};
