import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';

/**
 * Temporary Story component that redirects to the ContributionModesDemo
 */
const Story: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">Story Page</h1>
        <p className="mb-4">
          The Story component is currently being updated to support multiple contribution modes.
        </p>
        <p className="mb-6">
          Please use the Contribution Modes Demo to test the new functionality.
        </p>
        <Link
          to="/contribution-modes-demo"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Go to Contribution Modes Demo
        </Link>
      </div>
    </div>
  );
};

export default Story;
