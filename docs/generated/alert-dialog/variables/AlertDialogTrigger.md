[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [alert-dialog](../README.md) / AlertDialogTrigger

# Variable: AlertDialogTrigger

> `const` **AlertDialogTrigger**: `ForwardRefExoticComponent`\<`AlertDialogTriggerProps` & `RefAttributes`\<`HTMLButtonElement`\>\> = `AlertDialogPrimitive.Trigger`

Defined in: [src/components/ui/alert-dialog.tsx:9](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/alert-dialog.tsx#L9)
