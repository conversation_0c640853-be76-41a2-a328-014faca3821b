import { useAuth } from "@/contexts/auth";
import { BadgeTier } from "@/components/ui/achievement-badge";
import { toast } from "@/components/ui/use-toast";

interface AchievementOptions {
  silent?: boolean; // Don't show toast notifications
}

export function useAchievements() {
  const { user, updateAchievements, addBadge } = useAuth();

  // Update story creation achievements
  const trackStoryCreated = async (options: AchievementOptions = {}) => {
    if (!user || !user.achievements) return;

    const newCount = user.achievements.storiesCreated + 1;
    await updateAchievements({ storiesCreated: newCount });

    // Check if user earned any new badges
    if (
      newCount === 1 &&
      !user.achievements.badges.includes("novice" as <PERSON>geTier)
    ) {
      await addBadge("novice");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Novice Writer",
          description: "You created your first story!",
        });
      }
    }

    if (
      newCount === 5 &&
      !user.achievements.badges.includes("wordsmith" as BadgeTier)
    ) {
      await addBadge("wordsmith");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Wordsmith",
          description: "You've created 5 stories!",
        });
      }
    }

    if (
      newCount === 10 &&
      !user.achievements.badges.includes("storyteller" as BadgeTier)
    ) {
      await addBadge("storyteller");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Storyteller",
          description: "You've created 10 stories!",
        });
      }
    }

    if (
      newCount === 20 &&
      !user.achievements.badges.includes("authors-guild" as BadgeTier)
    ) {
      await addBadge("authors-guild");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Authors Guild",
          description:
            "You've created 20 stories and joined the Authors Guild!",
        });
      }
    }
  };

  // Track story participation
  const trackStoryParticipated = async (options: AchievementOptions = {}) => {
    if (!user || !user.achievements) return;

    const newCount = user.achievements.storiesParticipated + 1;
    await updateAchievements({ storiesParticipated: newCount });

    if (
      newCount === 5 &&
      !user.achievements.badges.includes("apprentice" as BadgeTier)
    ) {
      await addBadge("apprentice");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Apprentice",
          description: "You've participated in 5 stories!",
        });
      }
    }
  };

  // Track word contributions
  const trackWordsContributed = async (
    wordCount: number,
    options: AchievementOptions = {},
  ) => {
    if (!user || !user.achievements) return;

    const newCount = user.achievements.wordsContributed + wordCount;
    await updateAchievements({ wordsContributed: newCount });

    // Check for badge thresholds
    if (
      newCount >= 100 &&
      !user.achievements.badges.includes("wordsmith" as BadgeTier)
    ) {
      await addBadge("wordsmith");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Wordsmith",
          description: "You've contributed 100+ words to stories!",
        });
      }
    }

    if (
      newCount >= 500 &&
      !user.achievements.badges.includes("storyteller" as BadgeTier)
    ) {
      await addBadge("storyteller");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Storyteller",
          description: "You've contributed 500+ words to stories!",
        });
      }
    }

    if (
      newCount >= 1000 &&
      !user.achievements.badges.includes("authors-guild" as BadgeTier)
    ) {
      await addBadge("authors-guild");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Authors Guild",
          description: "You've contributed 1000+ words to stories!",
        });
      }
    }
  };

  // Track votes received on stories
  const trackVotesReceived = async (options: AchievementOptions = {}) => {
    if (!user || !user.achievements) return;

    const newCount = user.achievements.votesReceived + 1;
    await updateAchievements({ votesReceived: newCount });

    if (
      newCount >= 50 &&
      !user.achievements.badges.includes("master" as BadgeTier)
    ) {
      await addBadge("master");
      if (!options.silent) {
        toast({
          title: "Achievement Unlocked: Master Wordsmith",
          description: "Your stories have received 50+ votes!",
        });
      }
    }
  };

  return {
    trackStoryCreated,
    trackStoryParticipated,
    trackWordsContributed,
    trackVotesReceived,
    getUserBadges: () => user?.achievements?.badges || ([] as BadgeTier[]),
  };
}
