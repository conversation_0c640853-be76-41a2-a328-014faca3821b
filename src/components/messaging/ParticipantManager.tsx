import React, { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/auth/hooks';
import { useToast } from '@/hooks/use-toast';
import { InviteData } from './InviteButton';

export interface Participant {
  id: string;
  username: string;
  avatar_url?: string;
  role: 'creator' | 'contributor' | 'viewer';
  isOnline: boolean;
  lastSeen?: Date;
  contributionCount: number;
  joinedAt: Date;
  invitedBy?: string;
  status: 'active' | 'invited' | 'removed';
}

export interface Invitation {
  id: string;
  storyId: string;
  inviterUserId: string;
  inviterUsername: string;
  recipientEmail?: string;
  recipientUsername?: string;
  message: string;
  permissions: 'contributor' | 'viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  inviteCode: string;
  expiresAt: Date;
  createdAt: Date;
}

export interface ParticipantManagerProps {
  storyId: string;
  creatorId: string;
  onParticipantsUpdate?: (participants: Participant[]) => void;
  onInvitationsUpdate?: (invitations: Invitation[]) => void;
}

export const useParticipantManager = ({
  storyId,
  creatorId,
  onParticipantsUpdate,
  onInvitationsUpdate,
}: ParticipantManagerProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Generate unique invite code
  const generateInviteCode = (): string => {
    return `${storyId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  // Send email invitations (mock implementation)
  const sendEmailInvitations = async (emails: string[], message: string, permissions: 'contributor' | 'viewer'): Promise<void> => {
    // In a real implementation, this would integrate with an email service
    console.log('Sending email invitations:', { emails, message, permissions });
    
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // For demo purposes, we'll just create invitation records
    const invitePromises = emails.map(async (email) => {
      const inviteCode = generateInviteCode();
      
      const { error } = await supabase
        .from('story_invitations')
        .insert({
          story_id: storyId,
          inviter_user_id: user?.id,
          recipient_email: email,
          message,
          permissions,
          invite_code: inviteCode,
          status: 'pending',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        });

      if (error) {
        console.error('Error creating invitation:', error);
        throw error;
      }
    });

    await Promise.all(invitePromises);
  };

  // Send username-based invitations
  const sendUsernameInvitations = async (usernames: string[], message: string, permissions: 'contributor' | 'viewer'): Promise<void> => {
    console.log('Sending username invitations:', { usernames, message, permissions });

    const invitePromises = usernames.map(async (username) => {
      // First, check if user exists
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', username)
        .single();

      if (userError || !userData) {
        throw new Error(`User @${username} not found`);
      }

      // Check if user is already a participant
      const { data: existingParticipant } = await supabase
        .from('story_participants')
        .select('id')
        .eq('story_id', storyId)
        .eq('user_id', userData.id)
        .single();

      if (existingParticipant) {
        throw new Error(`@${username} is already a participant`);
      }

      const inviteCode = generateInviteCode();
      
      const { error } = await supabase
        .from('story_invitations')
        .insert({
          story_id: storyId,
          inviter_user_id: user?.id,
          recipient_username: username,
          recipient_user_id: userData.id,
          message,
          permissions,
          invite_code: inviteCode,
          status: 'pending',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        });

      if (error) {
        console.error('Error creating invitation:', error);
        throw error;
      }
    });

    await Promise.all(invitePromises);
  };

  // Process invitations
  const processInvitation = async (inviteData: InviteData): Promise<void> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setIsLoading(true);

    try {
      if (inviteData.type === 'email' && inviteData.recipients) {
        await sendEmailInvitations(inviteData.recipients, inviteData.message || '', inviteData.permissions || 'contributor');
      } else if (inviteData.type === 'username' && inviteData.recipients) {
        await sendUsernameInvitations(inviteData.recipients, inviteData.message || '', inviteData.permissions || 'contributor');
      } else if (inviteData.type === 'link') {
        // For link invitations, just create a generic invitation record
        const inviteCode = generateInviteCode();
        
        const { error } = await supabase
          .from('story_invitations')
          .insert({
            story_id: storyId,
            inviter_user_id: user.id,
            message: inviteData.message || `Join me in this collaborative story!`,
            permissions: inviteData.permissions || 'contributor',
            invite_code: inviteCode,
            status: 'pending',
            expires_at: inviteData.expiresAt?.toISOString() || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          });

        if (error) {
          console.error('Error creating link invitation:', error);
          throw error;
        }
      }

      // Refresh invitations
      await fetchInvitations();

    } catch (error) {
      console.error('Error processing invitation:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Accept invitation (for when someone joins via link)
  const acceptInvitation = async (inviteCode: string): Promise<void> => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Find the invitation
      const { data: invitation, error: inviteError } = await supabase
        .from('story_invitations')
        .select('*')
        .eq('invite_code', inviteCode)
        .eq('status', 'pending')
        .single();

      if (inviteError || !invitation) {
        throw new Error('Invalid or expired invitation');
      }

      // Check if invitation has expired
      if (new Date() > new Date(invitation.expires_at)) {
        throw new Error('Invitation has expired');
      }

      // Check if user is already a participant
      const { data: existingParticipant } = await supabase
        .from('story_participants')
        .select('id')
        .eq('story_id', storyId)
        .eq('user_id', user.id)
        .single();

      if (existingParticipant) {
        throw new Error('You are already a participant in this story');
      }

      // Add user as participant
      const { error: participantError } = await supabase
        .from('story_participants')
        .insert({
          story_id: storyId,
          user_id: user.id,
          role: invitation.permissions,
          joined_at: new Date().toISOString(),
          invited_by: invitation.inviter_user_id,
        });

      if (participantError) {
        throw new Error('Failed to join story');
      }

      // Mark invitation as accepted
      const { error: updateError } = await supabase
        .from('story_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitation.id);

      if (updateError) {
        console.error('Error updating invitation status:', updateError);
      }

      // Refresh participants
      await fetchParticipants();

      toast({
        title: 'Welcome to the story!',
        description: 'You have successfully joined as a ' + invitation.permissions,
      });

    } catch (error) {
      console.error('Error accepting invitation:', error);
      throw error;
    }
  };

  // Remove participant
  const removeParticipant = async (participantId: string): Promise<void> => {
    if (!user || user.id !== creatorId) {
      throw new Error('Only the story creator can remove participants');
    }

    try {
      const { error } = await supabase
        .from('story_participants')
        .delete()
        .eq('story_id', storyId)
        .eq('user_id', participantId);

      if (error) {
        throw new Error('Failed to remove participant');
      }

      await fetchParticipants();

      toast({
        title: 'Participant removed',
        description: 'The participant has been removed from the story.',
      });

    } catch (error) {
      console.error('Error removing participant:', error);
      throw error;
    }
  };

  // Update participant role
  const updateParticipantRole = async (participantId: string, newRole: 'contributor' | 'viewer'): Promise<void> => {
    if (!user || user.id !== creatorId) {
      throw new Error('Only the story creator can update participant roles');
    }

    try {
      const { error } = await supabase
        .from('story_participants')
        .update({ role: newRole })
        .eq('story_id', storyId)
        .eq('user_id', participantId);

      if (error) {
        throw new Error('Failed to update participant role');
      }

      await fetchParticipants();

      toast({
        title: 'Role updated',
        description: `Participant role has been updated to ${newRole}.`,
      });

    } catch (error) {
      console.error('Error updating participant role:', error);
      throw error;
    }
  };

  // Fetch participants
  const fetchParticipants = async (): Promise<void> => {
    try {
      const { data, error } = await supabase
        .from('story_participants')
        .select(`
          *,
          profiles:user_id (
            username,
            avatar_url
          )
        `)
        .eq('story_id', storyId)
        .order('joined_at', { ascending: true });

      if (error) {
        console.error('Error fetching participants:', error);
        return;
      }

      const formattedParticipants: Participant[] = data.map(p => ({
        id: p.user_id,
        username: p.profiles?.username || 'Anonymous',
        avatar_url: p.profiles?.avatar_url,
        role: p.role,
        isOnline: false, // This would be updated by presence system
        contributionCount: 0, // This would be calculated
        joinedAt: new Date(p.joined_at),
        invitedBy: p.invited_by,
        status: 'active',
      }));

      setParticipants(formattedParticipants);
      onParticipantsUpdate?.(formattedParticipants);

    } catch (error) {
      console.error('Error in fetchParticipants:', error);
    }
  };

  // Fetch invitations
  const fetchInvitations = async (): Promise<void> => {
    try {
      const { data, error } = await supabase
        .from('story_invitations')
        .select(`
          *,
          inviter:inviter_user_id (
            username
          )
        `)
        .eq('story_id', storyId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching invitations:', error);
        return;
      }

      const formattedInvitations: Invitation[] = data.map(i => ({
        id: i.id,
        storyId: i.story_id,
        inviterUserId: i.inviter_user_id,
        inviterUsername: i.inviter?.username || 'Unknown',
        recipientEmail: i.recipient_email,
        recipientUsername: i.recipient_username,
        message: i.message,
        permissions: i.permissions,
        status: i.status,
        inviteCode: i.invite_code,
        expiresAt: new Date(i.expires_at),
        createdAt: new Date(i.created_at),
      }));

      setInvitations(formattedInvitations);
      onInvitationsUpdate?.(formattedInvitations);

    } catch (error) {
      console.error('Error in fetchInvitations:', error);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (storyId) {
      fetchParticipants();
      fetchInvitations();
    }
  }, [storyId]);

  return {
    participants,
    invitations,
    isLoading,
    processInvitation,
    acceptInvitation,
    removeParticipant,
    updateParticipantRole,
    fetchParticipants,
    fetchInvitations,
  };
};

export default useParticipantManager; 