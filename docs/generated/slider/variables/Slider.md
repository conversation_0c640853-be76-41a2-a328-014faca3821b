[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [slider](../README.md) / Slider

# Variable: Slider

> `const` **Slider**: `ForwardRefExoticComponent`\<`Omit`\<`SliderProps` & `RefAttributes`\<`HTMLSpanElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLSpanElement`\>\>

Defined in: [src/components/ui/slider.tsx:6](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/slider.tsx#L6)
