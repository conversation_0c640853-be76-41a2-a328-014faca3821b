#!/usr/bin/env node

// Patched Vite script to work around esbuild issues
process.env.ROLLUP_SKIP_NODEJS_BUNDLE = "true";
process.env.ROLLUP_NATIVE_DISABLE = "true";
process.env.ESBUILD_BINARY_PATH =
  process.env.ESBUILD_BINARY_PATH ||
  process.env.npm_config_esbuild_binary_path ||
  "node_modules/.bin/esbuild";

// Monkey patch esbuild to use JS version
try {
  console.log("Starting patched Vite server (bypassing esbuild native)...");

  // Try to import directly from node_modules using relative path
  import("./node_modules/vite/dist/node/index.js")
    .then((vite) => {
      const { createServer } = vite;

      createServer({
        // Force use of esbuild for TS without native binary
        optimizeDeps: {
          esbuildOptions: {
            jsx: "automatic",
          },
        },
        server: {
          port: 5173,
          // Add proxy configuration to forward API requests to Express server
          proxy: {
            "/api": {
              target: "http://localhost:8080",
              changeOrigin: true,
              secure: false,
            },
          },
        },
      })
        .then((server) => {
          console.log("Vite server started on http://localhost:5173/");
          return server.listen(5173);
        })
        .catch((err) => {
          console.error("Failed to start Vite server:", err);
        });
    })
    .catch((err) => {
      console.error("Failed to import Vite:", err);
      // Try an alternative approach using dynamic import with require.resolve
      import("vite")
        .then((vite) => {
          const { createServer } = vite;

          createServer({
            optimizeDeps: {
              esbuildOptions: {
                jsx: "automatic",
              },
            },
            server: {
              port: 5173,
              // Add proxy configuration to forward API requests to Express server
              proxy: {
                "/api": {
                  target: "http://localhost:8080",
                  changeOrigin: true,
                  secure: false,
                },
              },
            },
          })
            .then((server) => {
              console.log("Vite server started on http://localhost:5173/");
              return server.listen(5173);
            })
            .catch((err) => {
              console.error(
                "Failed to start Vite server with alternative approach:",
                err,
              );
            });
        })
        .catch((err) => {
          console.error(
            "Failed to import Vite using alternative approach:",
            err,
          );
        });
    });
} catch (error) {
  console.error("Patched server startup error:", error);
}
