# Deploy & Run

This guide explains how to run and deploy the project across development, preview, and production environments.

---

## Prerequisites

- Node.js installed
- Vercel CLI installed (`npm i -g vercel`)
- Vercel account and project configured

---

## 1. Local Development

Use Vite's development server with hot reloading:

```bash
npm run start:development
```

This spins up the app on `http://localhost:5173` (default) with your local `.env` and HMR.

---

## 2. Local Preview Build

Build in `development` mode and preview the static output:

```bash
npm run start:preview-local
```

This runs:

1. `vite build --mode development`
2. `vite preview` (serves the `dist/` folder)

---

## 3. Local Production Build

Build in production mode and preview the optimized output:

```bash
npm run start:production-local
```

This runs:

1. `vite build`
2. `vite preview` (serves the `dist/` folder)

---

## 4. Setup Vercel Environment Variables

Before deploying API routes or edge functions locally, pull in Vercel secrets/vars:

```bash
npm run setup:vercel-env
```

Then run the Vercel dev server (includes static, `api/`, and `edge-functions/`):

```bash
npx vercel dev
```

---

## 5. Deploy via Vercel CLI

Use these scripts to push directly to Vercel from the CLI.

### Development

```bash
npm run deploy:development
```

### Preview

```bash
npm run deploy:preview
```

### Production

```bash
npm run deploy:production
```

Each script:

1. Generates Prisma client
2. Invokes Vercel with the appropriate flags

---

Now you can test locally in each mode, and deploy seamlessly with one CLI command per environment.
