// Simple Express server for CORS proxy only
const express = require("express");
const axios = require("axios");
const cors = require("cors");

const app = express();
const PORT = 8080;

// Enable JSON parsing and CORS
app.use(express.json());
app.use(
  cors({
    origin: "http://localhost:5173", // Allow Vite dev server
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  }),
);

// Simple health check endpoint
app.get("/api/health", (req, res) => {
  res.json({ status: "ok", message: "CORS proxy server is running" });
});

// DeepInfra API proxy
app.post("/api/ai-proxy", async (req, res) => {
  try {
    console.log("Proxying request to DeepInfra API");

    const response = await axios({
      method: "post",
      url: "https://api.deepinfra.com/v1/inference",
      headers: {
        "Content-Type": "application/json",
        Authorization: req.headers.authorization,
      },
      data: req.body,
      maxRedirects: 0, // Don't follow redirects
    });

    console.log("DeepInfra API proxy successful");
    res.status(response.status).json(response.data);
  } catch (error) {
    console.error("DeepInfra proxy error:", error.message);

    if (error.response) {
      res.status(error.response.status).json({
        error: true,
        message: error.message,
        data: error.response.data,
      });
    } else {
      res.status(500).json({
        error: true,
        message: "Failed to connect to DeepInfra API",
      });
    }
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`CORS proxy server running at http://localhost:${PORT}`);
  console.log("Press Ctrl+C to stop the server");
});
