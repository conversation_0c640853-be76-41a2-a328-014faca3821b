import { supabase } from '@/lib/supabase';

export interface StoryExport {
  version: string;
  exportDate: string;
  story: {
    id: string;
    title: string;
    description: string;
    creatorId: string;
    creatorUsername: string;
    contributionMode: string;
    wordsPerContribution?: number;
    isPublic: boolean;
    status: string;
    createdAt: string;
    updatedAt: string;
  };
  contributions: Array<{
    id: string;
    content: string;
    userId: string;
    username: string;
    position: number;
    createdAt: string;
    metadata?: any;
  }>;
  participants: Array<{
    userId: string;
    username: string;
    joinedAt: string;
    role: string;
    isActive: boolean;
  }>;
  sessions: Array<{
    id: string;
    userId: string;
    username: string;
    startedAt: string;
    endedAt?: string;
    contributionCount: number;
  }>;
  metadata: {
    totalContributions: number;
    totalParticipants: number;
    totalSessions: number;
    wordCount: number;
    characterCount: number;
    averageContributionLength: number;
  };
}

export class StoryExportService {
  /**
   * Export a complete story with all related data
   */
  static async exportStory(storyId: string, userId: string): Promise<StoryExport> {
    try {
      // Get story details
      const { data: story, error: storyError } = await supabase
        .from('stories')
        .select('*')
        .eq('id', storyId)
        .single();

      if (storyError || !story) {
        throw new Error('Story not found or access denied');
      }

      // Verify user has access to this story
      const hasAccess = story.creator_id === userId || story.is_public;
      if (!hasAccess) {
        // Check if user is a participant
        const { data: participation } = await supabase
          .from('story_participants')
          .select('id')
          .eq('story_id', storyId)
          .eq('user_id', userId)
          .single();

        if (!participation) {
          throw new Error('Access denied: You are not authorized to export this story');
        }
      }

      // Get creator username
      const { data: creator } = await supabase
        .from('profiles')
        .select('username')
        .eq('id', story.creator_id)
        .single();

      // Get all contributions with user details
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select(`
          id,
          content,
          user_id,
          position,
          created_at,
          metadata,
          profiles:user_id (username)
        `)
        .eq('story_id', storyId)
        .order('position', { ascending: true });

      if (contributionsError) {
        throw new Error('Failed to fetch contributions');
      }

      // Get all participants
      const { data: participants, error: participantsError } = await supabase
        .from('story_participants')
        .select(`
          user_id,
          joined_at,
          role,
          is_active,
          profiles:user_id (username)
        `)
        .eq('story_id', storyId);

      if (participantsError) {
        throw new Error('Failed to fetch participants');
      }

      // Get session data
      const { data: sessions, error: sessionsError } = await supabase
        .from('story_sessions')
        .select(`
          id,
          user_id,
          started_at,
          ended_at,
          contribution_count,
          profiles:user_id (username)
        `)
        .eq('story_id', storyId)
        .order('started_at', { ascending: true });

      if (sessionsError) {
        throw new Error('Failed to fetch sessions');
      }

      // Calculate metadata
      const fullText = contributions?.map(c => c.content).join(' ') || '';
      const wordCount = fullText.split(/\s+/).filter(word => word.length > 0).length;
      const characterCount = fullText.length;
      const avgContributionLength = contributions?.length 
        ? contributions.reduce((sum, c) => sum + c.content.length, 0) / contributions.length 
        : 0;

      // Format export data
      const exportData: StoryExport = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        story: {
          id: story.id,
          title: story.title,
          description: story.description || '',
          creatorId: story.creator_id,
          creatorUsername: creator?.username || 'Unknown',
          contributionMode: story.contribution_mode,
          wordsPerContribution: story.words_per_contribution,
          isPublic: story.is_public,
          status: story.status,
          createdAt: story.created_at,
          updatedAt: story.updated_at,
        },
        contributions: contributions?.map(c => ({
          id: c.id,
          content: c.content,
          userId: c.user_id,
          username: (c.profiles as any)?.username || 'Unknown',
          position: c.position,
          createdAt: c.created_at,
          metadata: c.metadata,
        })) || [],
        participants: participants?.map(p => ({
          userId: p.user_id,
          username: (p.profiles as any)?.username || 'Unknown',
          joinedAt: p.joined_at,
          role: p.role,
          isActive: p.is_active,
        })) || [],
        sessions: sessions?.map(s => ({
          id: s.id,
          userId: s.user_id,
          username: (s.profiles as any)?.username || 'Unknown',
          startedAt: s.started_at,
          endedAt: s.ended_at,
          contributionCount: s.contribution_count,
        })) || [],
        metadata: {
          totalContributions: contributions?.length || 0,
          totalParticipants: participants?.length || 0,
          totalSessions: sessions?.length || 0,
          wordCount,
          characterCount,
          averageContributionLength: Math.round(avgContributionLength),
        },
      };

      return exportData;

    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  }

  /**
   * Import a story from export data
   */
  static async importStory(
    exportData: StoryExport, 
    userId: string, 
    options: {
      preserveIds?: boolean;
      newTitle?: string;
      makePrivate?: boolean;
    } = {}
  ): Promise<{ storyId: string; success: boolean; warnings: string[] }> {
    const warnings: string[] = [];

    try {
      // Validate export data
      if (!exportData.version || !exportData.story) {
        throw new Error('Invalid export data format');
      }

      // Create new story
      const newStoryData = {
        id: options.preserveIds ? exportData.story.id : undefined,
        title: options.newTitle || `${exportData.story.title} (Imported)`,
        description: exportData.story.description,
        creator_id: userId, // Always set current user as creator
        contribution_mode: exportData.story.contributionMode,
        words_per_contribution: exportData.story.wordsPerContribution,
        is_public: options.makePrivate ? false : exportData.story.isPublic,
        status: 'active', // Always start as active
      };

      const { data: newStory, error: storyError } = await supabase
        .from('stories')
        .insert(newStoryData)
        .select()
        .single();

      if (storyError || !newStory) {
        throw new Error(`Failed to create story: ${storyError?.message}`);
      }

      const newStoryId = newStory.id;

      // Import contributions
      if (exportData.contributions.length > 0) {
        const contributionsToInsert = exportData.contributions.map(c => ({
          id: options.preserveIds ? c.id : undefined,
          story_id: newStoryId,
          user_id: userId, // All contributions attributed to importing user
          content: c.content,
          position: c.position,
          metadata: {
            ...c.metadata,
            originalUserId: c.userId,
            originalUsername: c.username,
            importedAt: new Date().toISOString(),
          },
        }));

        const { error: contributionsError } = await supabase
          .from('contributions')
          .insert(contributionsToInsert);

        if (contributionsError) {
          warnings.push(`Some contributions could not be imported: ${contributionsError.message}`);
        }
      }

      // Add importing user as participant
      const { error: participantError } = await supabase
        .from('story_participants')
        .insert({
          story_id: newStoryId,
          user_id: userId,
          role: 'creator',
          is_active: true,
        });

      if (participantError) {
        warnings.push(`Could not add user as participant: ${participantError.message}`);
      }

      // Create import session record
      const { error: sessionError } = await supabase
        .from('story_sessions')
        .insert({
          story_id: newStoryId,
          user_id: userId,
          started_at: new Date().toISOString(),
          contribution_count: 0,
          session_type: 'import',
        });

      if (sessionError) {
        warnings.push(`Could not create import session: ${sessionError.message}`);
      }

      return {
        storyId: newStoryId,
        success: true,
        warnings,
      };

    } catch (error) {
      console.error('Import error:', error);
      throw error;
    }
  }

  /**
   * Download story export as JSON file
   */
  static downloadStoryExport(exportData: StoryExport, filename?: string): void {
    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `story-${exportData.story.title.replace(/[^a-z0-9]/gi, '-')}-${new Date().toISOString().split('T')[0]}.json`;
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  /**
   * Validate import file
   */
  static validateImportData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.version) {
      errors.push('Missing version information');
    }

    if (!data.story) {
      errors.push('Missing story data');
    } else {
      if (!data.story.title) errors.push('Missing story title');
      if (!data.story.contributionMode) errors.push('Missing contribution mode');
    }

    if (!Array.isArray(data.contributions)) {
      errors.push('Invalid contributions data');
    }

    if (!Array.isArray(data.participants)) {
      errors.push('Invalid participants data');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

export default StoryExportService; 