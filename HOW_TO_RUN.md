# How to Run Word-by-Word Story

This document provides instructions on how to run the application with fixes for placeholder image handling and CORS issues.

## Option 1: Using yarn

1. First, start the Express server with the CORS proxy:

   ```bash
   yarn express-server
   ```

2. Then, in a separate terminal, start the Vite development server:

   ```bash
   yarn start:development
   ```

   The application will be available at: http://localhost:5173

## Option 2: Using provided batch files

1. Open a Windows Command Prompt (not WSL)
2. Start the Express server:

   ```
   run-express.bat
   ```

3. In a separate Command Prompt, start the Vite development server:

   ```
   run-vite-patched.bat
   ```

   The Express server runs on http://localhost:8080 and the development server on http://localhost:5173

## Troubleshooting

### esbuild errors

If you encounter esbuild errors with messages like:

```
The package "@esbuild/win32-x64" could not be found, and is needed by esbuild.
```

Try these solutions:

1. Use the patched vite script:

   ```
   yarn dev:patched
   ```

2. Or use a basic server without esbuild:
   ```
   yarn simple-dev
   ```

### CORS errors

If you encounter CORS errors when using the AI features, make sure the Express server is running. The Express server includes a proxy that forwards requests to the DeepInfra API, avoiding CORS issues.

## Important Notes

- The Express server must be running for the AI features (like "Magic Title") to work properly. This is because it provides a CORS proxy for the DeepInfra API.
- Avatar placeholders have been configured to use the local `/placeholder.svg` file.
