[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toggle-group](../README.md) / ToggleGroup

# Variable: ToggleGroup

> `const` **ToggleGroup**: `ForwardRefExoticComponent`\<(Omit\<ToggleGroupSingleProps & RefAttributes\<HTMLDivElement\>, "ref"\> \| Omit\<ToggleGroupMultipleProps & RefAttributes\<...\>, "ref"\>) & VariantProps\<...\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/toggle-group.tsx:15](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toggle-group.tsx#L15)
