[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [achievement-badge](../README.md) / BadgeList

# Variable: BadgeList

> `const` **BadgeList**: `React.FC`\<\{ `badges`: [`BadgeTier`](../type-aliases/BadgeTier.md)[]; `showLabels?`: `boolean`; `size?`: `"sm"` \| `"md"` \| `"lg"`; \}\>

Defined in: [src/components/ui/achievement-badge.tsx:132](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/achievement-badge.tsx#L132)
