import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

interface StoryUpdateInput {
  title?: string;
  is_public?: boolean;
  status?: 'in_progress' | 'completed' | 'abandoned';
  max_contributors?: number;
  max_words_per_contribution?: number;
  genre?: string;
  cover_image_url?: string;
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) {
    console.error('Auth error or no user:', error);
    throw { message: 'User not authenticated or auth error', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'PUT') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use PUT.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  let supabaseClient: SupabaseClient;

  try {
    const tempSupabase = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false }
    });
    await getAuthenticatedUser(req, tempSupabase); // Ensures user is authenticated

    supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false }
    });

    const url = new URL(req.url);
    const storyId = url.searchParams.get('id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'Story ID is required as a query parameter (e.g., ?id=your-story-id).' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const body: StoryUpdateInput = await req.json();
    const updates: Partial<StoryUpdateInput> & { updated_at?: string } = {}; // Ensure updated_at can be added

    // Validate and build updates object
    if (body.title !== undefined) {
      if (typeof body.title !== 'string' || body.title.trim() === '') {
        return new Response(JSON.stringify({ error: 'Title, if provided, must be a non-empty string.' }), { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
      }
      updates.title = body.title.trim();
    }
    if (body.is_public !== undefined) {
      if (typeof body.is_public !== 'boolean') {
        return new Response(JSON.stringify({ error: 'is_public, if provided, must be a boolean.' }), { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
      }
      updates.is_public = body.is_public;
    }
    if (body.status !== undefined) {
        const validStatuses = ['in_progress', 'completed', 'abandoned'];
        if (!validStatuses.includes(body.status)) {
            return new Response(JSON.stringify({ error: `Status, if provided, must be one of: ${validStatuses.join(', ')}.` }), { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } });
        }
        updates.status = body.status;
    }
    // Add other updatable fields similarly with validation
    if (body.max_contributors !== undefined) updates.max_contributors = body.max_contributors;
    if (body.max_words_per_contribution !== undefined) updates.max_words_per_contribution = body.max_words_per_contribution;
    if (body.genre !== undefined) updates.genre = body.genre;
    if (body.cover_image_url !== undefined) updates.cover_image_url = body.cover_image_url;


    if (Object.keys(updates).length === 0) {
      return new Response(JSON.stringify({ error: 'No update fields provided.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    // The trigger for updated_at should handle this automatically. Explicitly setting might be redundant or even problematic if RLS prevents it.
    // updates.updated_at = new Date().toISOString(); 

    const { data, error } = await supabaseClient
      .from('stories')
      .update(updates)
      .eq('id', storyId)
      .select()
      .single();

    if (error) {
      console.error('Supabase update error:', error);
       // PGRST204 (No content) or PGRST116 (Not found by .single()) might indicate RLS denial or non-existent ID
      if (error.code === 'PGRST204' || error.code === 'PGRST116') { 
        return new Response(JSON.stringify({ error: 'Story not found, or update not permitted.' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404, // Or 403 if distinguishable
        });
      }
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }
    
    if (!data) { // Should be caught by .single() erroring if no row is returned/updated
        return new Response(JSON.stringify({ error: 'Story not found, or update not permitted (no data returned after update).' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        });
    }

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200, // OK
    });

  } catch (err) {
    console.error('Error processing PUT request:', err);
    const status = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status,
    });
  }
}); 