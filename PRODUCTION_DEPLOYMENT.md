# Production Deployment Guide

This guide provides comprehensive instructions for deploying the Word-by-Word Story application to production environments.

## 🚀 Quick Production Deployment

### Prerequisites

- Node.js 18+ installed
- Vercel CLI installed (`npm i -g vercel`)
- Vercel account with project configured
- Supabase project set up
- Google AdSense account (optional)
- <PERSON><PERSON> account for subscriptions
- PostHog account for analytics

### 1. Environment Variables Setup

#### Required Environment Variables

Create these environment variables in your production environment (Vercel dashboard or hosting provider):

```bash
# === SUPABASE CONFIGURATION ===
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# === STRIPE CONFIGURATION ===
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLIC_KEY=pk_live_...
STRIPE_PRICE_ID=price_...
STRIPE_WEBHOOK_SIGNING_SECRET=whsec_...

# === GOOGLE ADSENSE (OPTIONAL) ===
VITE_ADSENSE_CLIENT_ID=ca-pub-...
VITE_ADSENSE_SLOT_HEADER=...
VITE_ADSENSE_SLOT_CONTENT=...
VITE_ADSENSE_SLOT_STORY_END=...
VITE_ADSENSE_SLOT_FOOTER=...
VITE_ADSENSE_SLOT_STORY_SIDEBAR=...

# === POSTHOG ANALYTICS ===
VITE_POSTHOG_KEY=phc_...
VITE_POSTHOG_HOST=https://us.i.posthog.com
VITE_POSTHOG_DISABLE_IN_DEV=true

# === REDIS (OPTIONAL - FOR CACHING) ===
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# === API CONFIGURATION ===
ADMIN_EMAIL=<EMAIL>
NODE_ENV=production
```

### 2. Vercel Deployment

#### Option A: Deploy via CLI

```bash
# Install dependencies
npm install

# Deploy to production
npm run deploy:production
```

#### Option B: Deploy via Git Integration

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### 3. Supabase Configuration

#### Database Setup

1. **Create Supabase Project**:
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note your project URL and anon key

2. **Run Database Migrations**:
   ```bash
   npx supabase db push
   ```

3. **Set up Row Level Security (RLS)**:
   - Enable RLS on all tables
   - Configure policies for user data access

4. **Configure Edge Functions**:
   ```bash
   npx supabase functions deploy
   ```

#### Required Tables

The application requires these database tables:
- `users` - User profiles and subscription status
- `stories` - Story data and metadata
- `contributions` - Individual story contributions
- `api_keys` - API key management
- `api_usage` - API usage tracking
- `subscriptions` - Stripe subscription data

### 4. Stripe Configuration

#### Setup Steps

1. **Create Stripe Account**:
   - Sign up at [stripe.com](https://stripe.com)
   - Complete account verification

2. **Create Products and Prices**:
   ```bash
   # Ad-Free Subscription
   stripe products create --name "Ad-Free Subscription" --description "Remove ads and get premium features"
   stripe prices create --product prod_... --unit-amount 999 --currency usd --recurring interval=month
   ```

3. **Configure Webhooks**:
   - Endpoint: `https://yourdomain.com/api/stripe/webhook`
   - Events: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`

4. **Customer Portal**:
   - Enable in Stripe dashboard
   - Configure allowed operations
   - Set return URL to your domain

### 5. Google AdSense Setup (Optional)

#### Configuration Steps

1. **Create AdSense Account**:
   - Apply at [adsense.google.com](https://adsense.google.com)
   - Get approval for your domain

2. **Create Ad Units**:
   - Header banner (728x90 or responsive)
   - Content ads (300x250 or responsive)
   - Story end ads (300x250)
   - Footer ads (728x90)
   - Sidebar ads (160x600 or 300x250)

3. **Configure Environment Variables**:
   - Set `VITE_ADSENSE_CLIENT_ID` to your publisher ID
   - Set slot IDs for each ad placement

### 6. PostHog Analytics Setup

#### Configuration Steps

1. **Create PostHog Account**:
   - Sign up at [posthog.com](https://posthog.com)
   - Create new project

2. **Configure Environment Variables**:
   ```bash
   VITE_POSTHOG_KEY=phc_your_project_key
   VITE_POSTHOG_HOST=https://us.i.posthog.com
   VITE_POSTHOG_DISABLE_IN_DEV=true
   ```

3. **Set up Events**:
   - User registration
   - Story creation
   - Contribution submission
   - Subscription events

### 7. Domain and SSL Configuration

#### Custom Domain Setup

1. **Add Domain to Vercel**:
   ```bash
   vercel domains add yourdomain.com
   ```

2. **Configure DNS**:
   - Add CNAME record pointing to Vercel
   - SSL certificates are automatically provisioned

3. **Update Environment Variables**:
   - Update Stripe webhook URLs
   - Update Supabase allowed origins
   - Update PostHog allowed domains

### 8. Performance Optimization

#### Caching Strategy

1. **Redis Setup** (Optional):
   - Use Redis for API response caching
   - Configure cache TTL values
   - Set up cache invalidation

2. **CDN Configuration**:
   - Vercel automatically provides CDN
   - Configure cache headers for static assets

#### Database Optimization

1. **Indexing**:
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_stories_user_id ON stories(user_id);
   CREATE INDEX idx_contributions_story_id ON contributions(story_id);
   CREATE INDEX idx_api_usage_key_id_timestamp ON api_usage(key_id, timestamp);
   ```

2. **Connection Pooling**:
   - Use Supabase connection pooler
   - Configure appropriate pool sizes

### 9. Monitoring and Logging

#### Error Tracking

1. **Sentry Integration** (Optional):
   ```bash
   npm install @sentry/react
   ```

2. **PostHog Error Tracking**:
   - Automatic error capture
   - Custom error events

#### Performance Monitoring

1. **Vercel Analytics**:
   - Automatic performance monitoring
   - Core Web Vitals tracking

2. **Custom Metrics**:
   - API response times
   - Database query performance
   - Cache hit rates

### 10. Security Configuration

#### Security Headers

Configure in `vercel.json`:
```json
{
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

#### API Security

1. **Rate Limiting**:
   - Implemented via middleware
   - Different limits per endpoint

2. **Authentication**:
   - JWT token validation
   - API key authentication
   - User ownership verification

### 11. Backup and Recovery

#### Database Backups

1. **Supabase Backups**:
   - Automatic daily backups
   - Point-in-time recovery available

2. **Manual Backups**:
   ```bash
   npx supabase db dump --file backup.sql
   ```

#### Environment Configuration Backup

1. **Export Environment Variables**:
   ```bash
   vercel env pull .env.production
   ```

2. **Store Securely**:
   - Use password manager
   - Document all configurations

### 12. Deployment Checklist

#### Pre-Deployment

- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] Stripe webhooks configured
- [ ] AdSense ads approved
- [ ] Domain DNS configured
- [ ] SSL certificates verified

#### Post-Deployment

- [ ] Test user registration/login
- [ ] Test story creation and collaboration
- [ ] Test subscription flow
- [ ] Test ad display (if enabled)
- [ ] Verify analytics tracking
- [ ] Test API endpoints
- [ ] Check error monitoring
- [ ] Verify performance metrics

### 13. Troubleshooting

#### Common Issues

1. **Environment Variables Not Loading**:
   - Check Vercel dashboard configuration
   - Verify variable names match exactly
   - Restart deployment after changes

2. **Supabase Connection Issues**:
   - Verify URL and keys are correct
   - Check RLS policies
   - Ensure service role key has proper permissions

3. **Stripe Webhook Failures**:
   - Verify webhook URL is accessible
   - Check webhook signing secret
   - Review webhook event logs

4. **AdSense Not Displaying**:
   - Verify domain is approved
   - Check ad slot IDs
   - Ensure ads.txt file is accessible

### 14. Maintenance

#### Regular Tasks

1. **Monitor Performance**:
   - Check Vercel analytics
   - Review PostHog metrics
   - Monitor error rates

2. **Update Dependencies**:
   ```bash
   npm audit
   npm update
   ```

3. **Database Maintenance**:
   - Monitor query performance
   - Review and optimize indexes
   - Clean up old data

4. **Security Updates**:
   - Regular dependency updates
   - Monitor security advisories
   - Review access logs

### 15. Scaling Considerations

#### Traffic Growth

1. **Database Scaling**:
   - Upgrade Supabase plan
   - Implement read replicas
   - Optimize queries

2. **API Scaling**:
   - Implement caching
   - Use CDN for static content
   - Consider serverless functions

3. **Cost Optimization**:
   - Monitor usage metrics
   - Optimize resource allocation
   - Review pricing tiers

---

## 🆘 Support

For deployment issues:

1. Check the troubleshooting section above
2. Review Vercel deployment logs
3. Check Supabase logs and metrics
4. Contact support if needed

## 📚 Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Stripe Documentation](https://stripe.com/docs)
- [Google AdSense Help](https://support.google.com/adsense)
- [PostHog Documentation](https://posthog.com/docs) 