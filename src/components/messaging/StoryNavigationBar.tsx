import React, { useState, use<PERSON>allback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  ChevronLeft,
  ChevronRight,
  SkipBack,
  SkipForward,
  Search,
  Filter,
  Zap,
  RotateCcw,
  Crown,
  User,
  Clock,
  Hash,
  Bookmark,
  BookmarkPlus,
} from 'lucide-react';
import { format } from 'date-fns';

export interface NavigationContribution {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
}

export interface StoryChapter {
  id: string;
  title: string;
  startPosition: number;
  endPosition: number;
  contributionCount: number;
  wordCount: number;
  isBookmarked?: boolean;
}

export interface StoryNavigationBarProps {
  contributions: NavigationContribution[];
  currentContribution?: string;
  chapters?: StoryChapter[];
  onNavigateToContribution: (contributionId: string) => void;
  onNavigateToChapter?: (chapterId: string) => void;
  onCreateBookmark?: (position: number) => void;
  onToggleSearch?: () => void;
  showMinimap?: boolean;
  className?: string;
}

const specialTypeIcons = {
  gotcha: Zap,
  reverse: RotateCcw,
  golden: Crown,
};

const specialTypeColors = {
  gotcha: 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-950',
  reverse: 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-950',
  golden: 'text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-950',
};

export const StoryNavigationBar: React.FC<StoryNavigationBarProps> = ({
  contributions,
  currentContribution,
  chapters = [],
  onNavigateToContribution,
  onNavigateToChapter,
  onCreateBookmark,
  onToggleSearch,
  showMinimap = true,
  className,
}) => {
  const [filterType, setFilterType] = useState<'all' | 'special' | 'author'>('all');
  const [selectedAuthor, setSelectedAuthor] = useState<string | null>(null);

  // Get current position
  const currentIndex = currentContribution 
    ? contributions.findIndex(c => c.id === currentContribution)
    : -1;

  // Get unique authors
  const authors = Array.from(new Set(contributions.map(c => c.author.id)))
    .map(authorId => contributions.find(c => c.author.id === authorId)?.author)
    .filter(Boolean);

  // Filter contributions based on current filter
  const filteredContributions = contributions.filter(contribution => {
    if (filterType === 'special') {
      return contribution.special_type;
    }
    if (filterType === 'author' && selectedAuthor) {
      return contribution.author.id === selectedAuthor;
    }
    return true;
  });

  // Navigation functions
  const goToFirst = useCallback(() => {
    if (contributions.length > 0) {
      onNavigateToContribution(contributions[0].id);
    }
  }, [contributions, onNavigateToContribution]);

  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      onNavigateToContribution(contributions[currentIndex - 1].id);
    }
  }, [currentIndex, contributions, onNavigateToContribution]);

  const goToNext = useCallback(() => {
    if (currentIndex < contributions.length - 1) {
      onNavigateToContribution(contributions[currentIndex + 1].id);
    }
  }, [currentIndex, contributions, onNavigateToContribution]);

  const goToLast = useCallback(() => {
    if (contributions.length > 0) {
      onNavigateToContribution(contributions[contributions.length - 1].id);
    }
  }, [contributions, onNavigateToContribution]);

  // Create bookmark at current position
  const handleCreateBookmark = useCallback(() => {
    if (currentContribution) {
      const contribution = contributions.find(c => c.id === currentContribution);
      if (contribution && onCreateBookmark) {
        onCreateBookmark(contribution.position);
      }
    }
  }, [currentContribution, contributions, onCreateBookmark]);

  return (
    <TooltipProvider>
      <div className={cn(
        'flex items-center gap-2 p-3 border-b bg-background/95 backdrop-blur-sm',
        className
      )}>
        {/* Navigation Controls */}
        <div className="flex items-center gap-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={goToFirst}
                disabled={currentIndex <= 0}
                className="h-8 w-8"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Go to first contribution</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={goToPrevious}
                disabled={currentIndex <= 0}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Previous contribution</TooltipContent>
          </Tooltip>

          <div className="px-3 py-1 text-sm bg-muted rounded-md min-w-[80px] text-center">
            {currentIndex >= 0 ? (
              <span>{currentIndex + 1} / {contributions.length}</span>
            ) : (
              <span>— / {contributions.length}</span>
            )}
          </div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={goToNext}
                disabled={currentIndex >= contributions.length - 1}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Next contribution</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={goToLast}
                disabled={currentIndex >= contributions.length - 1}
                className="h-8 w-8"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Go to last contribution</TooltipContent>
          </Tooltip>
        </div>

        {/* Progress Minimap */}
        {showMinimap && contributions.length > 0 && (
          <div className="flex-1 mx-4">
            <div className="relative h-2 bg-muted rounded-full overflow-hidden">
              {/* Progress segments */}
              {contributions.map((contribution, index) => {
                const width = (1 / contributions.length) * 100;
                const left = (index / contributions.length) * 100;
                const isCurrent = contribution.id === currentContribution;
                
                return (
                  <Tooltip key={contribution.id}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          'absolute h-full cursor-pointer transition-all duration-200 hover:h-3 hover:-mt-0.5',
                          isCurrent 
                            ? 'bg-primary z-10 h-3 -mt-0.5' 
                            : contribution.special_type 
                              ? 'bg-gradient-to-r from-yellow-400 to-amber-500' 
                              : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                        )}
                        style={{
                          left: `${left}%`,
                          width: `${width}%`,
                        }}
                        onClick={() => onNavigateToContribution(contribution.id)}
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-center">
                        <div className="font-medium">#{contribution.position}</div>
                        <div className="text-xs text-muted-foreground">
                          {contribution.author.username}
                        </div>
                        {contribution.special_type && (
                          <Badge variant="secondary" className="text-xs mt-1">
                            {contribution.special_type}
                          </Badge>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </div>
          </div>
        )}

        {/* Chapters Navigation */}
        {chapters.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="gap-2">
                <Bookmark className="h-4 w-4" />
                Chapters
                <Badge variant="secondary" className="text-xs">
                  {chapters.length}
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64">
              <DropdownMenuLabel>Story Chapters</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <ScrollArea className="max-h-64">
                {chapters.map((chapter) => (
                  <DropdownMenuItem
                    key={chapter.id}
                    onClick={() => onNavigateToChapter?.(chapter.id)}
                    className="flex items-center justify-between"
                  >
                    <div className="flex-1">
                      <div className="font-medium">{chapter.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {chapter.contributionCount} contributions • {chapter.wordCount} words
                      </div>
                    </div>
                    {chapter.isBookmarked && (
                      <Bookmark className="h-4 w-4 text-primary fill-current" />
                    )}
                  </DropdownMenuItem>
                ))}
              </ScrollArea>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Filter Controls */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Filter Contributions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem
                onClick={() => setFilterType('all')}
                className={filterType === 'all' ? 'bg-accent' : ''}
              >
                <Hash className="w-4 h-4 mr-2" />
                All Contributions
              </DropdownMenuItem>
              
              <DropdownMenuItem
                onClick={() => setFilterType('special')}
                className={filterType === 'special' ? 'bg-accent' : ''}
              >
                <Zap className="w-4 h-4 mr-2" />
                Special Actions Only
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              <DropdownMenuLabel>Filter by Author</DropdownMenuLabel>
              
              {authors.map((author) => (
                <DropdownMenuItem
                  key={author!.id}
                  onClick={() => {
                    setFilterType('author');
                    setSelectedAuthor(author!.id);
                  }}
                  className={filterType === 'author' && selectedAuthor === author!.id ? 'bg-accent' : ''}
                >
                  <div className="flex items-center gap-2">
                    <Avatar className="w-4 h-4">
                      <AvatarImage src={author!.avatar_url} />
                      <AvatarFallback className="text-xs">
                        {author!.username.slice(0, 1).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span>{author!.username}</span>
                    <Badge variant="outline" className="text-xs ml-auto">
                      {contributions.filter(c => c.author.id === author!.id).length}
                    </Badge>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Search Toggle */}
          {onToggleSearch && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onToggleSearch}
                  className="h-8 w-8"
                >
                  <Search className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Search contributions</TooltipContent>
            </Tooltip>
          )}

          {/* Create Bookmark */}
          {onCreateBookmark && currentContribution && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleCreateBookmark}
                  className="h-8 w-8"
                >
                  <BookmarkPlus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Bookmark current position</TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Current Contribution Info */}
        {currentContribution && (
          <div className="flex items-center gap-2 pl-2 border-l">
            {(() => {
              const contribution = contributions.find(c => c.id === currentContribution);
              if (!contribution) return null;

              const SpecialIcon = contribution.special_type ? specialTypeIcons[contribution.special_type] : null;

              return (
                <>
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={contribution.author.avatar_url} />
                    <AvatarFallback className="text-xs">
                      {contribution.author.username.slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-sm">
                    <div className="flex items-center gap-1">
                      <span className="font-medium">{contribution.author.username}</span>
                      {SpecialIcon && (
                        <div className={cn(
                          'p-1 rounded',
                          contribution.special_type && specialTypeColors[contribution.special_type]
                        )}>
                          <SpecialIcon className="w-3 h-3" />
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(contribution.created_at), 'MMM d, h:mm a')}
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};

export default StoryNavigationBar; 