// <PERSON>ript to patch the Rollup parseAst.js file
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the parseAst.js file
const parseAstPath = path.join(
  __dirname,
  "node_modules",
  "rollup",
  "dist",
  "es",
  "shared",
  "parseAst.js",
);

console.log(`Checking for file: ${parseAstPath}`);

if (fs.existsSync(parseAstPath)) {
  console.log("File found, creating backup...");

  // Create backup
  const backupPath = `${parseAstPath}.backup`;
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(parseAstPath, backupPath);
    console.log(`Backup created at ${backupPath}`);
  }

  // Read the file content
  const content = fs.readFileSync(parseAstPath, "utf8");

  // Replace the problematic import
  const patchedContent = content.replace(
    "import { parse, parseAsync } from '../../native.js';",
    `// Patched import to fix CommonJS/ESM mismatch
import pkg from '../../native.js';
const { parse = () => null, parseAsync = () => Promise.resolve(null) } = pkg || {};`,
  );

  // Write the patched content
  fs.writeFileSync(parseAstPath, patchedContent);
  console.log("File patched successfully!");
} else {
  console.error("File not found. Make sure the path is correct.");
}
