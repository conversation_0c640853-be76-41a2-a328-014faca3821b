describe('Subscription and Payment Flow', () => {
  beforeEach(() => {
    // Mock authentication
    cy.mockSupabaseAuth()
    
    // Mock subscription endpoints
    cy.intercept('GET', '**/subscriptions/status', {
      statusCode: 200,
      body: { isSubscribed: false, tier: 'free' }
    }).as('getSubscriptionStatus')
    
    cy.intercept('POST', '**/subscriptions/create-checkout-session', {
      statusCode: 200,
      body: { 
        url: 'https://checkout.stripe.com/pay/test-session-id',
        sessionId: 'test-session-id'
      }
    }).as('createCheckoutSession')
  })

  describe('Subscription Page', () => {
    it('should display subscription plans correctly', () => {
      cy.visitAndWait('/subscription')
      cy.wait('@getSubscriptionStatus')
      
      cy.contains('Choose Your Ad-Free Plan').should('be.visible')
      cy.get('[data-testid="subscription-plan"]').should('have.length.greaterThan', 0)
      cy.get('[data-testid="plan-price"]').should('be.visible')
      cy.get('[data-testid="plan-features"]').should('be.visible')
    })

    it('should highlight the best value plan', () => {
      cy.visitAndWait('/subscription')
      cy.wait('@getSubscriptionStatus')
      
      cy.get('[data-testid="best-value-badge"]').should('be.visible')
      cy.get('[data-testid="best-value-plan"]').should('have.class', 'border-blue-500')
    })

    it('should start checkout process when plan is selected', () => {
      cy.visitAndWait('/subscription')
      cy.wait('@getSubscriptionStatus')
      
      cy.get('[data-testid="subscribe-button"]').first().click()
      cy.wait('@createCheckoutSession')
      
      // Should show loading state
      cy.get('[data-testid="subscribe-button"]').first().should('contain', 'Redirecting')
    })

    it('should show current subscription status for subscribed users', () => {
      cy.intercept('GET', '**/subscriptions/status', {
        statusCode: 200,
        body: { 
          isSubscribed: true, 
          tier: 'premium',
          subscription: {
            status: 'active',
            current_period_end: '2024-12-31'
          }
        }
      }).as('getActiveSubscription')
      
      cy.visitAndWait('/subscription')
      cy.wait('@getActiveSubscription')
      
      cy.contains('Current Plan').should('be.visible')
      cy.contains('Active').should('be.visible')
      cy.get('[data-testid="manage-subscription-button"]').should('be.visible')
    })

    it('should handle subscription management', () => {
      cy.intercept('POST', '**/subscriptions/create-portal-session', {
        statusCode: 200,
        body: { url: 'https://billing.stripe.com/p/session/test' }
      }).as('createPortalSession')
      
      cy.intercept('GET', '**/subscriptions/status', {
        statusCode: 200,
        body: { isSubscribed: true, tier: 'premium' }
      }).as('getActiveSubscription')
      
      cy.visitAndWait('/subscription')
      cy.wait('@getActiveSubscription')
      
      cy.get('[data-testid="manage-subscription-button"]').click()
      cy.wait('@createPortalSession')
    })
  })

  describe('Payment Success Flow', () => {
    it('should handle successful payment callback', () => {
      cy.intercept('POST', '**/subscriptions/verify-payment', {
        statusCode: 200,
        body: { success: true, subscription: { status: 'active' } }
      }).as('verifyPayment')
      
      cy.visitAndWait('/payment-success?session_id=test-session-id&success=true')
      cy.wait('@verifyPayment')
      
      cy.contains('Payment Successful').should('be.visible')
      cy.contains('Ad-Free Experience Activated').should('be.visible')
      cy.get('[data-testid="continue-button"]').should('be.visible')
    })

    it('should handle payment verification failure', () => {
      cy.intercept('POST', '**/subscriptions/verify-payment', {
        statusCode: 400,
        body: { success: false, error: 'Payment verification failed' }
      }).as('verifyPaymentFailed')
      
      cy.visitAndWait('/payment-success?session_id=invalid-session&success=true')
      cy.wait('@verifyPaymentFailed')
      
      cy.contains('Payment Verification Failed').should('be.visible')
      cy.get('[data-testid="contact-support-button"]').should('be.visible')
    })

    it('should redirect to dashboard after successful payment', () => {
      cy.intercept('POST', '**/subscriptions/verify-payment', {
        statusCode: 200,
        body: { success: true }
      }).as('verifyPayment')
      
      cy.visitAndWait('/payment-success?session_id=test-session-id&success=true')
      cy.wait('@verifyPayment')
      
      cy.get('[data-testid="continue-button"]').click()
      cy.url().should('include', '/dashboard')
    })
  })

  describe('Ad-Free Experience', () => {
    beforeEach(() => {
      // Mock premium user
      cy.intercept('GET', '**/subscriptions/status', {
        statusCode: 200,
        body: { isSubscribed: true, tier: 'premium' }
      }).as('getPremiumStatus')
    })

    it('should not display ads for premium users', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getPremiumStatus')
      
      cy.get('[data-testid="advertisement"]').should('not.exist')
    })

    it('should show premium badge in navigation', () => {
      cy.visitAndWait('/dashboard')
      cy.wait('@getPremiumStatus')
      
      cy.get('[data-testid="premium-badge"]').should('be.visible')
      cy.get('[data-testid="premium-badge"]').should('contain', 'Premium')
    })

    it('should display premium features', () => {
      cy.visitAndWait('/dashboard')
      cy.wait('@getPremiumStatus')
      
      cy.get('[data-testid="premium-features"]').should('be.visible')
      cy.contains('Ad-free experience').should('be.visible')
    })
  })

  describe('Free User Experience', () => {
    it('should display ads for free users', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getSubscriptionStatus')
      
      cy.get('[data-testid="advertisement"]').should('be.visible')
      cy.get('[data-testid="ad-content"]').should('be.visible')
    })

    it('should show upgrade prompts', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getSubscriptionStatus')
      
      cy.get('[data-testid="upgrade-prompt"]').should('be.visible')
      cy.get('[data-testid="remove-ads-button"]').should('be.visible')
    })

    it('should redirect to subscription page when clicking upgrade', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getSubscriptionStatus')
      
      cy.get('[data-testid="remove-ads-button"]').click()
      cy.url().should('include', '/subscription')
    })
  })

  describe('Pricing and Credits', () => {
    it('should display pricing information correctly', () => {
      cy.visitAndWait('/pricing')
      
      cy.contains('Pricing').should('be.visible')
      cy.get('[data-testid="credit-package"]').should('be.visible')
      cy.get('[data-testid="credit-price"]').should('be.visible')
    })

    it('should allow purchasing credits', () => {
      cy.intercept('POST', '**/credits/purchase', {
        statusCode: 200,
        body: { success: true, credits: 100 }
      }).as('purchaseCredits')
      
      cy.visitAndWait('/pricing')
      
      cy.get('[data-testid="buy-credits-button"]').first().click()
      cy.wait('@purchaseCredits')
      
      cy.contains('Credits purchased successfully').should('be.visible')
    })

    it('should display current credit balance', () => {
      cy.intercept('GET', '**/credits/balance', {
        statusCode: 200,
        body: { credits: 50 }
      }).as('getCreditBalance')
      
      cy.visitAndWait('/dashboard')
      cy.wait('@getCreditBalance')
      
      cy.get('[data-testid="credit-balance"]').should('contain', '50')
    })
  })
})