// Comprehensive fix for Rollup and Vite issues
import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("Starting comprehensive fix...");

// Function to execute commands safely
function safeExec(command, options = {}) {
  try {
    return execSync(command, { stdio: "inherit", ...options });
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error.message);
    return null;
  }
}

// Step 1: Check if Express is installed, if not install it
console.log("Checking for Express...");
try {
  await import("express");
  console.log("Express is already installed.");
} catch (error) {
  console.log("Installing Express...");
  safeExec("npm install express --save");
}

// Step 2: Create public directory if it doesn't exist
const publicDir = path.join(process.cwd(), "public");
if (!fs.existsSync(publicDir)) {
  console.log("Creating public directory...");
  fs.mkdirSync(publicDir, { recursive: true });
}

// Step 3: Create a basic index.html in public directory
const indexHtmlPath = path.join(publicDir, "index.html");
if (!fs.existsSync(indexHtmlPath)) {
  console.log("Creating basic index.html...");
  const indexHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Word by Word Story</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    .card {
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 1.5rem;
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <h1>Word by Word Story</h1>
  <div class="card">
    <h2>Welcome!</h2>
    <p>This is a simple static page for the Word by Word Story application.</p>
    <p>You can contribute to stories one word at a time, creating collaborative narratives.</p>
  </div>
</body>
</html>
  `;
  fs.writeFileSync(indexHtmlPath, indexHtml);
}

// Step 4: Update package.json to add express server script
console.log("Updating package.json...");
const packageJsonPath = path.join(process.cwd(), "package.json");
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

// Add express server script
packageJson.scripts = {
  ...packageJson.scripts,
  "express-server": "node express-server.mjs",
};

// Add express as a dependency if not already there
packageJson.dependencies = packageJson.dependencies || {};
if (!packageJson.dependencies.express) {
  packageJson.dependencies.express = "^4.18.2";
}

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

// Step 5: Create Express server file
console.log("Creating Express server file...");
const expressServerContent = `// Express server for local development
import express from 'express';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 8080;

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// For all other routes, serve index.html (for SPA routing)
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, 'public', 'index.html');
  
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    // If index.html doesn't exist, create a basic one
    const html = \`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Word by Word Story</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
          }
          h1 {
            color: #333;
          }
          .card {
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            background-color: #f9f9f9;
          }
        </style>
      </head>
      <body>
        <h1>Word by Word Story</h1>
        <div class="card">
          <h2>Development Server</h2>
          <p>This is a simple Express server for local development.</p>
          <p>To see your actual application, you need to build it first with:</p>
          <pre>npm run build</pre>
          <p>Then copy the files from the 'dist' directory to the 'public' directory.</p>
        </div>
      </body>
      </html>
    \`;
    res.send(html);
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(\`Server running at http://localhost:\${PORT}\`);
  console.log('Press Ctrl+C to stop the server');
});`;

fs.writeFileSync("express-server.mjs", expressServerContent);

// Step 6: Try to fix Rollup issues
console.log("Attempting to fix Rollup issues...");

// Create .npmrc file
const npmrcContent = `rollup-plugin-node-polyfills:resolve=@rollup/plugin-node-polyfills
rollup:resolve=@rollup/rollup-js
`;
fs.writeFileSync(".npmrc", npmrcContent);

// Try to patch the Rollup native.js file if it exists
const nativeJsPath = path.join(
  process.cwd(),
  "node_modules",
  "rollup",
  "dist",
  "native.js",
);
if (fs.existsSync(nativeJsPath)) {
  console.log("Patching Rollup native.js file...");

  // Backup the original file
  const backupPath = `${nativeJsPath}.backup`;
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(nativeJsPath, backupPath);
  }

  // Create patched content
  const patchedContent = `// This is a patched version of the Rollup native.js file
// It forces Rollup to use the JS implementation instead of native modules

'use strict';

// Mock the native module to always return null
function requireWithFriendlyError() {
  return null;
}

// Export a mock native module
const native = null;

// Export the mock
module.exports = native;`;

  // Write patched content
  fs.writeFileSync(nativeJsPath, patchedContent);
  console.log("Rollup native.js patched successfully");
}

// Step 7: Create a run script that tries multiple approaches
console.log("Creating run script...");
const runScriptContent = `@echo off
echo ===================================================
echo Word by Word Story - Development Server
echo ===================================================

echo Trying to start the Express server...
start cmd /k "npm run express-server"

echo.
echo If you want to try running with Vite, open a new command prompt and run:
echo npm run dev
echo.

echo Express server should be running at http://localhost:8080
echo Press Ctrl+C in the other command window to stop the server
`;

fs.writeFileSync("run-local.bat", runScriptContent);
console.log("Created run-local.bat script");

console.log("\nComprehensive fix completed!");
console.log("To run the application, execute:");
console.log("run-local.bat");
console.log("\nThis will start an Express server at http://localhost:8080");
console.log("You can also try running the Vite development server with:");
console.log("npm run dev");
