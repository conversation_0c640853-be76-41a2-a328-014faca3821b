import React from "react";
import { CreditOptionCard } from "@/components/pricing/CreditOptionCard";
import { Sparkles } from "lucide-react";

interface WordSuggestionsCreditsCardProps {
  onBuy: (type: string, amount: number, cents: number) => void;
  isLoading: boolean;
}

export const WordSuggestionsCreditsCard: React.FC<
  WordSuggestionsCreditsCardProps
> = ({ onBuy, isLoading }) => {
  const options = [
    {
      label: "20",
      creditsLabel: "20 credits",
      credits: 20,
      cents: 99,
      price: "$0.99",
    },
    {
      label: "100",
      creditsLabel: "100 credits",
      credits: 100,
      cents: 399,
      price: "$3.99",
    },
    {
      label: "500",
      creditsLabel: "500 credits",
      credits: 500,
      cents: 1499,
      price: "$14.99",
    },
  ];

  return (
    <CreditOptionCard
      icon={<Sparkles className="h-4 w-4 text-literary-gold" />}
      title="Small Pack"
      description="Perfect for trying out AI features. Use credits for word suggestions, sentences, and more."
      options={options}
      onBuy={onBuy}
      isLoading={isLoading}
      type="Credits"
    />
  );
};
