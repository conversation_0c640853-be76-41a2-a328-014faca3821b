/**
 * Permissions Hook
 * React hook for managing user permissions and role-based access control
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth';
import {
  getUserStoryPermissions,
  canUserPerformAction,
  validateContributionPermission,
  validateMessagingPermission,
  getPermissionSummary,
  checkStoryAccess,
  type UserPermissions,
  type UserRole,
} from '@/services/permissionService';

export interface UsePermissionsOptions {
  storyId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface PermissionValidation {
  canContribute: boolean;
  canMessage: boolean;
  contributionReason?: string;
  messagingReason?: string;
}

export interface UsePermissionsReturn {
  // Permission data
  permissions: UserPermissions | null;
  validation: PermissionValidation;
  summary: ReturnType<typeof getPermissionSummary> | null;
  
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Role helpers
  isCreator: boolean;
  isContributor: boolean;
  isViewer: boolean;
  isMuted: boolean;
  isBanned: boolean;
  hasAccess: boolean;
  
  // Action checkers
  canPerform: (action: keyof UserPermissions) => boolean;
  checkContribution: () => Promise<{ canContribute: boolean; reason?: string }>;
  checkMessaging: () => Promise<{ canMessage: boolean; reason?: string }>;
  
  // Data management
  refreshPermissions: () => Promise<void>;
  clearPermissions: () => void;
}

export const usePermissions = (options: UsePermissionsOptions = {}): UsePermissionsReturn => {
  const { user } = useAuth();
  const { storyId, autoRefresh = false, refreshInterval = 30000 } = options;

  // State management
  const [permissions, setPermissions] = useState<UserPermissions | null>(null);
  const [validation, setValidation] = useState<PermissionValidation>({
    canContribute: false,
    canMessage: false,
  });
  const [summary, setSummary] = useState<ReturnType<typeof getPermissionSummary> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasAccess, setHasAccess] = useState(false);

  // Refresh permissions data
  const refreshPermissions = useCallback(async () => {
    if (!storyId || !user?.id) {
      setPermissions(null);
      setValidation({ canContribute: false, canMessage: false });
      setSummary(null);
      setHasAccess(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Check story access first
      const accessResult = await checkStoryAccess(storyId, user.id);
      setHasAccess(accessResult.hasAccess);

      if (!accessResult.hasAccess) {
        setPermissions(null);
        setValidation({ canContribute: false, canMessage: false });
        setSummary(null);
        setError(accessResult.reason || 'Access denied');
        return;
      }

      // Get user permissions
      const userPermissions = await getUserStoryPermissions(storyId, user.id);
      setPermissions(userPermissions);

      if (userPermissions) {
        // Get permission summary
        const permissionSummary = getPermissionSummary(userPermissions);
        setSummary(permissionSummary);

        // Validate specific actions
        const [contributionValidation, messagingValidation] = await Promise.all([
          validateContributionPermission(storyId, user.id),
          validateMessagingPermission(storyId, user.id),
        ]);

        setValidation({
          canContribute: contributionValidation.canContribute,
          canMessage: messagingValidation.canMessage,
          contributionReason: contributionValidation.reason,
          messagingReason: messagingValidation.reason,
        });
      } else {
        setValidation({ canContribute: false, canMessage: false });
        setSummary(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load permissions';
      setError(errorMessage);
      setPermissions(null);
      setValidation({ canContribute: false, canMessage: false });
      setSummary(null);
      setHasAccess(false);
    } finally {
      setIsLoading(false);
    }
  }, [storyId, user?.id]);

  // Clear permissions data
  const clearPermissions = useCallback(() => {
    setPermissions(null);
    setValidation({ canContribute: false, canMessage: false });
    setSummary(null);
    setError(null);
    setHasAccess(false);
  }, []);

  // Check if user can perform specific action
  const canPerform = useCallback((action: keyof UserPermissions): boolean => {
    if (!permissions) return false;
    return permissions[action] as boolean;
  }, [permissions]);

  // Check contribution permission with fresh validation
  const checkContribution = useCallback(async (): Promise<{ canContribute: boolean; reason?: string }> => {
    if (!storyId || !user?.id) {
      return { canContribute: false, reason: 'Not authenticated' };
    }
    return validateContributionPermission(storyId, user.id);
  }, [storyId, user?.id]);

  // Check messaging permission with fresh validation
  const checkMessaging = useCallback(async (): Promise<{ canMessage: boolean; reason?: string }> => {
    if (!storyId || !user?.id) {
      return { canMessage: false, reason: 'Not authenticated' };
    }
    return validateMessagingPermission(storyId, user.id);
  }, [storyId, user?.id]);

  // Role helper properties
  const isCreator = permissions?.isCreator || false;
  const isContributor = permissions?.role === 'contributor';
  const isViewer = permissions?.role === 'viewer';
  const isMuted = permissions?.isMuted || false;
  const isBanned = permissions?.isBanned || false;

  // Load permissions when dependencies change
  useEffect(() => {
    refreshPermissions();
  }, [refreshPermissions]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !storyId || !user?.id) return;

    const interval = setInterval(refreshPermissions, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshPermissions, storyId, user?.id]);

  // Clear permissions when user logs out
  useEffect(() => {
    if (!user) {
      clearPermissions();
    }
  }, [user, clearPermissions]);

  return {
    // Permission data
    permissions,
    validation,
    summary,
    
    // Loading states
    isLoading,
    error,
    
    // Role helpers
    isCreator,
    isContributor,
    isViewer,
    isMuted,
    isBanned,
    hasAccess,
    
    // Action checkers
    canPerform,
    checkContribution,
    checkMessaging,
    
    // Data management
    refreshPermissions,
    clearPermissions,
  };
};

// Specialized hooks for common use cases

/**
 * Hook for contribution permissions
 */
export const useContributionPermissions = (storyId?: string) => {
  const { permissions, validation, checkContribution, refreshPermissions } = usePermissions({ storyId });
  
  return {
    canContribute: validation.canContribute,
    contributionReason: validation.contributionReason,
    isViewer: permissions?.role === 'viewer',
    isMuted: permissions?.isMuted || false,
    checkContribution,
    refreshPermissions,
  };
};

/**
 * Hook for messaging permissions
 */
export const useMessagingPermissions = (storyId?: string) => {
  const { permissions, validation, checkMessaging, refreshPermissions } = usePermissions({ storyId });
  
  return {
    canMessage: validation.canMessage,
    canReact: permissions?.canReactToMessages || false,
    messagingReason: validation.messagingReason,
    isViewer: permissions?.role === 'viewer',
    isMuted: permissions?.isMuted || false,
    checkMessaging,
    refreshPermissions,
  };
};

/**
 * Hook for creator permissions
 */
export const useCreatorPermissions = (storyId?: string) => {
  const { permissions, canPerform, refreshPermissions } = usePermissions({ storyId });
  
  return {
    isCreator: permissions?.isCreator || false,
    canManageParticipants: canPerform('canManageParticipants'),
    canCreateInvitations: canPerform('canCreateInvitations'),
    canEditStoryDetails: canPerform('canEditStoryDetails'),
    canDeleteStory: canPerform('canDeleteStory'),
    canModerateContent: canPerform('canModerateContent'),
    refreshPermissions,
  };
};

export default usePermissions;