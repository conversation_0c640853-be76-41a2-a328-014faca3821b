import React from "react";
import {
  <PERSON>,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

export const CreditsCard: React.FC<{ credits: number }> = ({ credits }) => (
  <div className="mb-8 flex justify-center">
    <Card className="w-full max-w-md border-literary-gold/30 shadow-md">
      <CardHeader className="pb-3 border-b border-gray-100">
        <CardTitle className="flex items-center justify-between">
          <span className="text-literary-navy text-xl">Your Credits</span>
          <span className="text-literary-gold font-bold text-3xl">
            {credits}
          </span>
        </CardTitle>
        <CardDescription className="text-gray-600">
          Use credits for AI features and special story elements
        </CardDescription>
      </CardHeader>
    </Card>
  </div>
);
